import React, { useState, useEffect, useMemo } from "react";
import axios from "axios";
import PageContainer from "../common/PageContainer";
import ReportFilter from "../common/ReportFilter";
import ReportPagination from "../common/ReportPagination";
import SortableTableHeader from "../common/SortableTableHeader";
import { sortArrayByKey } from "../../utils/sortUtils";
import * as XLSX from "xlsx";
import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import Swal from "sweetalert2";
import Modal from "../common/Modal";
import { formatUSD } from "./Helpers/CurrencyFormat.js";
import "../common/Modal.css";
import "react-datepicker/dist/react-datepicker.css";
import DatePicker from "react-datepicker";
import ModalContent from "./ModalContents";
import "./invoice.css";
import {
  getCurrentUserInvoice,ENDPOINTS,MERCHANT_ID,STATUS_MAP_FULL,ACTIONS_MAP,STATUS_IDS,ACTION_IDS,MODAL_SIZES,
  DEFAULT_INVOICE_DATE_RANGE_DAYS,DEFAULT_PAGE_SIZE,SPECIAL_USER_IDS,ALLOWED_ROLES_FOR_SHARE_LINK,
} from "./invoice-settings";
import { Link } from "react-router-dom";
import { handlePauseReminderAction, handleResumeReminderAction } from "./invoiceSwalActions";

const InvoiceReport = () => {

  const DEFAULT_DATE_RANGE_DAYS = DEFAULT_INVOICE_DATE_RANGE_DAYS ?? 15;
  // Centralized date range generator

  const formatDateUS = (date) =>
    date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });

  const getDefaultDateRange = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const startDate = new Date(today);
    startDate.setDate(today.getDate() - (DEFAULT_DATE_RANGE_DAYS - 1));
    return {
      startDate: formatDateUS(startDate), // e.g., 07/28/2025
      endDate: formatDateUS(today)        // e.g., 08/01/2025
    };
  };

  // tooltip map used by tabs and PageContainer
  const TAB_TOOLTIP = {
    all: 'Invoices created within the selected date range.',
    unpaid: 'Invoices with status "Unpaid" created within the date range.',
    in_process: 'Invoices with status "Payment In Process" invoiced within the date range.',
    partially_paid: 'Invoices with status "Partially Paid" partially paid within the date range.',
    paid: 'Invoices with status "Paid" fully paid within the date range.',
    overdue: 'Invoices with status "Overdue" and due within the date range.',
  };

  const [tabHoverText, setTabHoverText] = useState('');

  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");

  const defaultRange = getDefaultDateRange();
  const [startDate, setStartDate] = useState(defaultRange.startDate);
  const [endDate, setEndDate] = useState(defaultRange.endDate);

  const [currentPage, setCurrentPage] = useState(1);
  //--=Tab filter=--
  const [activeTab, setActiveTab] = useState('all');
  const HIDDEN_UI_COLUMNS = ["billing_profile_name", "lead_group"];
  //--=Tab filter=--
  //--== Processing action handler ==--

  //--== Processing action handler ==--
  const [itemsPerPage, setItemsPerPage] = useState(DEFAULT_PAGE_SIZE);
  const [sortField, setSortField] = useState("invoice_date");
  const [sortDirection, setSortDirection] = useState("desc");
  const [isSearching, setIsSearching] = useState(false);
  const merchantId = MERCHANT_ID;
  const [actionSelections, setActionSelections] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [modalData, setModalData] = useState({
    title: "",
    content: "",
    actionType: "",
    invoiceId: null,
    invoiceDate: "",
    invoiceAmount: "",
  });
  const [userRoles, setUserRoles] = useState([]);
  const [hoverTooltipText, setHoverTooltipText] = useState("");
// NEW: open partial-paid details modal from Amount click
  const handleAmountClick = (inv) => {
    const statusId = parseInt(inv?.status_id, 10);
    if (statusId !== STATUS_IDS.PARTIALLY_PAID) return; // Only for Partially Paid

    setModalData({
      title: `Partial Payment Details - Invoice #${inv?.customerInvoiceNo || inv?.customer_invoice_no || 'N/A'}`,
      actionType: ACTION_IDS.PP_INFO, // this routes to PartialPaid content in the common modal
      invoiceId: inv?.invoice_id || "",
      invoiceAmount: isNaN(parseFloat(inv?.total_amount))
        ? "0.00"
        : parseFloat(inv.total_amount).toFixed(2),
      fetchInvoices,
      onClose: () => setShowModal(false),
    });

    setShowModal(true);
  };
  const refreshWithActiveFilters = () => {
    if (!startDate && !endDate) {
      // All-time case handled here
      return fetchAllInvoices();
    }
    return fetchInvoices(startDate, endDate);
  };
const handleUpdateInterest = async (invoiceId, customerInvoiceNo) => {
  const cu = getCurrentUserInvoice() || {};
  const userId = cu.user_id ?? cu.id ?? null;

  const confirm = await Swal.fire({
    icon: "warning",
    title: "Are you sure?",
    html: `Are you sure you want to update interest for invoice <strong>#${customerInvoiceNo}</strong>?`,
    showCancelButton: true,
    confirmButtonText: "Yes, update it",
    cancelButtonText: "No, cancel",
    confirmButtonColor: "#007bff",
    cancelButtonColor: "#dc3545",
    customClass: { popup: "swal-wide" },
    allowOutsideClick: false,
    showLoaderOnConfirm: true, // spinner दिखेगा
    preConfirm: async () => {
      try {
        const { data } = await axios.post(ENDPOINTS.SEND_ADD_INTEREST, {
          invoice_id: invoiceId,
          user_id: userId,
        });
        // success हो या fail — दोनों में resolve करें
        return data; // e.g. {result:"success"|"fail", msg:"..."}
      } catch (err) {
        return { result: "fail", msg: err?.message || "Interest update failed." };
      }
    },
  });

  // Cancel किया तो कुछ नहीं
  if (!confirm.isConfirmed) return;

  const r = confirm.value || {};

  if (r.result === "success") {
    Swal.fire({
      icon: "success",
      title: "Interest Updated!",
      text: r.msg || "Interest updated successfully.",
      confirmButtonColor: "#7C5CFC",
    });
  } else {
    Swal.fire({
      icon: "error",
      title: "Failed!",
      text: r.msg || "Something went wrong.",
      confirmButtonText: "OK",
      confirmButtonColor: "#7C5CFC",
    });
  }
};


  const handleActionChange = (invoiceId, newValue, inv) => {
    if (newValue === "Select") {
      setActionSelections((prev) => ({
        ...prev,
        [invoiceId]: newValue,
      }));
      return;
    }

    // Handle Share Invoice Link action: copy link and show SweetAlert, do NOT open modal
    if (newValue === ACTION_IDS.SHARE_INVOICE_LINK) {
      // Check for either invoice_url or other_payment_link
      const paymentLink = inv?.invoice_url || inv?.other_payment_link;

      if (paymentLink) {
        // Try modern clipboard API
        if (navigator.clipboard && window.isSecureContext) {
          navigator.clipboard.writeText(paymentLink);
        } else {
          // Fallback method for insecure contexts or older browsers
          const textArea = document.createElement("textarea");
          textArea.value = paymentLink;
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          document.execCommand("copy");
          document.body.removeChild(textArea);
        }

        // Show success alert
        Swal.fire({
          icon: "success",
          title: "Copied!",
          text: "Payment link copied to clipboard",
          confirmButtonText: "OK",
          confirmButtonColor: "#7C5CFC",
          allowOutsideClick: false,
          allowEscapeKey: true,
        });
      }

      // Do NOT open the modal
      return;
    }

    
    // Handle Update Interest action: show SweetAlert, do NOT open modal
    // Update Interest: Show SweetAlert instead of modal
    if (newValue === ACTION_IDS.UPDATE_INTEREST) {
      handleUpdateInterest(invoiceId, inv?.customerInvoiceNo || "N/A");
      return;
    }
    if (newValue === ACTION_IDS.CANCEL_AUTO_INV_REMINDER) {
      handlePauseReminderAction(inv, fetchInvoices);
      return;
    }
    if (newValue === ACTION_IDS.RESUME_AUTO_INV_REMINDER) {
      handleResumeReminderAction(inv, fetchInvoices);
      return;
    }
    setActionSelections((prev) => ({
      ...prev,
      [invoiceId]: newValue,
    }));
    const actionText = ACTIONS_MAP[newValue]?.text || newValue;

    const modalPayload = {
      title: `${
        ACTIONS_MAP[newValue]?.text || "Action"
      } - Invoice #${inv?.customerInvoiceNo}`,
      actionType: newValue || "",
      invoiceId: invoiceId || "",
      customerInvoiceNo: inv?.customerInvoiceNo || "N/A",
      invoiceDate: inv?.invoice_date || "N/A",
      invoiceAmount: isNaN(parseFloat(inv?.total_amount))
        ? "0.00"
        : parseFloat(inv.total_amount).toFixed(2),
      invoiceUrl: inv?.invoice_url || "",
      businessName: inv?.business_name || "N/A",
      customerName: inv?.customer_name || "N/A",
      userEmail: inv?.user_email || "",
      statusId: inv?.status_id || "",
      productTitle: inv?.product_title || "N/A",
      dueDate: inv?.due_date || "N/A",
      daysDue:
        typeof inv?.days_due === "number" || !isNaN(inv?.days_due)
          ? parseInt(inv.days_due, 10)
          : "N/A",
      billingProfile: inv?.billing_profile || "",
      displayName: inv?.display_name || "N/A",
      category: inv?.category || "",
      leadGroup: inv?.lead_group || "",
      phoneNo: inv?.phone_no || "",
      address: inv?.address || "",
      zip: inv?.zip || "",
      country: inv?.country || "",
      state: inv?.state || "",
      ccEmail: typeof inv?.cc_email === "string" ? inv.cc_email : "",
      bccEmail: typeof inv?.bcc_email === "string" ? inv.bcc_email : "",
      noteToRecipient: inv?.note_to_recipient || "",
      termsConditions: inv?.terms_conditions || "",
      //paymentPlanStatus: inv?.payment_plan_status || "None",
    };

    //console.log("modalData set with the following values:", modalPayload); // <--- LOGGING HERE
    const resetActionSelection = () => {
      setActionSelections(prev => ({ ...prev, [invoiceId]: "Select" }));
    };

    setModalData({
      resetActionSelection,
      ...modalPayload,
      fetchInvoices: refreshWithActiveFilters,
      onClose: () => setShowModal(false),
    });
    
    setShowModal(true);
  };
  const handleCloseModal = () => {
    setShowModal(false);
    setModalData({
      title: "",
      content: "",
      actionType: "",
      invoiceId: null,
      invoiceDate: "",
      invoiceAmount: "",
    });
  };

  const currentUser = getCurrentUserInvoice(); // This gives user_id
  const currentUserId = currentUser?.user_id || null;

  const ActionDropdown = ({ inv }) => {
    const actionList = (inv.action || "").split(",").map((a) => a.trim()).filter(Boolean);

    const hasActions = actionList.length > 0;
    if (!hasActions) {
      return (
        <select className="form-select action-disabled-invoice" disabled>
          <option>No Action</option>
        </select>
      );
    }  

    return (
      <select
        className="form-select"
        value={actionSelections[inv.invoice_id] || "Select"}
        onChange={(e) => handleActionChange(inv.invoice_id, e.target.value, inv)}
      >
        <option value="Select">Select Action</option>
        {(inv.action || "")
          .split(",")
          .map((act, idx) => {
            const val = act.trim();
            if (!val) return null;

            // Condition: Hide action '13' for all except user_id = 45117
            if (val === String(ACTION_IDS.DELETE) && currentUserId !== SPECIAL_USER_IDS.CAN_SEE_DELETE) {
              return null; // Skip rendering this option
            }
            // Restrict share_invoice_link to specific roles
           if ( val === String(ACTION_IDS.SHARE_INVOICE_LINK) && !userRoles.some(role => ALLOWED_ROLES_FOR_SHARE_LINK.includes(role)) ) {
              return null;
            }

            let label = "";
            let value = val;

            const parsedId = parseInt(val, 10);

            //Custom label for action 13 (regardless of ACTIONS_MAP)
            if (val === String(ACTION_IDS.DELETE)) {
              label = "Delete";
            } else if (!isNaN(parsedId) && ACTIONS_MAP[parsedId]) {
              label = ACTIONS_MAP[parsedId].text;
              value = parsedId;
            } else if (ACTIONS_MAP[val]) {
              label = ACTIONS_MAP[val].text;
            } else {
              label = val
                .replace(/_/g, " ")
                .replace(/\b\w/g, (char) => char.toUpperCase());
            }

            return (
              <option key={idx} value={value}>
                {label}
              </option>
            );
          })}
      </select>
    );
  };

  const statusMap = STATUS_MAP_FULL;
  const actionsMap = ACTIONS_MAP;

  // utility function for view/edit
  const renderViewEditLinks = (row) => {

    const canEdit = row.can_edit && userRoles.some(role => 
      role === "echeck_client" || role === "echeck_bingo"
    );

    if (!row.can_view && !row.can_edit) {
      return <span className="text-muted">-</span>;
    }
    return (
      <>
        {row.can_view && row.view_link && (
          <a
            href={row.view_link}
            target="_blank"
            rel="noopener noreferrer"
            style={{ marginRight: "8px", color: "#007bff", textDecoration: "none" }}
          >
            <i className="bi bi-eye"></i> View
          </a>
        )}
      {canEdit && (
        <Link
          to={`/invoices/edit-invoice/${row.invoice_id}`}
          style={{ color: "#007bff", textDecoration: "none" }}
          target="_blank"
          rel="noopener noreferrer"
        >
          <i className="bi bi-pencil"></i> Edit
        </Link>
      )}
      </>
    );
  };

  const columnDefinitions = [
    {
      id: "invoice_date",
      label: "Date",
      sortable: true,
      render: (value, row) => {
        return (
          <div>
            <div>{value || "-"}</div>
            <div>{renderViewEditLinks(row)}</div>
          </div>
        );
      },
    },
    // {
    //   id: "customer_invoice_no",
    //   label: "Invoice #",
    //   sortable: true,
    //   render: (value) => value || "-",
    // },
    {
    id: "customer_invoice_no",
      label: "Invoice #",
      sortable: true,
      render: (value, row) => {
        return value || row?.customerInvoiceNo || "-";
      },
    },
    {
      id: "billing_profile_name",
      label: "Billing Profile",
      sortable: true,
      render: (value) => value || "-",
    },
{
  id: "total_amount",
  label: "Amount",
  sortable: true,
  render: (value, row) => {
    const isPartiallyPaid = Number(row?.status_id) === STATUS_IDS.PARTIALLY_PAID;

    const openPartialPaidModal = () => {
      setModalData({
        title: "Invoice partially payments",
        actionType: ACTION_IDS.PP_INFO, // <-- new, info-only view
        invoiceId: row?.invoice_id,
        invoiceAmount: (parseFloat(row?.total_amount) || 0).toFixed(2),
        fetchInvoices,                             // optional
        onClose: () => setShowModal(false),
      });
      setShowModal(true);
    };

    return (
      <div
        className="tooltip-wrapper amount-tooltip"
        style={{ cursor: isPartiallyPaid ? "pointer" : "default" }}
      >
        <a
          className="amount-link"
          role={isPartiallyPaid ? "button" : undefined}
          tabIndex={isPartiallyPaid ? 0 : -1}
          onClick={isPartiallyPaid ? openPartialPaidModal : undefined}
          onKeyDown={
            isPartiallyPaid
              ? (e) => {
                  if (e.key === "Enter" || e.key === " ") openPartialPaidModal();
                }
              : undefined
          }
          title={isPartiallyPaid ? "View partial payment details" : undefined}
        >
          {formatUSD(value)}
        </a>

        <div className="custom-tooltip top">
          <span className="tooltip-label">Service value is:</span>{" "}
          <span className="tooltip-value">{formatUSD(row?.service_amount)}</span>
          {" and "}
          <span className="tooltip-label">Charge value is:</span>{" "}
          <span className="tooltip-value">{formatUSD(row?.chargeable_amount)}</span>
        </div>
      </div>
    );
  },
  cellStyle: () => ({ textAlign: "right" }),
}

,
    {
      id: "business_name",
      label: "Business Name",
      sortable: true,
      render: (value) => value || "-",
    },
    {
      id: "customer_name",
      label: "Customer Name",
      sortable: true,
      render: (value) => value || "-",
    },
    {
      id: "display_name",
      label: "User",
      sortable: true,
      render: (value) => value || "-",
    },
    {
      id: "status_id",
      label: "Status",
      sortable: true,
      render: (value, row) => {
        const status = statusMap[value];
        if (!status) return "-";

        let additional = "";

        if (
          row.updated_status?.toLowerCase() === "remind" &&
          row.remind_status_date
        ) {
          // Show full datetime (already in YYYY-MM-DD HH:mm:ss format)
          additional = ` (${row.remind_count || 1})\nOn ${row.remind_status_date}`;
        }

        return (
          <span
            style={{
              color: status.color,
              padding: "4px 8px",
              borderRadius: "4px",
              backgroundColor: `${status.color}20`,
              display: "inline-block",
              whiteSpace: "pre-line",
              //minWidth: "120px"
              minWidth: additional ? "120px" : "",
            }}
          >
            {status.text}{additional}
          </span>
        );
      },
    },

    // {
    //   id: "status_id",
    //   label: "Status",
    //   sortable: true,
    //   render: (value) => {
    //     const status = statusMap[value];
    //     if (!status) return "-";
    //     return (
    //       <span
    //         style={{
    //           color: status.color,
    //           padding: "4px 8px",
    //           borderRadius: "4px",
    //           backgroundColor: `${status.color}20`,
    //           display: "inline-block",
    //         }}
    //       >
    //         {status.text}
    //       </span>
    //     );
    //   },
    // },
    {
      id: "lead_group",
      label: "Lead Group",
      sortable: true,
      render: (value) => value || "-",
    },
    {
      id: "invoice_type_label",
      label: "Type",
      sortable: false,
      render: (value) => value || "-",
      cellStyle: () => ({
      }),  
    },
    {
      id: "product_title",
      label: "Product",
      sortable: true,
      render: (value) => value || "-",
    },
    {
      id: "due_date",
      label: "Due Date",
      sortable: true,
      render: (value) => value || "-",
    },
    {
      id: "days_due",
      label: "No. Days Due",
      sortable: true,
      render: (value) => {
        if (value === 0) return "0";
        if (!value || value === "-") return "-";
        return value;
      },
    },
    {
      id: "actions",
      label: "Action",
      sortable: false,
      render: (value, row) => <ActionDropdown inv={row} />,
      cellStyle: () => ({
        width: "150px",
      }),      
    },
  ];

  const [visibleColumns, setVisibleColumns] = useState([
    "invoice_date",
    "customer_invoice_no",
    //"billing_profile_name",
    "total_amount",
    "business_name",
    "customer_name",
    "status_id",
    //"lead_group",
    "invoice_type_label",
    "due_date",
    "days_due",
    "actions",
  ]);

  useEffect(() => {

    const currentUser = getCurrentUserInvoice();
    if (currentUser && Array.isArray(currentUser.roles)) {
      setUserRoles(currentUser.roles);
    }  
    const defaultRange = getDefaultDateRange();
    fetchInvoices(defaultRange.startDate, defaultRange.endDate);
  }, []);

  const generateExportData = () => {
    const exportColumns = columnDefinitions.filter(
      (col) => col.id !== "actions" && visibleColumns.includes(col.id)
    );

    const headers = exportColumns.map((col) => col.label);

    const data = sortedInvoices.map((inv) => {
      return exportColumns.map((col) => {
        const rawValue = inv[col.id];

        // Handle special formats
        if (col.id === "total_amount") {
          const total = formatUSD(rawValue);
          // const service = formatUSD(inv?.service_amount);
          // const charge = formatUSD(inv?.chargeable_amount);
          // return `Total: ${total}, Service: ${service}, Charge: ${charge}`;
          return total;
        }

        if (col.id === "status_id") {
          const status = statusMap[rawValue];
          return status ? status.text : "-";
        }

        if (col.id === "customer_invoice_no") {
          return rawValue || inv.customerInvoiceNo || "-";
        }

        if (["invoice_date", "due_date", "paid_date"].includes(col.id)) {
          return formatDateMMDDYYYY(rawValue);
        }

        const amountFields = [
          "total_amount",
          "service_amount",
          "chargeable_amount",
          "outstanding_charge_value",
          "outstanding_service_value",
          "payment_received",
          "pending_balance"
        ];

        if (amountFields.includes(col.id)) {
          return formatUSD(rawValue);
        }        
        // Basic fallback handling
        if (typeof rawValue === "string" || typeof rawValue === "number") {
          return rawValue;
        }

        return "-";
      });
    });

    return { headers, data };
  };

  const generateExportExcelData = () => {

    const exportColumns = [
      { id: "invoice_date", label: "Date" },
      { id: "customer_invoice_no", label: "Invoice #" },
      { id: "billing_profile_name", label: "Billing Profile" },
      { id: "total_amount", label: "Amount" },
      { id: "service_amount", label: "Service Amount" },
      { id: "chargeable_amount", label: "Chargeable Amount" },
      { id: "outstanding_charge_value", label: "Outstanding Charge Amount" },
      { id: "outstanding_service_value", label: "Outstanding Service Amount" },
      { id: "payment_received", label: "Payment Received" },
      { id: "pending_balance", label: "Pending Balance" },
      { id: "customer_name", label: "Customer Name" },
      { id: "business_name", label: "Business Name" },
      { id: "affiliate_name", label: "Affiliate Name" },
      { id: "merchant", label: "Merchant" },
      { id: "product", label: "Product" },
      { id: "days_due", label: "No. Days Due" },
      { id: "status_id", label: "Status" },
      { id: "paid_date", label: "Paid Date" },
      { id: "sales_person", label: "Sales Person" },
    ];

    const headers = exportColumns.map((col) => col.label);

    const data = sortedInvoices.map((inv) => {
      return exportColumns.map((col) => {
        const rawValue = inv[col.id];

        // Handle special formats
        if (col.id === "total_amount") {
          const total = formatUSD(rawValue);
          // const service = formatUSD(inv?.service_amount);
          // const charge = formatUSD(inv?.chargeable_amount);
          // return `Total: ${total}, Service: ${service}, Charge: ${charge}`;
          return total;
        }

        if (col.id === "status_id") {
          const status = statusMap[rawValue];
          return status ? status.text : "-";
        }
        if (["invoice_date", "due_date", "paid_date"].includes(col.id)) {
          return formatDateMMDDYYYY(rawValue);
        }
        if (col.id === "customer_invoice_no") {
          return rawValue || inv.customerInvoiceNo || "-";
        }        
        const amountFields = [
          "total_amount",
          "service_amount",
          "chargeable_amount",
          "outstanding_charge_value",
          "outstanding_service_value",
          "payment_received",
          "pending_balance"
        ];

        if (amountFields.includes(col.id)) {
          return formatUSD(rawValue);
        }        
        // Basic fallback handling
        if (typeof rawValue === "string" || typeof rawValue === "number") {
          return rawValue;
        }

        return "-";
      });
    });

    return { headers, data };
  };


  const fetchInvoices = async (start = startDate, end = endDate) => {
    setLoading(true);
    setError(null);
    
    const formatDate = (date) => {
      if (!date) return null;
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) return null;
      
      const mm = String(dateObj.getMonth() + 1).padStart(2, "0");
      const dd = String(dateObj.getDate()).padStart(2, "0");
      const yyyy = dateObj.getFullYear();
      return `${mm}/${dd}/${yyyy}`;
    };

    // Use provided dates or default to 15-day range
    let date_from, date_to;
    
    const defaultRange = getDefaultDateRange();
    date_from = formatDate(start || defaultRange.startDate);
    date_to = formatDate(end || defaultRange.endDate);

    try {
      const response = await axios.post(
        ENDPOINTS.INVOICE_LISTING,
        {
          date_from,
          date_to,
          merchant_id: merchantId,
          filter_type: "",
          search: "",
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const records = response.data?.data?.records || [];
      setInvoices(records);
    } catch (err) {
      setError("Failed to load invoice data");
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (field) => {
    const direction =
      sortField === field && sortDirection === "asc" ? "desc" : "asc";
    setSortField(field);
    setSortDirection(direction);
  };
  const fetchAllInvoices = async () => {
    setLoading(true);
    setError(null);

    // Get today's date in MM/DD/YYYY format
    const today = new Date();
    const mm = String(today.getMonth() + 1).padStart(2, "0");
    const dd = String(today.getDate()).padStart(2, "0");
    const yyyy = today.getFullYear();
    const formattedToday = `${mm}/${dd}/${yyyy}`;

    try {
      const response = await axios.post(
        ENDPOINTS.INVOICE_LISTING,
        {
          merchant_id: merchantId,
          date_from: "01/01/2000",     // Very old date to include all
          date_to: formattedToday,     // Today’s date
          filter_type: "",
          search: "",
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const records = response.data?.data?.records || [];
      //console.log("Fetched All Invoices:", records.length);
      setInvoices(records);
    } catch (err) {
      console.error("Error in fetchAllInvoices:", err?.response?.data || err.message);
      setError("Failed to load invoice data");
    } finally {
      setLoading(false);
    }
  };

const handleApplyDateFilter = async (start, end) => {
  //console.log('handleApplyDateFilter', start, end);

  // Update state with the new dates (empty strings for "all time")
  setStartDate(start || '');
  setEndDate(end || '');
  setCurrentPage(1);
  //console.log('start and end dates are empty sss',start ,end);
  if (!start && !end) {
    //console.log('start and end dates are empty',start ,end);

    Swal.fire({
      icon: 'success',
      title: 'All Time Filter Applied',
      text: 'Showing all invoices',
      timer: 1500,
      toast: true,
      position: 'top-end',
      showConfirmButton: false
    });
    // Fetch all invoices for "all time" filter
    await fetchAllInvoices();
  } else {
    // Fetch filtered invoices
    await fetchInvoices(start, end);

    const startDisplay = start ? new Date(start).toLocaleDateString() : '';
    const endDisplay = end ? new Date(end).toLocaleDateString() : '';

    Swal.fire({
      icon: 'success',
      title: 'Date Filter Applied',
      text: `Showing invoices from ${startDisplay} to ${endDisplay}`,
      timer: 1500,
      toast: true,
      position: 'top-end',
      showConfirmButton: false
    });
  }
};

  const handleClearFilters = () => {
    const defaultRange = getDefaultDateRange();
    setSearchTerm('');
    setStartDate(defaultRange.startDate);
    setEndDate(defaultRange.endDate);
    setCurrentPage(1);
    fetchInvoices(defaultRange.startDate, defaultRange.endDate);
  };

  const toggleColumnVisibility = (columnId) => {
    setVisibleColumns((prevVisibleColumns) => {
      if (prevVisibleColumns.includes(columnId)) {
        return prevVisibleColumns.filter((id) => id !== columnId);
      } else {
        return [...prevVisibleColumns, columnId];
      }
    });
  };
  const resetToDefaultColumns = () => {
    setVisibleColumns(columnDefinitions.map((col) => col.id));
  };
  const selectAllColumns = () => {
    setVisibleColumns(columnDefinitions.map((col) => col.id));
  };
  const parseInvoiceDate = (dateString) => {
    if (!dateString) return null;
    let date = new Date(dateString);
    if (isNaN(date.getTime())) {
      const parts = dateString.split("/");
      if (parts.length === 3) {
        date = new Date(parts[2], parts[0] - 1, parts[1]);
      }
    }
    if (isNaN(date.getTime())) {
      return null;
    }
    date.setHours(0, 0, 0, 0);
    return date;
  };
  const getExportFileName = (format) => {
    const today = new Date();
    const mm = String(today.getMonth() + 1).padStart(2, "0"); // Always 2-digit month
    const dd = String(today.getDate()).padStart(2, "0");      // Always 2-digit day
    const yyyy = today.getFullYear();
    return `Invoice_Report_${mm}-${dd}-${yyyy}.${format}`;
  };
  const formatDateMMDDYYYY = (dateStr) => {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr || "-";
    const mm = String(date.getMonth() + 1).padStart(2, "0");
    const dd = String(date.getDate()).padStart(2, "0");
    const yyyy = date.getFullYear();
    return `${mm}/${dd}/${yyyy}`;
  };

  const exportToCSV = () => {
    const { headers, data } = generateExportExcelData();
    if (data.length === 0) {
      Swal.fire("No Data", "No invoices to export.", "info");
      return;
    }

    // Escape commas and quotes
    const escapeCSV = (value) => {
      if (value == null) return "";
      const str = String(value).replace(/"/g, '""');
      return `"${str}"`;
    };

    const csvContent = [
      headers.map(escapeCSV).join(","), // header row
      ...data.map((row) => row.map(escapeCSV).join(",")),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", getExportFileName("csv"));
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    Swal.fire("Success", "Invoice data exported to CSV!", "success");
  };

  const exportToPDF = () => {
    const { headers, data } = generateExportData();
    if (data.length === 0) {
      Swal.fire("No Data", "No invoices to export.", "info");
      return;
    }
    const doc = new jsPDF("landscape");
    doc.setFontSize(18);
    doc.text("Invoice Report", doc.internal.pageSize.getWidth() / 2, 15, {
      align: "center",
    });
    autoTable(doc, {
      head: [headers],
      body: data,
      startY: 20,
      theme: "grid",
      styles: {
        fontSize: 8,
        cellPadding: 2,
        overflow: "linebreak",
      },
      headStyles: {
        fillColor: [22, 160, 133],
        textColor: [255, 255, 255],
        fontStyle: "bold",
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240],
      },
      margin: { top: 10, right: 10, bottom: 10, left: 10 },
      didDrawPage: function (data) {
        const str = "Page " + doc.internal.getNumberOfPages();
        doc.setFontSize(10);
        doc.text(
          str,
          doc.internal.pageSize.getWidth() - 10,
          doc.internal.pageSize.getHeight() - 10,
          { align: "right" }
        );
      },
    });
    doc.save(getExportFileName("pdf"));
    Swal.fire("Success", "Invoice data exported to PDF!", "success");
  };
  const exportToExcel = () => {
    const { headers, data } = generateExportExcelData();
    if (data.length === 0) {
      Swal.fire("No Data", "No invoices to export.", "info");
      return;
    }
    const formattedData = data.map((row) => {
      const rowObj = {};
      headers.forEach((label, idx) => {
        rowObj[label] = row[idx];
      });
      return rowObj;
    });
    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Invoices");
    XLSX.writeFile(wb, getExportFileName("xlsx"));
    Swal.fire("Success", "Invoice data exported to Excel!", "success");
  };

const filteredInvoices = useMemo(() => {
  return invoices.filter((inv) => {
    const searchTermLower = searchTerm.toLowerCase().trim();

  const matchesSearch =
    searchTerm === "" ||
    (inv.customer_invoice_no &&
      inv.customer_invoice_no.toLowerCase().includes(searchTermLower)) ||
    (inv.customerInvoiceNo &&
      inv.customerInvoiceNo.toLowerCase().includes(searchTermLower)) ||    
    (inv.business_name &&
      inv.business_name.toLowerCase().includes(searchTermLower)) ||
    (inv.customer_name &&
      inv.customer_name.toLowerCase().includes(searchTermLower)) ||
    (inv.display_name &&
      inv.display_name.toLowerCase().includes(searchTermLower)) ||
    (STATUS_MAP_FULL[inv.status_id]?.text || "")
      .toLowerCase()
      .includes(searchTermLower);
      

    // const matchesSearch =
    //   searchTerm === "" ||
    //   (inv.customer_invoice_no &&
    //     inv.customer_invoice_no.toLowerCase().includes(searchTermLower)) ||
    //   (inv.business_name &&
    //     inv.business_name.toLowerCase().includes(searchTermLower)) ||
    //   (inv.customer_name &&
    //     inv.customer_name.toLowerCase().includes(searchTermLower)) ||
    //   (inv.billing_profile_name &&
    //     inv.billing_profile_name.toLowerCase().includes(searchTermLower)) ||
    //   (inv.display_name &&
    //     inv.display_name.toLowerCase().includes(searchTermLower)) ||
    //   (STATUS_MAP_FULL[inv.status_id]?.text || "")
    //     .toLowerCase()
    //     .includes(searchTermLower) ||
    //   (inv.days_due && String(inv.days_due).includes(searchTermLower)) ||
    //   (inv.product_title &&
    //     inv.product_title.toLowerCase().includes(searchTermLower)) ||
    //   (inv.invoice_type_label &&
    //     inv.invoice_type_label.toLowerCase().includes(searchTermLower)) ||
    //   (inv.total_amount &&
    //     String(inv.total_amount).includes(searchTermLower)) ||
    //   (inv.due_date &&
    //     inv.due_date.toLowerCase().includes(searchTermLower)) ||
    //   (inv.invoice_date &&
    //     inv.invoice_date.toLowerCase().includes(searchTermLower));

    let matchesDateRange = true;
    if (startDate || endDate) {
      const invoiceDate = parseInvoiceDate(inv.invoice_date);
      const startFilterDate = startDate ? parseInvoiceDate(startDate) : null;
      const endFilterDate = endDate ? parseInvoiceDate(endDate) : null;

      if (!invoiceDate) {
        matchesDateRange = false;
      } else {
        if (startFilterDate && invoiceDate < startFilterDate) {
          matchesDateRange = false;
        }
        if (endFilterDate && matchesDateRange && invoiceDate > endFilterDate) {
          matchesDateRange = false;
        }
      }
    }

    // Tab filter
    let matchesTab = true;
    if (activeTab !== "all") {
      const statusId = parseInt(inv.status_id, 10);
      switch (activeTab) {
        case "unpaid":
          matchesTab = statusId === STATUS_IDS.UNPAID;
          break;
        case "in_process":
          matchesTab = statusId === STATUS_IDS.PAYMENT_IN_PROCESS;
          break;
        case "partially_paid":
          matchesTab = statusId === STATUS_IDS.PARTIALLY_PAID;
          break;
        case "paid":
          matchesTab = statusId === STATUS_IDS.PAID;
          break;
        case "overdue":
          matchesTab = inv.days_due > 0 && statusId !== STATUS_IDS.PAID && statusId !== STATUS_IDS.CANCELLED;
          break;
        default:
          matchesTab = true;
      }
    }

    return matchesSearch && matchesDateRange && matchesTab;
  });
}, [invoices, searchTerm, startDate, endDate, activeTab]);


  const sortedInvoices = useMemo(() => {
    return sortArrayByKey(filteredInvoices, sortField, sortDirection);
  }, [filteredInvoices, sortField, sortDirection]);

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = sortedInvoices.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredInvoices.length / itemsPerPage);

const getModalSize = (actionType) => MODAL_SIZES[actionType] || MODAL_SIZES.default;
// === Info-only partial payment block ===
const PartialPaymentInfoBlock = ({ invoiceId, invoiceAmount }) => {
  const [loading, setLoading] = useState(true);
  const [rows, setRows] = useState([]);
  const [overdueAmount, setOverdueAmount] = useState("0.00");
  const [outstandingService, setOutstandingService] = useState("0.00");
  const [outstandingCharge, setOutstandingCharge] = useState("0.00");

  const parseDollar = (str) =>
    parseFloat(String(str || "").replace(/[^0-9.]/g, "")) || 0;

  const formatDate = (str) => {
    const d = new Date(str);
    if (isNaN(d.getTime())) return str || "NA";
    const mm = String(d.getMonth() + 1).padStart(2, "0");
    const dd = String(d.getDate()).padStart(2, "0");
    const yy = d.getFullYear();
    return `${mm}/${dd}/${yy}`;
  };

  useEffect(() => {
    const fetchInfo = async () => {
      setLoading(true);
      try {
        const payload = {
          action_type: String(ACTION_IDS.PARTIALLY_PAID),
          invoiceid: String(invoiceId),
          invoiceAmount: String(invoiceAmount),
        };
        const res = await axios.post(ENDPOINTS.GET_INVOICE_ACTION, payload);
        const data = res?.data || {};

        setOverdueAmount(data?.overdue_amount || "0.00");
        setOutstandingService(data?.outstanding_service_value || "0.00");
        setOutstandingCharge(data?.outstanding_charge_value || "0.00");

        const html = data?.payment_table_html || "";
        const doc = new DOMParser().parseFromString(html, "text/html");

        // Prefer rows with class "ppamt"; fallback to <tbody> rows
        let trList = [...doc.querySelectorAll("tr.ppamt")];
        if (trList.length === 0) {
          const tb = doc.querySelector("tbody");
          if (tb) trList = [...tb.querySelectorAll("tr")];
        }

        const parsed = trList.map((tr) => {
          const tds = tr.querySelectorAll("td");
          return {
            refId: tds[0]?.textContent?.trim() || "",
            paymentDate: formatDate(tds[1]?.textContent?.trim()),
            clearedDate: formatDate(tds[2]?.textContent?.trim()),
            paymentMode: tds[3]?.textContent?.trim() || "",
            note: tds[4]?.textContent?.trim() || "",
            service: parseDollar(tds[5]?.textContent),
            charge: parseDollar(tds[6]?.textContent),
            received: parseDollar(tds[7]?.textContent),
          };
        });

        setRows(parsed);
      } catch {
        setRows([]);
      } finally {
        setLoading(false);
      }
    };

    if (invoiceId && invoiceAmount) fetchInfo();
    else setLoading(false);
  }, [invoiceId, invoiceAmount]);

  const totalPartial = rows.reduce((s, r) => s + (r.received || 0), 0);

  if (loading) {
    return (
      <div className="modal-body text-center py-5">
        <div className="spinner-border text-primary" role="status" />
        <p className="mt-3">Loading partial payments…</p>
      </div>
    );
  }

  return (
    <div>
      <div className="table-responsive custom-inv-table">
        <table className="table table-bordered table-sm mb-0">
          <thead className="bg-light">
            <tr>
              <th>Reference ID*</th>
              <th>Payment Date*</th>
              <th>Cleared Date*</th>
              <th>Payment Mode*</th>
              <th>Note</th>
              <th>Payment - Service</th>
              <th>Payment - Charge</th>
              <th>Payment Received*</th>
            </tr>
          </thead>
          <tbody>
            {rows.length ? (
              rows.map((r, i) => (
                <tr key={i} className="ppamt">
                  <td>{r.refId || "NA"}</td>
                  <td>{r.paymentDate || "NA"}</td>
                  <td>{r.clearedDate || "NA"}</td>
                  <td>{r.paymentMode || "NA"}</td>
                  <td>{r.note || "NA"}</td>
                  <td>{formatUSD(r.service)}</td>
                  <td>{formatUSD(r.charge)}</td>
                  <td>{formatUSD(r.received)}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="8" className="text-center text-muted py-3">
                  No partial payments found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Total Partial Payment Amount */}
      <div className="rounded info-setup mb-3 mt-3">
        <div className="d-flex justify-content-between">
          <strong>Total Partial Payment Amount</strong>
          <div>{formatUSD(totalPartial)}</div>
        </div>
      </div>

      {/* Overdue Amount + tooltip with outstanding breakdown */}
      <div className="rounded info-setup mb-1">
        <div className="d-flex justify-content-between align-items-center">
          <strong>
            Overdue Amount
            {(parseDollar(outstandingService) > 0 ||
              parseDollar(outstandingCharge) > 0) && (
              <span className="tooltip-wrapper amount-tooltip ms-2 d-inline-block">
                <i className="fa fa-info-circle text-primary" aria-hidden="true" />
                <div className="custom-tooltip top">
                  <div>
                    <strong>Outstanding Service:</strong>{" "}
                    {formatUSD(outstandingService)}
                  </div>
                  <div>
                    <strong>Outstanding Charge:</strong>{" "}
                    {formatUSD(outstandingCharge)}
                  </div>
                </div>
              </span>
            )}
          </strong>
          <div>{formatUSD(overdueAmount)}</div>
        </div>
      </div>
    </div>
  );
};

return (
  <PageContainer
    title="Invoice Report"
    showInvoiceTabs={true}
    activeTab={activeTab}
    onTabChange={setActiveTab}
    tabTooltipMap={TAB_TOOLTIP}
    tabHoverText={tabHoverText}
    setTabHoverText={setTabHoverText}
    className="veda"
  >
    <ReportFilter
      //key="invoice-filter"
      searchTerm={searchTerm}
      setSearchTerm={setSearchTerm}
      startDate={startDate}
      endDate={endDate}
      handleApplyDateFilter={handleApplyDateFilter}
      refreshData={refreshWithActiveFilters}
      loading={loading}
      isSearching={isSearching}
      setIsSearching={setIsSearching}
      setCurrentPage={setCurrentPage}
      columnGroups={[
        {
          id: "default",
          title: "Columns",
          columns: columnDefinitions.filter(
            (col) => !HIDDEN_UI_COLUMNS.includes(col.id)
          ),
        },
      ]}
      visibleColumns={visibleColumns}
      toggleColumnVisibility={toggleColumnVisibility}
      resetToDefaultColumns={resetToDefaultColumns}
      selectAllColumns={selectAllColumns}
      exportToExcel={exportToExcel}
      exportToPDF={exportToPDF}
      exportToCSV={exportToCSV}
      customSearchPlaceholder="Invoice Number, Business Name, Customer Name, User and Invoice Status"
    />
    <div className="table-responsive mt-4 custom-invoice-table">
      {loading ? (
        <div className="text-center p-4">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2">Loading invoice data...</p>
        </div>
      ) : error ? (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      ) : currentItems.length === 0 ? (
        <div className="text-center p-4">
          <p>No invoices found</p>
        </div>
      ) : (
        <table className="table table-bordered table-hover table-striped ">
          <thead>
            <tr>
              {columnDefinitions
                .filter(
                  (col) =>
                    visibleColumns.includes(col.id) &&
                    !HIDDEN_UI_COLUMNS.includes(col.id)
                )
                .map((col) =>
                  col.id === "actions" || col.sortable === false ? (
                    <th key={col.id}>{col.label}</th> // 👈 plain header, no sort UI
                  ) : (
                    <SortableTableHeader
                      key={col.id}
                      label={col.label}
                      field={col.id}
                      currentSortField={sortField}
                      currentSortDirection={sortDirection}
                      onSort={handleSort}
                    />
                  )
                )}
            </tr>
          </thead>
          <tbody>
            {currentItems.map((inv, index) => (
              <tr key={index}>
                {columnDefinitions
                  //.filter((col) => visibleColumns.includes(col.id))
                  .filter(
                    (col) =>
                      visibleColumns.includes(col.id) &&
                      !HIDDEN_UI_COLUMNS.includes(col.id)
                  )
                  .map((col) => (
                    <td
                      key={col.id}
                      style={
                        typeof col.cellStyle === "function"
                          ? col.cellStyle(inv[col.id], inv)
                          : {}
                      }
                    >
                      {col.render(inv[col.id], inv)}
                    </td>
                  ))}
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
    {!loading && !error && filteredInvoices.length > 0 && (
      <ReportPagination
        currentPage={currentPage}
        totalPages={totalPages}
        paginate={setCurrentPage}
        goToPreviousPage={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
        goToNextPage={() =>
          setCurrentPage((prev) => Math.min(prev + 1, totalPages))
        }
        indexOfFirstItem={indexOfFirstItem}
        indexOfLastItem={indexOfLastItem}
        totalFilteredItems={filteredInvoices.length}
        totalItems={invoices.length}
        itemName="invoices"
        loading={loading}
      />
    )}

    <Modal
      show={showModal}
      onClose={() => setShowModal(false)}
      title={modalData.title}
      size={getModalSize(modalData?.actionType)}
    >
      {modalData?.actionType === ACTION_IDS.PP_INFO ? (
        <PartialPaymentInfoBlock
          invoiceId={modalData?.invoiceId}
          invoiceAmount={modalData?.invoiceAmount}
        />
      ) : (
        <ModalContent modalData={modalData} actionsMap={actionsMap} />
      )}
    </Modal>
  </PageContainer>
);
};

export default InvoiceReport;
