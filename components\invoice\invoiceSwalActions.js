import Swal from "sweetalert2";
import { handlePauseReminder, handleResumeReminder } from "./invoiceActionHandlers";
import { getCurrentUserInvoice } from "./invoice-settings";

export const handlePauseReminderAction = (inv, fetchInvoices, onClose) => {
  const user = getCurrentUserInvoice();
  const userId = user?.id || null;

  Swal.fire({
    icon: "question",
    title: '<h2 style="font-weight: 600; font-size: 24px;">Are you sure?</h2>',
    html: `
      <p style="font-size: 16px; margin-top: 12px;">
        Are you sure you want to <strong>pause</strong> the auto reminder for invoice
        <strong>'${inv?.business_name}'</strong> invoice # <strong>'${inv?.customerInvoiceNo}'</strong>?
      </p>
    `,
    showCancelButton: true,
    confirmButtonText: "Yes, pause it",
    cancelButtonText: "Cancel",
    confirmButtonColor: "#3085d6",
    cancelButtonColor: "#d33",
    allowOutsideClick: false,
    allowEscapeKey: true,
    reverseButtons: false,
    showLoaderOnConfirm: true,
    preConfirm: async () => {
      try {
        const payload = {
          invoiceid: inv?.invoice_id,
          user_id: userId,
        };
        const res = await handlePauseReminder(payload);

        if (res?.result !== "success") {
          throw new Error(res?.msg || "Failed to pause reminder.");
        }
        return res;
      } catch (err) {
        Swal.showValidationMessage(err.message || "Unexpected error occurred.");
      }
    }
  }).then((result) => {
    if (result.isConfirmed && result.value) {
      Swal.fire("Success", result.value.msg || "Reminder paused.", "success");
      onClose?.();
      fetchInvoices?.();
    }
  });
};

export const handleResumeReminderAction = (inv, fetchInvoices, onClose) => {
  const user = getCurrentUserInvoice();
  const userId = user?.id || null;

  Swal.fire({
    icon: "question",
    title: '<h2 style="font-weight: 600; font-size: 24px;">Resume Auto Reminder?</h2>',
    html: `
      <p style="font-size: 16px; margin-top: 12px;">
        Are you sure you want to <strong>resume</strong> auto reminder for invoice
        <strong>'${inv?.business_name}'</strong> invoice # <strong>'${inv?.customerInvoiceNo}'</strong>?
      </p>
    `,
    showCancelButton: true,
    confirmButtonText: "Yes, resume it",
    cancelButtonText: "Cancel",
    confirmButtonColor: "#3085d6",
    cancelButtonColor: "#d33",
    allowOutsideClick: false,
    allowEscapeKey: true,
    reverseButtons: false,
    showLoaderOnConfirm: true,
    preConfirm: async () => {
      try {
        const payload = {
          invoiceid: inv?.invoice_id,
          user_id: userId,
        };
        const res = await handleResumeReminder(payload);

        if (res?.result !== "success") {
          throw new Error(res?.msg || "Failed to resume reminder.");
        }
        return res;
      } catch (err) {
        Swal.showValidationMessage(err.message || "Unexpected error occurred.");
      }
    }
  }).then((result) => {
    if (result.isConfirmed && result.value) {
      Swal.fire("Success", result.value.msg || "Reminder resumed.", "success");
      onClose?.();
      fetchInvoices?.();
    }
  });
};
