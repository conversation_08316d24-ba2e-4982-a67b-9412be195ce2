/* Finance Report Specific Styles */

/* Table Header Styling */
.finance-table thead th,
.finance-table .sortable-header {
  background-color: #f8f9fa !important;
  border: 1px solid #dee2e6 !important;
  border-bottom: 2px solid #1086be !important;
  font-weight: 600 !important;
  color: #1086be !important;
  white-space: nowrap !important;
  padding: 0.75rem 0.5rem !important;
  font-size: 0.8rem !important;
  text-transform: capitalize !important;
  position: relative !important;
  vertical-align: middle !important;
  text-align: left !important;
}

/* Table Header Bottom Border */
.finance-table thead th::after,
.finance-table .sortable-header::after {
  content: '' !important;
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 2px !important;
  background-color: #1086be !important;
}

/* Table Row Styling */
.finance-table tbody tr:nth-of-type(even) {
  background-color: rgba(0, 0, 0, 0.05) !important;
}

.finance-table tbody tr:hover {
  background-color: rgba(16, 134, 190, 0.05) !important;
}

/* Table Cell Styling */
.finance-table tbody td {
  padding: 0.4rem 0.5rem !important;
  vertical-align: middle !important;
  border: 1px solid #dee2e6 !important;
  font-size: 0.75rem !important;
}

/* Table ID Column */
.finance-table .id-column {
  font-weight: 600 !important;
  background-color: #f2f2f2 !important;
}

/* Action Dropdown Styling */
.dropdown-menu {
  min-width: 220px !important;
  padding: 8px 0 !important;
  margin: 0 !important;
  font-size: 0.85rem !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 16px rgba(0,0,0,0.15) !important;
  border: 1px solid rgba(0,0,0,0.08) !important;
  z-index: 1000 !important;
}

.dropdown-item {
  padding: 8px 16px;
  font-size: 0.85rem;
  color: #495057;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.dropdown-item:hover {
  background-color: rgba(16, 134, 190, 0.1);
  color: #1086be;
}

.dropdown-item:active {
  background-color: rgba(16, 134, 190, 0.2);
  color: #1086be;
}

.dropdown-divider {
  margin: 4px 0;
  border-top: 1px solid rgba(0,0,0,0.05);
}

/* Action Button Styling */
.btn-outline-primary {
  border-color: #1086be;
  color: #1086be;
  background-color: transparent;
  transition: all 0.2s ease;
}

.btn-outline-primary:hover {
  background-color: #1086be;
  color: white;
  border-color: #1086be;
}

.btn-outline-primary.dropdown-toggle {
  border-radius: 4px;
  padding: 4px 10px;
  font-size: 0.75rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* View/Edit Button Styling */
.view-edit-btn {
  display: flex;
  gap: 5px;
  margin-bottom: 5px;
}

.view-edit-btn a {
  color: #1086be;
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 500;
}

.view-edit-btn a:hover {
  text-decoration: underline;
  color: #f06b25;
}

/* Status Badge Styling */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
}

/* Default status - gray */
.status-badge {
  background-color: #6c757d;
  color: white;
}

/* Paid status - green */
.status-badge.paid {
  background-color: #28a745;
  color: white;
}

/* Invoiced status - blue */
.status-badge.invoiced {
  background-color: #007bff;
  color: white;
}

/* Cancel status - red */
.status-badge.cancel {
  background-color: #dc3545;
  color: white;
}

/* Draft status - gray */
.status-badge.draft {
  background-color: #6c757d;
  color: white;
}

/* Remind status - purple */
.status-badge.remind {
  background-color: #6f42c1;
  color: white;
}

/* Partially paid status - yellow/orange */
.status-badge.partiallypaid {
  background-color: #fd7e14;
  color: white;
}

/* Payment in process status - teal */
.status-badge.paymentinprocess {
  background-color: #20c997;
  color: white;
}

/* Charge with quickbooks status - dark blue */
.status-badge.chargewithquickbooks {
  background-color: #0056b3;
  color: white;
}

/* Charge with equipay status - light blue */
.status-badge.chargewithequipay {
  background-color: #17a2b8;
  color: white;
}

/* First collections status - orange */
.status-badge.firstcollections {
  background-color: #fd7e14;
  color: white;
}

/* Second collections status - darker orange */
.status-badge.secondcollections {
  background-color: #e65c00;
  color: white;
}

/* Demand collections status - dark red */
.status-badge.demandcollections {
  background-color: #c82333;
  color: white;
}

/* Delete status - black */
.status-badge.delete {
  background-color: #343a40;
  color: white;
}

/* Void status - light gray */
.status-badge.void {
  background-color: #adb5bd;
  color: white;
}

/* Payment Plan status - indigo */
.status-badge.paymentplan {
  background-color: #6610f2;
  color: white;
}

/* Table Cell Styling */
.finance-table td {
  vertical-align: middle;
  font-size: 0.85rem;
  padding: 8px 12px;
}

/* Ensure dropdown menus appear correctly */
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  z-index: 1000;
}

/* Ensure the first row stands out */
.table tbody tr:first-child {
  background-color: rgba(16, 134, 190, 0.05) !important;
}

/* .table tbody tr:first-child td {
  font-weight: 500;
} */

/* Info Banner Styling */
.info-banner {
  background-color: #cce5ff;
  border-color: #b8daff;
  color: #004085;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.info-banner i {
  margin-right: 10px;
  font-size: 1.1rem;
}

/* Notes Buttons Styling */
.btn-outline-info,
.btn-outline-success {
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 3px;
  padding: 0;
  transition: all 0.3s ease;
}

.btn-outline-info {
  color: #17a2b8;
  border-color: #17a2b8;
}

.btn-outline-info:hover {
  background-color: #17a2b8;
  color: white;
}

.btn-outline-success {
  color: #28a745;
  border-color: #28a745;
}

.btn-outline-success:hover {
  background-color: #28a745;
  color: white;
}
