/* MyProfile.css */

/* Using global header styling from the application */

.profile-section {
  margin-bottom: 30px;
  padding: 25px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.profile-section:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.section-title {
  color: #0077be;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #0077be;
  font-size: 18px;
  position: relative;
  display: inline-block;
  padding-right: 30px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: #f06b25;
}

.subsection-title {
  color: #333;
  font-weight: 600;
  margin-bottom: 25px;
  font-size: 16px;
  position: relative;
  padding-left: 15px;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 10px;
}

.subsection-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 5px;
  width: 6px;
  height: 18px;
  background-color: #f06b25;
  border-radius: 3px;
}

.form-control {
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  height: 45px;
}

.form-control:focus {
  border-color: #0077be;
  box-shadow: 0 0 0 0.2rem rgba(0, 119, 190, 0.25);
}

.form-control[readonly] {
  background-color: #f8f9fa;
  opacity: 0.7;
}

label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

/* Referral Links Styling */
.referral-links {
  margin-top: 15px;
}

.referral-link-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f1f3f9;
}

.referral-link-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.referral-link-type {
  width: 180px;
  font-size: 12px;
  font-weight: 600;
  color: #6c757d;
}

.referral-link-url {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.referral-link-url a {
  color: #0077be;
  text-decoration: none;
  font-size: 14px;
}

.referral-link-url a:hover {
  text-decoration: underline;
}

.referral-link-actions {
  display: flex;
  gap: 10px;
}

.copy-link-btn, .qr-btn {
  background-color: #f06b25 !important;
  color: white !important;
  border: none !important;
  padding: 5px 10px !important;
  font-size: 12px !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
  font-weight: 500 !important;
}

.copy-link-btn:hover, .qr-btn:hover {
  background-color: #e05a15 !important;
  color: white !important;
}

/* Update Profile Button */
.update-profile-btn {
  background-color: #f06b25 !important;
  color: white !important;
  border: none !important;
  padding: 6px 20px !important;
  font-size: 14px !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
  font-weight: 500 !important;
}

.update-profile-btn:hover {
  background-color: #e05a15 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(240, 107, 37, 0.3) !important;
}

/* Google Places Autocomplete styling */
.pac-container {
  border-radius: 4px;
  margin-top: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  font-family: inherit;
  z-index: 9999 !important;
}

.pac-item {
  padding: 8px 10px;
  font-size: 14px;
  cursor: pointer;
}

.pac-item:hover {
  background-color: #f8f9fa;
}

.pac-item-selected {
  background-color: #e9ecef;
}

.pac-icon {
  margin-right: 10px;
}

.pac-item-query {
  font-size: 14px;
  color: #333;
}

/* Read-only fields styling */
.form-control[readonly] {
  background-color: #f8f9fa;
  cursor: not-allowed;
  color: #6c757d;
}

/* Address suggestions styling */
.address-input-container {
  position: relative;
}

.address-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border: 1px solid #ced4da;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
}

.suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover {
  background-color: #f8f9fa;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .referral-link-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .referral-link-type {
    width: 100%;
    margin-bottom: 5px;
  }

  .referral-link-url {
    width: 100%;
    margin-bottom: 10px;
  }

  .referral-link-actions {
    width: 100%;
    justify-content: flex-start;
  }
}
