/* Invoice Settings Styles */
.invoice-settings-container {
  padding: 20px;
}

.settings-section {
  background: #fff;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section-title {
  color: #333;
  font-weight: 600;
  margin-bottom: 20px;
  font-size: 1.2rem;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  font-weight: 500;
  color: #555;
  margin-bottom: 8px;
}

.form-control {
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 10px 12px;
  font-size: 14px;
}

/* Dropdown indicator for select elements */
select.form-control {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
  cursor: pointer;
}

select.form-control:focus {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23007bff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

/* Ensure dropdown indicators work in all browsers */
select.form-control::-ms-expand {
  display: none;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Invalid state styling for required fields */
.form-control.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
  background-color: #fff5f5;
  transition: all 0.2s ease;
}

.form-control.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
  background-color: #fff5f5;
}

.form-control.is-invalid:hover {
  border-color: #c82333;
  background-color: #fff0f0;
}

/* Invalid feedback message styling */
.invoice-settings-invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 12px;
  color: #dc3545;
  font-weight: 500;
  background-color: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 4px;
  padding: 6px 8px;
  position: relative;
}

.invoice-settings-invalid-feedback::before {
  content: "⚠";
  margin-right: 6px;
  font-size: 14px;
}



/* Duplicate day validation styling */
.reminder-col .form-control.is-invalid {
  border-color: #dc3545;
  background-color: #fff5f5;
}

.reminder-col .form-control.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Helper text styling */
.helper-text {
  background: #f8f9fa;
  border-left: 4px solid #007bff;
  padding: 12px 16px;
  border-radius: 4px;
  margin-top: 16px;
}

.helper-text small {
  font-size: 13px;
  line-height: 1.4;
}

.helper-text i {
  color: #007bff;
}

/* Validation errors summary styling */
.validation-errors-summary .alert {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.15);
  position: relative;
  padding-right: 50px;
}

.validation-errors-summary .alert-heading {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #721c24;
  display: flex;
  align-items: center;
}

.validation-errors-summary .alert-heading i {
  color: #dc3545;
  margin-right: 8px;
  font-size: 16px;
}

.validation-errors-summary ul {
  padding-left: 24px;
  margin-bottom: 0;
}

.validation-errors-summary li {
  margin-bottom: 6px;
  font-size: 13px;
  line-height: 1.4;
  color: #721c24;
  position: relative;
}

.validation-errors-summary li::before {
  content: "•";
  color: #dc3545;
  font-weight: bold;
  position: absolute;
  left: -16px;
}

.validation-errors-summary .btn-close {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(220, 53, 69, 0.1);
  border: none;
  font-size: 18px;
  cursor: pointer;
  opacity: 0.8;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.validation-errors-summary .btn-close:hover {
  opacity: 1;
  background: rgba(220, 53, 69, 0.2);
  transform: scale(1.05);
}

/* Reminder Table Styles */
.reminder-table {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.reminder-header {
  display: grid;
  grid-template-columns: 1fr 2fr 2fr 1fr;
  background: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.reminder-header > div {
  padding: 15px 12px;
  border-right: 1px solid #dee2e6;
}

.reminder-header > div:last-child {
  border-right: none;
}

.reminder-row {
  display: grid;
  grid-template-columns: 1fr 2fr 2fr 1fr;
  border-bottom: 1px solid #e0e0e0;
  background: #fff;
}

.reminder-row:last-child {
  border-bottom: none;
}

.reminder-row:hover {
  background: #f8f9fa;
}

.reminder-col {
  padding: 12px;
  border-right: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
}

.reminder-col:last-child {
  border-right: none;
}

.reminder-col .form-control-sm {
  font-size: 14px;
  padding: 6px 8px;
}

/* Dropdown indicators for small form controls in reminder table */
.reminder-col select.form-control-sm {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 6px center;
  background-size: 14px;
  padding-right: 30px;
  cursor: pointer;
}

.reminder-col select.form-control-sm:focus {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23007bff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

.reminder-col select.form-control-sm::-ms-expand {
  display: none;
}

/* Overdue Days Column with Repeat Days Dropdown */
.reminder-col .d-flex {
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
}

.reminder-col .d-flex .form-control-sm {
  flex: 1;
  min-width: 80px;
}

.reminder-col .d-flex .form-control-sm:first-child {
  flex: 2;
  min-width: 120px;
}

/* Button Styles */
.btn-outline-warning {
  border-color: #ffc107;
  color: #856404;
}

.btn-outline-warning:hover {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

.btn-outline-danger {
  border-color: #dc3545;
  color: #dc3545;
}

.btn-outline-danger:hover {
  background-color: #dc3545;
  border-color: #dc3545;
  color: #fff;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.gap-2 {
  gap: 0.5rem;
}

/* Loading States */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .reminder-header,
  .reminder-row {
    grid-template-columns: 1fr;
  }
  
  .reminder-header > div,
  .reminder-col {
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .reminder-header > div:last-child,
  .reminder-col:last-child {
    border-bottom: none;
  }
  
  .reminder-header {
    display: none;
  }
  
  .reminder-row {
    margin-bottom: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 10px;
  }
  
  .reminder-col {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .reminder-col:last-child {
    border-bottom: none;
  }
  
  .reminder-col::before {
    content: attr(data-label);
    font-weight: 600;
    color: #495057;
    display: block;
    margin-bottom: 4px;
    font-size: 12px;
  }
}

/* Animation for new reminders */
.reminder-row {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Add More Reminder Button Styles */
.add-reminder-btn {
  background: white;
  border: 1px solid #ff6b35;
  color: #ff6b35;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-reminder-btn:hover {
  background: #ff6b35;
  color: white;
}

.add-reminder-btn i {
  font-size: 12px;
}

/* Preview and Delete Button Styles */
.preview-btn {
  background: white;
  border: 1px solid #ff6b35;
  color: #ff6b35;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  position: relative;
}

.preview-btn:hover {
  background: #ff6b35;
  color: white;
}

/* Preview button when no templates selected */
.preview-btn[title*="default content"] {
  border-color: #6c757d;
  color: #6c757d;
  background-color: #f8f9fa;
}

.preview-btn[title*="default content"]:hover {
  background-color: #6c757d;
  color: white;
  border-color: #6c757d;
}

/* Add a subtle indicator for default content preview */
.preview-btn[title*="default content"]::after {
  content: "⚠";
  position: absolute;
  top: -2px;
  right: -2px;
  background: #ffc107;
  color: #212529;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

/* Disabled preview button styling */
.preview-btn.disabled-preview {
  border-color: #dee2e6;
  color: #6c757d;
  background-color: #f8f9fa;
  cursor: not-allowed;
  opacity: 0.6;
}

.preview-btn.disabled-preview:hover {
  border-color: #dee2e6;
  color: #6c757d;
  background-color: #f8f9fa;
  transform: none;
}

/* Add warning indicator for disabled preview */
.preview-btn.disabled-preview::after {
  content: "⚠";
  position: absolute;
  top: -2px;
  right: -2px;
  background: #ffc107;
  color: #212529;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.delete-btn {
  background: white;
  border: 1px solid #dc3545;
  color: #dc3545;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.delete-btn:hover {
  background: #dc3545;
  color: white;
}

.delete-btn i {
  font-size: 12px;
}

/* Save Settings Button */
.save-button-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;
  padding: 0 1rem;
}

.save-settings-btn {
  background: #ff6b35;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
  text-align: center;
}

.save-settings-btn:hover:not(:disabled) {
  background: #e55a2b;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);
}

.save-settings-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Template Preview Modal Styles */
.template-preview-modal-wrapper {
  /* Parent class for all template preview modal styles */
}

.template-preview-modal-wrapper .modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.template-preview-modal-wrapper .modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.template-preview-modal-wrapper .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
}

.template-preview-modal-wrapper .modal-title {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: bold;
}

.template-preview-modal-wrapper .modal-close {
  background-color: #ff0000;
  border: none;
  font-size: 21px;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
  font-weight: 800;
}

.template-preview-modal-wrapper .modal-close:hover {
  background-color: #e60000;
  color: white;
}

.template-preview-modal-wrapper .template-preview-modal {
  padding: 20px 30px;
  max-height: calc(90vh - 80px);
  overflow-y: auto;
}

.template-preview-modal-wrapper .template-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 20px;
}

.template-preview-modal-wrapper .tab-button {
  padding: 10px 20px;
  border: none;
  background: #f8f9fa;
  cursor: pointer;
  border-radius: 4px 4px 0 0;
  margin-right: 5px;
  transition: all 0.2s;
  font-size: 14px;
}

.template-preview-modal-wrapper .tab-button:hover {
  background: #e9ecef;
}

.template-preview-modal-wrapper .tab-button.active {
  background: white;
  border: 1px solid #ddd;
  border-bottom: 1px solid white;
  color: #333;
  font-weight: 500;
}

.template-preview-modal-wrapper .tab-content {
  padding: 0 0;
}

.template-preview-modal-wrapper .template-section {
  margin-bottom: 15px;
}

.template-preview-modal-wrapper .template-label {
  display: block;
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
}

.template-preview-modal-wrapper .template-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f8f9fa;
  font-size: 14px;
  color: #666;
}

.template-preview-modal-wrapper .template-body {
  line-height: 1.6;
  color: #333;
  font-size: 14px;
}

.template-preview-modal-wrapper .template-body p {
  margin-bottom: 12px;
}

.template-preview-modal-wrapper .action-buttons {
  margin: 20px 0;
}

.template-preview-modal-wrapper .action-btn {
  display: block;
  width: 100%;
  padding: 12px;
  background: #ff6b35;
  color: white;
  border: none;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.template-preview-modal-wrapper .action-btn:hover {
  background: #e55a2b;
}

.template-preview-modal-wrapper .signature {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.template-preview-modal-wrapper .signature p {
  margin-bottom: 5px;
}

.template-preview-modal-wrapper .social-links {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.template-preview-modal-wrapper .social-links p {
  margin-bottom: 10px;
  font-weight: 500;
}

.template-preview-modal-wrapper .social-icons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.template-preview-modal-wrapper .social-icon {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: opacity 0.2s;
}

.template-preview-modal-wrapper .social-icon:hover {
  opacity: 0.8;
}

.template-preview-modal-wrapper .social-icon.linkedin {
  background: #0077b5;
}

.template-preview-modal-wrapper .social-icon.twitter {
  background: #333;
}

.template-preview-modal-wrapper .social-icon.facebook {
  background: #1877f2;
}

.template-preview-modal-wrapper .social-icon.instagram {
  background: #e4405f;
}

/* Responsive design for template preview modal */
@media (max-width: 768px) {
  .template-preview-modal-wrapper .modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .template-preview-modal-wrapper .template-preview-modal {
    padding: 15px 20px;
  }
  
  .template-preview-modal-wrapper .modal-header {
    padding: 15px 20px;
  }
} 