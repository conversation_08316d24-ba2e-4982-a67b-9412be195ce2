# Invoice Actions Widget - पूर्ण गाइड (Complete Guide)

## परिचय (Introduction)
यह गाइड आपको Invoice Actions Widget बनाने में मदद करेगा जो कि InvoicesTab में उपयोग होने वाली same functionality प्रदान करता है। यह widget विभिन्न invoice statuses के आधार पर different actions show करता है।

## प्रोजेक्ट संरचना (Project Structure)
```
components/
├── invoice/
│   ├── InvoiceReport.jsx (मुख्य रिपोर्ट फाइल)
│   ├── invoice-settings.js (सभी constants और settings)
│   ├── invoiceActionHandlers.js (API calls के लिए)
│   ├── invoiceSwalActions.js (SweetAlert actions)
│   ├── ModalContents.jsx (Modal content router)
│   ├── invoice.css (Styling)
│   └── Modals/ (सभी modal components)
└── tabs/
    └── InvoicesTab.jsx (Tab component)
```

## Step 1: Invoice Actions Widget Component बनाना

### File: `components/common/InvoiceActionsWidget.jsx`

```jsx
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { 
  ACTION_IDS, 
  ACTIONS_MAP, 
  SPECIAL_USER_IDS, 
  ALLOWED_ROLES_FOR_SHARE_LINK 
} from '../invoice/invoice-settings';
import './InvoiceActionsWidget.css';

const InvoiceActionsWidget = ({ 
  invoice, 
  currentUserId, 
  userRoles = [], 
  onActionSelect, 
  selectedValue = "Select",
  disabled = false,
  className = "",
  size = "default" // "sm", "default", "lg"
}) => {
  const [actionSelections, setActionSelections] = useState({});

  // Invoice के actions को parse करना
  const parseInvoiceActions = (invoice) => {
    if (!invoice || !invoice.action) return [];
    return invoice.action.split(",").map(a => a.trim()).filter(Boolean);
  };

  // Action change handler
  const handleActionChange = (invoiceId, actionValue, invoiceData) => {
    if (actionValue === "Select") return;
    
    setActionSelections(prev => ({
      ...prev,
      [invoiceId]: actionValue
    }));

    // Parent component को notify करना
    if (onActionSelect) {
      onActionSelect(invoiceId, actionValue, invoiceData);
    }
  };

  // Actions list generate करना
  const getAvailableActions = (invoice) => {
    const actionList = parseInvoiceActions(invoice);
    
    return actionList.filter(action => {
      // Delete action केवल specific user को show करना
      if (action === String(ACTION_IDS.DELETE) && 
          currentUserId !== SPECIAL_USER_IDS.CAN_SEE_DELETE) {
        return false;
      }
      
      // Share invoice link केवल allowed roles को show करना
      if (action === String(ACTION_IDS.SHARE_INVOICE_LINK) && 
          !userRoles.some(role => ALLOWED_ROLES_FOR_SHARE_LINK.includes(role))) {
        return false;
      }
      
      return true;
    });
  };

  const availableActions = getAvailableActions(invoice);
  const hasActions = availableActions.length > 0;

  // CSS classes generate करना
  const getSelectClasses = () => {
    let classes = "form-select invoice-actions-select";
    
    if (className) classes += ` ${className}`;
    if (size === "sm") classes += " form-select-sm";
    if (size === "lg") classes += " form-select-lg";
    if (disabled || !hasActions) classes += " action-disabled-invoice";
    
    return classes;
  };

  // अगर कोई actions नहीं हैं
  if (!hasActions) {
    return (
      <select className={getSelectClasses()} disabled>
        <option>No Action</option>
      </select>
    );
  }

  return (
    <select
      className={getSelectClasses()}
      value={actionSelections[invoice.invoice_id] || selectedValue}
      onChange={(e) => handleActionChange(invoice.invoice_id, e.target.value, invoice)}
      disabled={disabled}
    >
      <option value="Select">Select Action</option>
      {availableActions.map((action, index) => {
        const actionText = ACTIONS_MAP[action]?.text || `Action ${action}`;
        return (
          <option key={`${action}-${index}`} value={action}>
            {actionText}
          </option>
        );
      })}
    </select>
  );
};

// PropTypes for validation
InvoiceActionsWidget.propTypes = {
  invoice: PropTypes.object.isRequired,
  currentUserId: PropTypes.number,
  userRoles: PropTypes.array,
  onActionSelect: PropTypes.func,
  selectedValue: PropTypes.string,
  disabled: PropTypes.bool,
  className: PropTypes.string,
  size: PropTypes.oneOf(['sm', 'default', 'lg'])
};

export default InvoiceActionsWidget;
```

## Step 2: CSS Styling File बनाना

### File: `components/common/InvoiceActionsWidget.css`

```css
/* Invoice Actions Widget Styles */
.invoice-actions-select {
  font-size: 0.9rem !important;
  height: 38px !important;
  min-width: 150px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: #fff;
  color: #495057;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.invoice-actions-select:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.invoice-actions-select:hover:not(:disabled) {
  border-color: #adb5bd;
}

/* Disabled state */
.action-disabled-invoice {
  pointer-events: none;
  background: #f5f5f5 !important;
  color: #999 !important;
  border: 1px solid #7e8993 !important;
  opacity: 0.6;
  cursor: not-allowed !important;
}

/* Size variations */
.form-select-sm {
  height: 32px !important;
  font-size: 0.8rem !important;
  min-width: 130px;
}

.form-select-lg {
  height: 44px !important;
  font-size: 1rem !important;
  min-width: 170px;
}

/* Table integration */
.custom-invoice-table .invoice-actions-select {
  width: 100%;
  margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .invoice-actions-select {
    min-width: 120px;
    font-size: 0.8rem !important;
  }
}
```

## Step 3: Widget को InvoicesTab में integrate करना

### File: `components/tabs/InvoicesTab.jsx` में changes

Line 3 पर import add करें:
```jsx
import InvoiceActionsWidget from '../common/InvoiceActionsWidget';
import { getCurrentUserInvoice } from '../invoice/invoice-settings';
```

Line 68-110 के बीच existing action dropdown को replace करें:
```jsx
{/* Old code को replace करें */}
<div className="opp_edit_dlt_btn projects-iris">
  {invoice.status != 2 && invoice.status != 3 && invoice.status != 6 ? (
    <InvoiceActionsWidget
      invoice={invoice}
      currentUserId={getCurrentUserInvoice()?.id}
      userRoles={getCurrentUserInvoice()?.roles || []}
      onActionSelect={handleInvoiceActionChange}
      disabled={false}
      size="sm"
      className="invoice-tab-actions"
    />
  ) : (
    <span className="text-muted">No Actions Available</span>
  )}
</div>
```

## Step 4: Action Handler Function बनाना

### File: `components/tabs/InvoicesTab.jsx` में function add करें

```jsx
// Line 4 के बाद add करें
const handleInvoiceActionChange = (invoiceId, actionValue, invoiceData) => {
  // यहाँ आप अपना action handling logic add कर सकते हैं
  console.log('Action selected:', {
    invoiceId,
    actionValue,
    invoiceData
  });

  // Parent component के handleInvoiceActionChange को call करें
  if (handleInvoiceActionChange) {
    const event = { target: { value: actionValue } };
    handleInvoiceActionChange(event, invoiceId);
  }
};
```

## Step 5: Modal Integration के लिए Complete Setup

### File: `components/common/InvoiceActionsModal.jsx` बनाना

```jsx
import React, { useState } from 'react';
import Modal from '../common/Modal';
import ModalContent from '../invoice/ModalContents';
import { ACTIONS_MAP, MODAL_SIZES } from '../invoice/invoice-settings';

const InvoiceActionsModal = ({
  isOpen,
  onClose,
  modalData,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false);

  // Modal size determine करना
  const getModalSize = (actionType) => {
    // Different actions के लिए different sizes
    switch(actionType) {
      case 'send_reminder':
      case 'resend':
        return MODAL_SIZES.LARGE;
      case 'update_interest':
      case 'pp_info':
        return MODAL_SIZES.MEDIUM;
      default:
        return MODAL_SIZES.SMALL;
    }
  };

  const handleSuccess = (result) => {
    setLoading(false);
    if (onSuccess) {
      onSuccess(result);
    }
    onClose();
  };

  const handleError = (error) => {
    setLoading(false);
    console.error('Modal action error:', error);
  };

  if (!isOpen || !modalData) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={modalData.title}
      size={getModalSize(modalData.actionType)}
      loading={loading}
    >
      <ModalContent
        modalData={{
          ...modalData,
          onSuccess: handleSuccess,
          onError: handleError,
          setLoading: setLoading
        }}
        actionsMap={ACTIONS_MAP}
      />
    </Modal>
  );
};

export default InvoiceActionsModal;
```

## Step 6: Complete Integration Example

### File: `components/pages/InvoiceManagement.jsx` (Example usage)

```jsx
import React, { useState, useEffect } from 'react';
import InvoiceActionsWidget from '../common/InvoiceActionsWidget';
import InvoiceActionsModal from '../common/InvoiceActionsModal';
import { getCurrentUserInvoice, ACTIONS_MAP } from '../invoice/invoice-settings';

const InvoiceManagement = () => {
  const [invoices, setInvoices] = useState([]);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalData, setModalData] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);

  useEffect(() => {
    // User data load करना
    const user = getCurrentUserInvoice();
    setCurrentUser(user);

    // Invoices load करना
    loadInvoices();
  }, []);

  const loadInvoices = async () => {
    // यहाँ आपका invoice loading logic होगा
    try {
      // API call example
      // const response = await axios.get('/api/invoices');
      // setInvoices(response.data);
    } catch (error) {
      console.error('Error loading invoices:', error);
    }
  };

  const handleActionSelect = (invoiceId, actionValue, invoiceData) => {
    // Modal data prepare करना
    const modalPayload = {
      title: `${ACTIONS_MAP[actionValue]?.text || "Action"} - Invoice #${invoiceData?.customerInvoiceNo}`,
      actionType: actionValue,
      invoiceId: invoiceId,
      customerInvoiceNo: invoiceData?.customerInvoiceNo || "N/A",
      invoiceDate: invoiceData?.invoice_date || "N/A",
      invoiceAmount: parseFloat(invoiceData?.total_amount || 0).toFixed(2),
      invoiceUrl: invoiceData?.invoice_url || "",
      businessName: invoiceData?.business_name || "N/A",
      customerName: invoiceData?.customer_name || "N/A",
      userEmail: invoiceData?.user_email || "",
      statusId: invoiceData?.status_id || "",
      productTitle: invoiceData?.product_title || "N/A",
      dueDate: invoiceData?.due_date || "N/A",
      daysDue: parseInt(invoiceData?.days_due || 0, 10)
    };

    setModalData(modalPayload);
    setModalOpen(true);
  };

  const handleModalSuccess = (result) => {
    console.log('Action completed successfully:', result);
    // Invoices को refresh करना
    loadInvoices();
    // Success message show करना
    // Swal.fire('Success!', 'Action completed successfully', 'success');
  };

  const handleModalClose = () => {
    setModalOpen(false);
    setModalData(null);
  };

  return (
    <div className="invoice-management">
      <h2>Invoice Management</h2>

      <div className="invoices-list">
        {invoices.map((invoice, index) => (
          <div key={invoice.id || index} className="invoice-item">
            <div className="invoice-details">
              <h5>Invoice #{invoice.customerInvoiceNo}</h5>
              <p>Amount: ${invoice.total_amount}</p>
              <p>Status: {invoice.status_name}</p>
            </div>

            <div className="invoice-actions">
              <InvoiceActionsWidget
                invoice={invoice}
                currentUserId={currentUser?.id}
                userRoles={currentUser?.roles || []}
                onActionSelect={handleActionSelect}
                size="default"
                className="my-custom-actions"
              />
            </div>
          </div>
        ))}
      </div>

      {/* Modal for actions */}
      <InvoiceActionsModal
        isOpen={modalOpen}
        onClose={handleModalClose}
        modalData={modalData}
        onSuccess={handleModalSuccess}
      />
    </div>
  );
};

export default InvoiceManagement;
```

## Step 7: Advanced Configuration Options

### Custom Action Filtering

```jsx
// Custom filter function बनाना
const customActionFilter = (action, invoice, user) => {
  // Custom business logic
  if (invoice.status === 'paid' && action === 'send_reminder') {
    return false; // Paid invoices के लिए reminder hide करना
  }

  if (user.role === 'viewer' && ['delete', 'cancel'].includes(action)) {
    return false; // Viewer को destructive actions नहीं दिखाना
  }

  return true;
};

// Widget में use करना
<InvoiceActionsWidget
  invoice={invoice}
  currentUserId={currentUser?.id}
  userRoles={currentUser?.roles || []}
  onActionSelect={handleActionSelect}
  customFilter={customActionFilter}
/>
```

## Step 8: Error Handling और Validation

### File: `components/common/InvoiceActionsWidget.jsx` में error handling add करना

Line 45 के बाद add करें:
```jsx
// Error handling state
const [error, setError] = useState(null);
const [isProcessing, setIsProcessing] = useState(false);

// Validation function
const validateAction = (actionValue, invoiceData) => {
  if (!actionValue || actionValue === "Select") {
    setError("Please select a valid action");
    return false;
  }

  if (!invoiceData || !invoiceData.invoice_id) {
    setError("Invalid invoice data");
    return false;
  }

  // Clear any previous errors
  setError(null);
  return true;
};

// Enhanced action change handler
const handleActionChange = async (invoiceId, actionValue, invoiceData) => {
  if (!validateAction(actionValue, invoiceData)) {
    return;
  }

  setIsProcessing(true);

  try {
    setActionSelections(prev => ({
      ...prev,
      [invoiceId]: actionValue
    }));

    // Parent component को notify करना
    if (onActionSelect) {
      await onActionSelect(invoiceId, actionValue, invoiceData);
    }
  } catch (error) {
    setError(error.message || "An error occurred");
    console.error('Action selection error:', error);
  } finally {
    setIsProcessing(false);
  }
};
```

## Step 9: Testing और Debugging

### Testing Checklist:

1. **Basic Functionality Test:**
```jsx
// Test file: `__tests__/InvoiceActionsWidget.test.jsx`
import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import InvoiceActionsWidget from '../components/common/InvoiceActionsWidget';

const mockInvoice = {
  invoice_id: 123,
  action: "1,2,3,resend",
  status: 1,
  customerInvoiceNo: "INV-001"
};

test('renders action dropdown with correct options', () => {
  render(
    <InvoiceActionsWidget
      invoice={mockInvoice}
      currentUserId={12345}
      userRoles={['admin']}
      onActionSelect={jest.fn()}
    />
  );

  expect(screen.getByText('Select Action')).toBeInTheDocument();
  expect(screen.getByText('Unpaid')).toBeInTheDocument();
  expect(screen.getByText('Paid')).toBeInTheDocument();
});
```

2. **Manual Testing Steps:**
   - विभिन्न invoice statuses के साथ test करें
   - Different user roles के साथ test करें
   - Modal opening/closing test करें
   - API calls test करें

## Step 10: Production Deployment Checklist

### Pre-deployment Steps:

1. **Code Review:**
   - सभी console.log statements remove करें
   - Error handling properly implement करें
   - PropTypes validation add करें

2. **Performance Optimization:**
```jsx
// Memoization add करना
import React, { memo, useMemo } from 'react';

const InvoiceActionsWidget = memo(({ invoice, ...props }) => {
  const availableActions = useMemo(() => {
    return getAvailableActions(invoice);
  }, [invoice.action, props.currentUserId, props.userRoles]);

  // Rest of component...
});
```

3. **CSS Optimization:**
```css
/* Production-ready CSS */
.invoice-actions-select {
  /* Add vendor prefixes for better browser support */
  -webkit-transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -moz-transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
```

## Step 11: Common Issues और Solutions

### Issue 1: Actions not showing
**Solution:** Check करें कि invoice.action field properly populated है

### Issue 2: Modal not opening
**Solution:** Modal component properly imported और configured है या नहीं

### Issue 3: Styling issues
**Solution:** CSS classes properly applied हैं और conflicts नहीं हैं

### Issue 4: API calls failing
**Solution:** Network tab में check करें और error handling add करें

## Step 12: Advanced Features (Optional)

### Bulk Actions Support:
```jsx
const BulkInvoiceActions = ({ selectedInvoices, onBulkAction }) => {
  const [bulkAction, setBulkAction] = useState('');

  const handleBulkAction = () => {
    if (bulkAction && selectedInvoices.length > 0) {
      onBulkAction(bulkAction, selectedInvoices);
    }
  };

  return (
    <div className="bulk-actions">
      <select
        value={bulkAction}
        onChange={(e) => setBulkAction(e.target.value)}
      >
        <option value="">Select Bulk Action</option>
        <option value="send_reminder">Send Reminder to All</option>
        <option value="mark_paid">Mark All as Paid</option>
      </select>
      <button onClick={handleBulkAction}>Apply</button>
    </div>
  );
};
```

## Final Notes (अंतिम टिप्पणियाँ)

1. **Security:** हमेशा user permissions check करें
2. **Performance:** Large datasets के लिए pagination use करें
3. **Accessibility:** Screen readers के लिए proper labels add करें
4. **Mobile Responsiveness:** सभी screen sizes पर test करें
5. **Error Logging:** Production में proper error logging implement करें

यह widget अब production-ready है और आप इसे अपने project में कहीं भी use कर सकते हैं। सभी steps को carefully follow करें और हर change के बाद test करें।

## Support और Documentation

अगर कोई issue आए तो:
1. Browser console check करें
2. Network tab में API calls check करें
3. React DevTools से component state check करें
4. CSS inspector से styling issues check करें

**Happy Coding! 🚀**
