import React, { useState, useEffect } from 'react';
import { Mo<PERSON>, Button, Form, Row, Col } from 'react-bootstrap';

const PaymentMethodModal = ({
  show,
  onHide,
  onSelectPaymentMethod,
  currentPaymentMethodName,
  currentPaymentMethodType,
  currentPaymentInstruction,
  currentOtherBankInstructions,
  currentInvoiceUrl,
  otherEditPaymentLink
}) => {
  const [paymentOption, setPaymentOption] = useState(currentPaymentMethodType || 'Quickbook'); // 'Quickbook' or 'Other'
  const [qbCreditCard, setQbCreditCard] = useState(false);
  const [qbBankTransfer, setQbBankTransfer] = useState(false);
  const [qbBankInstructions, setQbBankInstructions] = useState(currentPaymentMethodType === 'Quickbook' ? currentPaymentInstruction : '');

  const [otherCreditCard, setOtherCreditCard] = useState(false);
  const [otherBankTransfer, setOtherBankTransfer] = useState(false);
  const [otherPayPal, setOtherPayPal] = useState(false);
  const [otherPayment, setOtherPayment] = useState(false);
  const [otherPaymentLink, setOtherPaymentLink] = useState(otherEditPaymentLink || currentInvoiceUrl || '');
  const [otherBankInstructions, setOtherBankInstructions] = useState(currentPaymentInstruction || '');

    // Initialize state based on currentPaymentMethodName and currentPaymentInstruction when modal opens or current value changes
  useEffect(() => {
    if (show) {
      console.log('Modal opened with:', {
        currentPaymentMethodName,
        currentPaymentMethodType,
        otherEditPaymentLink,
        currentInvoiceUrl,
        otherBankInstructions
      });
      
      // Set payment link first
      setOtherPaymentLink(otherEditPaymentLink || currentInvoiceUrl || '');
      
      // Set payment option based on currentPaymentMethodType
      setPaymentOption(currentPaymentMethodType || 'Quickbook');
      
      // Parse the payment method name to set checkboxes
      if (currentPaymentMethodName) {
        let methods = [];
        if (currentPaymentMethodName.includes(',')) {
          methods = currentPaymentMethodName.split(',').map(m => m.trim());
        } else if (currentPaymentMethodName.includes('-')) {
          const parts = currentPaymentMethodName.split('-');
          if (parts.length > 1) {
            methods = parts[1].split(',').map(m => m.trim());
          }
        }else{
          methods = currentPaymentMethodName;
        }
        
        console.log('Parsed methods:', methods);
        
        if (currentPaymentMethodType === 'Quickbook') {
          // Set QuickBook options
          setQbCreditCard(methods.includes('credit_card'));
          setQbBankTransfer(methods.includes('ach') || methods.includes('Bank Transfer'));
          setQbBankInstructions(currentPaymentInstruction || '');
          
          // Reset Other options and clear Other bank instructions
          setOtherCreditCard(false);
          setOtherBankTransfer(false);
          setOtherPayPal(false);
          setOtherPayment(false);
          setOtherBankInstructions('');
        } else {
          // Set Other options
          setOtherCreditCard(methods.includes('credit_card'));
          setOtherBankTransfer(methods.includes('ach') || methods.includes('Bank Transfer'));
          setOtherPayPal(methods.includes('PayPal'));
          setOtherPayment(methods.includes('Other Payment'));
          
          // Set Other bank instructions for Other payment type
          setOtherBankInstructions(currentPaymentInstruction || '');
          
          // Reset QuickBook options
          setQbCreditCard(false);
          setQbBankTransfer(false);
          setQbBankInstructions('');
        }
        
      } else {
        // Default to Bank Transfer checked if no method is set
        setQbBankTransfer(true);
        setQbBankInstructions(currentPaymentInstruction || '');
        setOtherBankInstructions('');
      }
    }
  }, [show]);

  // Helper to build selected method string
  const buildSelectedMethod = () => {
    if (paymentOption === 'Quickbook') {
      const qbMethods = [];
      if (qbCreditCard) qbMethods.push('credit_card');
      if (qbBankTransfer) qbMethods.push('ach');
      return qbMethods.join(',');
    } else {
      const otherMethods = [];
      if (otherCreditCard) otherMethods.push('credit_card');
      if (otherBankTransfer) otherMethods.push('ach');
      if (otherPayPal) otherMethods.push('PayPal');
      if (otherPayment) otherMethods.push('Other Payment');
      return otherMethods.join(',');
    }
  };

  // On any checkbox or instruction change, only update local state
  const handleCheckboxChange = (setter) => (e) => {
    setter(e.target.checked);
  };
  const handleInstructionChange = (setter, value) => {
    setter(value);
  };

  // Change Other Payment Link to input type url with validation
  const handleSave = () => {
    let payment_instruction = '';
    let invoice_url = '';
    
    if (paymentOption === 'Quickbook') {
      payment_instruction = qbBankInstructions;
    } else if (paymentOption === 'Other' && otherBankTransfer) {
      payment_instruction = otherBankInstructions;
    }
    
    if (paymentOption === 'Other' && otherPayment) {
      invoice_url = otherPaymentLink;
    }
    
    const saveData = {
      method: buildSelectedMethod(),
      payment_method_type: paymentOption,
      payment_instruction,
      invoice_url
    };
    
    console.log('Modal sending save data:', saveData);
    console.log('Modal state when saving:', {
      paymentOption,
      otherPayment,
      otherPaymentLink,
      otherEditPaymentLink
    });
    
    onSelectPaymentMethod(saveData);
    setTimeout(() => {
      onHide();
    }, 100);
  };

  return (
    <Modal show={show} onHide={onHide} centered size="lg" dialogClassName="payment-method-modal">
      <Modal.Header closeButton>
        <Modal.Title>Payment Method</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form>
          <Row className="mb-3">
            <Col>
              <Form.Check
                inline
                type="radio"
                label="Quickbook"
                name="paymentOption"
                value="Quickbook"
                checked={paymentOption === 'Quickbook'}
                onChange={() => setPaymentOption('Quickbook')}
              />
              <Form.Check
                inline
                type="radio"
                label="Others"
                name="paymentOption"
                value="Other"
                checked={paymentOption === 'Other'}
                onChange={() => setPaymentOption('Other')}
              />
            </Col>
          </Row>

          {paymentOption === 'Quickbook' && (
            <div className="p-3 border rounded">
              <Form.Group className="mb-2">
                <Form.Check
                  type="checkbox"
                  id="qb-credit-card-check"
                  label="Credit/Debit Card"
                  checked={qbCreditCard}
                  onChange={handleCheckboxChange(setQbCreditCard)}
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Check
                  type="checkbox"
                  id="qb-bank-transfer-check"
                  label="Bank Transfer"
                  checked={qbBankTransfer}
                  onChange={handleCheckboxChange(setQbBankTransfer)}
                />
              </Form.Group>
              <Form.Group>
                <Form.Label>QB Bank Transfer Instructions:</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  value={qbBankInstructions}
                  onChange={(e) => handleInstructionChange(setQbBankInstructions, e.target.value)}
                />
              </Form.Group>
            </div>
          )}

          {paymentOption === 'Other' && (
            <div className="p-3 border rounded">
              <Form.Group className="mb-2">
                <Form.Check
                  type="checkbox"
                  id="other-credit-card-check"
                  label="Credit/Debit Card"
                  checked={otherCreditCard}
                  onChange={handleCheckboxChange(setOtherCreditCard)}
                />
              </Form.Group>
              <Form.Group className="mb-2">
                <Form.Check
                  type="checkbox"
                  id="other-bank-transfer-check"
                  label="Bank transfer"
                  checked={otherBankTransfer}
                  onChange={e => {
                    setOtherBankTransfer(e.target.checked);
                    if (!e.target.checked) setOtherBankInstructions('');
                  }}
                />
              </Form.Group>
              {/* Show instructions only if Bank transfer is checked */}
              {otherBankTransfer && (
                <Form.Group className="mb-2">
                  <Form.Label>Other Bank Transfer Instructions: {otherBankInstructions}</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    value={otherBankInstructions}
                    onChange={e => setOtherBankInstructions(e.target.value)}
                  />
                </Form.Group>
              )}
              <Form.Group className="mb-2">
                <Form.Check
                  type="checkbox"
                  id="other-paypal-check"
                  label="PayPal"
                  checked={otherPayPal}
                  onChange={handleCheckboxChange(setOtherPayPal)}
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Check
                  type="checkbox"
                  id="other-payment-check"
                  label="Other Payment"
                  checked={otherPayment}
                  onChange={handleCheckboxChange(setOtherPayment)}
                />
              </Form.Group>
              {otherPayment && (
                <Form.Group>
                                     <Form.Label>Other Payment Link:</Form.Label>
                  <Form.Control
                    type="url"
                    id="otherPaymentLink"
                    value={otherPaymentLink}
                    onChange={e => setOtherPaymentLink(e.target.value)}
                    placeholder="https://quickbooks.intuit.com/in/"
                    required={otherPayment}
                    pattern="https?://.+"
                  />
                  {/* Optionally show validation error */}
                </Form.Group>
              )}
            </div>
          )}
        </Form>
      </Modal.Body>
      <Modal.Footer>
       
        <Button className='occams_submit_btn' onClick={handleSave}>
          Save Changes
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default PaymentMethodModal; 