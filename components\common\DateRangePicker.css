/* DateRangePicker Component Styles */

.date-range-picker {
  position: relative;
  display: inline-block;
  width: 100%;
}

.date-range-input {
  display: flex;
  width: 100%;
  border: 1px solid #ced4da;
  border-radius: 4px;
  overflow: hidden;
}

.date-range-input input {
  flex: 1;
  padding: 8px 12px;
  border: none;
  outline: none;
  font-size: 14px;
}

.filter-button {
  background-color: #f06b25;
  color: white;
  border: none;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.filter-button:hover {
  background-color: #e05a15;
}

.date-range-calendar {
  position: absolute;
  top: calc(100% + 5px);
  left: 0;
  z-index: 1000;
  background-color: white;
  border: 1px solid #ced4da;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 600px;
  max-width: 100vw;
  display: flex;
  flex-direction: column;
}

.calendar-container {
  display: flex;
  padding: 0;
}

.preset-options {
  width: 150px;
  border-right: 1px solid #e9ecef;
  background-color: #f8f9fa;
  overflow-y: auto;
  max-height: 400px;
}

.preset-option {
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
  color: #495057;
}

.preset-option:hover {
  background-color: #e9ecef;
}

.preset-option.active {
  background-color: #e9ecef;
  font-weight: 500;
  color: #17a2b8;
}

.calendars-wrapper {
  flex: 1;
  padding: 15px;
}

.date-inputs {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.date-input-group {
  flex: 1;
}

.date-input-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
}

.calendars {
  display: flex;
  gap: 15px;
}

.calendar {
  flex: 1;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.nav-button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #6c757d;
  padding: 0 5px;
}

.nav-button:hover {
  color: #17a2b8;
}

.month-year {
  display: flex;
  align-items: center;
  gap: 5px;
}

.month {
  font-weight: 500;
  color: #495057;
}

.month-year select {
  border: none;
  background: none;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
  padding: 2px;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  font-weight: 500;
  color: #6c757d;
  font-size: 12px;
  margin-bottom: 5px;
}

.days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
}

.day {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 30px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 13px;
  color: #495057;
  position: relative;
}

.day:hover {
  background-color: #e9ecef;
}

.day.other-month {
  color: #adb5bd;
}

.day.selected {
  background-color: #e6f7fa;
}

.day.start-date, .day.end-date {
  background-color: #17a2b8;
  color: white;
}

.day.hover-range {
  background-color: #e6f7fa;
}

.calendar-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 15px;
  border-top: 1px solid #e9ecef;
}

.apply-button, .cancel-button {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid transparent;
}

.apply-button {
  background-color: #28a745;
  color: white;
}

.apply-button:hover {
  background-color: #218838;
}

.cancel-button {
  background-color: #f8f9fa;
  border-color: #ced4da;
  color: #6c757d;
}

.cancel-button:hover {
  background-color: #e9ecef;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .date-range-calendar {
    width: 100%;
  }
  
  .calendar-container {
    flex-direction: column;
  }
  
  .preset-options {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
    max-height: 200px;
  }
  
  .calendars {
    flex-direction: column;
  }
}
