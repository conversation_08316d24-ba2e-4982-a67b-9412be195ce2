# Invoice Actions Reuse करने का Clean Implementation Guide

## 🎯 Overview
ProjectDetail.jsx में existing invoice folder के components को reuse करके advanced invoice actions implement करना।

## 📋 Required Files to Create/Modify

### 1. New File: `components/common/ReusableActionDropdown.jsx`
### 2. Modify: `components/ProjectDetail.jsx` 
### 3. Modify: `components/tabs/InvoicesTab.jsx`

---

## 🚀 Step 1: Create ReusableActionDropdown Component

**File:** `components/common/ReusableActionDropdown.jsx`

```jsx
import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { 
  ACTION_IDS, 
  ACTIONS_MAP, 
  SPECIAL_USER_IDS, 
  ALLOWED_ROLES_FOR_SHARE_LINK 
} from '../invoice/invoice-settings';

const ReusableActionDropdown = ({ 
  invoice, 
  currentUserId, 
  userRoles = [], 
  actionSelections = {}, 
  onActionChange,
  disabled = false,
  className = ""
}) => {
  // Parse invoice actions from string to array
  const parseInvoiceActions = (inv) => {
    if (!inv || !inv.action) return [];
    return inv.action.split(",").map((a) => a.trim()).filter(Boolean);
  };

  // Filter actions based on user permissions
  const getFilteredActions = (inv) => {
    const actionList = parseInvoiceActions(inv);
    
    return actionList.filter(action => {
      // Hide delete action for non-authorized users
      if (action === String(ACTION_IDS.DELETE) && 
          currentUserId !== SPECIAL_USER_IDS.CAN_SEE_DELETE) {
        return false;
      }
      
      // Hide share invoice link for unauthorized roles
      if (action === String(ACTION_IDS.SHARE_INVOICE_LINK) && 
          !userRoles.some(role => ALLOWED_ROLES_FOR_SHARE_LINK.includes(role))) {
        return false;
      }
      
      return true;
    });
  };

  const availableActions = useMemo(() => {
    return getFilteredActions(invoice);
  }, [invoice.action, currentUserId, userRoles]);

  const hasActions = availableActions.length > 0;

  // Handle action change
  const handleChange = (e) => {
    const value = e.target.value;
    if (value !== "Select" && onActionChange) {
      onActionChange(invoice.invoice_id || invoice.id, value, invoice);
    }
  };

  // If no actions available
  if (!hasActions) {
    return (
      <select className="form-select action-disabled-invoice" disabled>
        <option>No Action</option>
      </select>
    );
  }

  return (
    <select
      className={`form-select ${className}`}
      value={actionSelections[invoice.invoice_id || invoice.id] || "Select"}
      onChange={handleChange}
      disabled={disabled}
    >
      <option value="Select">Select Action</option>
      {availableActions.map((action, idx) => {
        const actionText = ACTIONS_MAP[action]?.text || `Action ${action}`;
        return (
          <option key={`${action}-${idx}`} value={action}>
            {actionText}
          </option>
        );
      })}
    </select>
  );
};

ReusableActionDropdown.propTypes = {
  invoice: PropTypes.object.isRequired,
  currentUserId: PropTypes.number,
  userRoles: PropTypes.array,
  actionSelections: PropTypes.object,
  onActionChange: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  className: PropTypes.string
};

export default ReusableActionDropdown;
```

---

## 🔧 Step 2: Update ProjectDetail.jsx

### Add Imports (Line 25 के बाद):
```jsx
// Invoice system imports
import { 
  ACTION_IDS, 
  ACTIONS_MAP, 
  getCurrentUserInvoice,
  SPECIAL_USER_IDS,
  ALLOWED_ROLES_FOR_SHARE_LINK 
} from './invoice/invoice-settings';
import Modal from './common/Modal';
import ModalContent from './invoice/ModalContents';
import './invoice/invoice.css';
import ReusableActionDropdown from './common/ReusableActionDropdown';
```

### Add Modal States (Line 679 के बाद):
```jsx
// Modal states for invoice actions
const [showModal, setShowModal] = useState(false);
const [modalData, setModalData] = useState(null);
const [actionSelections, setActionSelections] = useState({});
```

### Replace handleInvoiceActionChange (Line 688):
```jsx
// Enhanced invoice action handler
const handleInvoiceActionChange = (invoiceId, newValue, inv) => {
  if (newValue === "Select") return;

  // Update action selections
  setActionSelections(prev => ({
    ...prev,
    [invoiceId]: newValue
  }));

  // Create modal payload (same structure as InvoiceReport.jsx)
  const modalPayload = {
    title: `${ACTIONS_MAP[newValue]?.text || "Action"} - Invoice #${inv?.customer_invoice_no}`,
    actionType: newValue || "",
    invoiceId: invoiceId || "",
    customerInvoiceNo: inv?.customer_invoice_no || "N/A",
    invoiceDate: inv?.invoice_date || "N/A",
    invoiceAmount: isNaN(parseFloat(inv?.total_amount))
      ? "0.00"
      : parseFloat(inv.total_amount).toFixed(2),
    invoiceUrl: inv?.invoice_url || "",
    businessName: inv?.business_name || "N/A",
    customerName: inv?.customer_name || "N/A",
    userEmail: inv?.user_email || "",
    statusId: inv?.status_id || inv?.status || "",
    productTitle: inv?.product_title || inv?.product_names || "N/A",
    dueDate: inv?.due_date || "N/A",
    daysDue: typeof inv?.days_due === "number" || !isNaN(inv?.days_due)
      ? parseInt(inv.days_due, 10)
      : "N/A",
    resetActionSelection: () => {
      setActionSelections(prev => ({
        ...prev,
        [invoiceId]: "Select"
      }));
    }
  };

  // Set modal data and show modal
  setModalData(modalPayload);
  setShowModal(true);
};

// Modal close handler
const handleModalClose = () => {
  setShowModal(false);
  setModalData(null);
  
  // Reset action selection
  if (modalData?.invoiceId) {
    setActionSelections(prev => ({
      ...prev,
      [modalData.invoiceId]: "Select"
    }));
  }
};

// Modal success handler
const handleModalSuccess = (result) => {
  console.log('Invoice action completed:', result);
  
  // Refresh invoice data
  fetchInvoiceData();
  
  // Close modal
  handleModalClose();
};
```

### Update fetchInvoiceData Function (Line 1596):
```jsx
// Modified fetchInvoiceData function
const fetchInvoiceData = async () => {
  setLoading(true);
  setError('');

  try {
    const response = await axios.post(
      "https://portal.occamsadvisory.com/portal/wp-json/productsplugin/v1/get-project-invoices",
      { project_id: projectId }
    );

    if (response.data.status == 200) {
      setIsInvoiceData(true);

      // Transform invoice data to match InvoiceReport format
      const transformedInvoices = (response.data.data || []).map(invoice => ({
        ...invoice,
        // Add action field based on status
        action: getInvoiceActions(invoice.status),
        // Ensure required fields exist
        invoice_id: invoice.id,
        customer_invoice_no: invoice.customer_invoice_no || `ERC-${invoice.id}`,
        total_amount: invoice.total_amount || '0.00',
        business_name: invoice.business_name || 'N/A',
        customer_name: invoice.customer_name || 'N/A',
        user_email: invoice.user_email || '',
        product_title: invoice.product_names || 'N/A'
      }));

      setInvoices(transformedInvoices);
    } else {
      setError(response.data.message || 'Failed to fetch invoices');
    }
  } catch (err) {
    console.error('Error fetching invoice data:', err);
  } finally {
    setLoading(false);
  }
};

// Helper function to get actions based on status
const getInvoiceActions = (status) => {
  switch(status) {
    case 1: // Unpaid
    case 5: // Overdue
      return "2,3,6,14,share_invoice_link"; // Paid, Void, Payment in process, Partially paid, Share link
    case 14: // Partially paid
      return "14"; // Only Partially paid
    case 2: // Paid
    case 3: // Void
    case 6: // Payment in process
    default:
      return ""; // No actions
  }
};
```

### Add Modal Component (Line 5192 के बाद):
```jsx
{/* Invoice Action Modal - Using existing ModalContent */}
{showModal && modalData && (
  <Modal
    isOpen={showModal}
    onClose={handleModalClose}
    title={modalData.title}
    size="lg"
  >
    <ModalContent
      modalData={modalData}
      actionsMap={ACTIONS_MAP}
    />
  </Modal>
)}
```

---

## 🔄 Step 3: Update InvoicesTab.jsx

### Add Imports (Line 1):
```jsx
import React from 'react';
import ReusableActionDropdown from '../common/ReusableActionDropdown';
import { getCurrentUserInvoice } from '../invoice/invoice-settings';
```

### Replace Action Dropdown (Line 67-136):
```jsx
<div className="opp_edit_dlt_btn projects-iris">
  <ReusableActionDropdown
    invoice={invoice}
    currentUserId={getCurrentUserInvoice()?.id}
    userRoles={getCurrentUserInvoice()?.roles || []}
    actionSelections={invoiceActions}
    onActionChange={handleInvoiceActionChange}
    disabled={false}
    className="project-invoice-actions"
  />
</div>
```

---

## ✅ Implementation Checklist

### Phase 1: Component Creation
- [ ] Create `ReusableActionDropdown.jsx` file
- [ ] Copy provided code exactly
- [ ] Verify import paths are correct

### Phase 2: ProjectDetail.jsx Updates
- [ ] Add all required imports
- [ ] Add modal states
- [ ] Replace `handleInvoiceActionChange` function
- [ ] Add modal close and success handlers
- [ ] Update `fetchInvoiceData` function
- [ ] Add `getInvoiceActions` helper function
- [ ] Add modal component in JSX

### Phase 3: InvoicesTab.jsx Updates
- [ ] Add required imports
- [ ] Replace existing select dropdown with `ReusableActionDropdown`

### Phase 4: Testing
- [ ] Test different invoice statuses
- [ ] Test action dropdown functionality
- [ ] Test modal opening/closing
- [ ] Test action processing
- [ ] Verify user permissions work

---

## 🎯 Expected Results

After implementation:
1. **Dynamic Actions** - Actions based on invoice.action field
2. **User Permissions** - Role-based action filtering
3. **Modal Integration** - Complete modal system for all actions
4. **Consistent UI** - Same look and feel as InvoiceReport
5. **Full Functionality** - All invoice actions working (Paid, Void, Partial, etc.)

---

## 🚨 Important Notes

1. **File Paths** - Verify all import paths are correct for your project structure
2. **Existing Code** - Don't remove existing functionality, only replace specified sections
3. **Testing** - Test thoroughly with different user roles and invoice statuses
4. **Console Logs** - Check browser console for any errors during implementation

**Ready for Implementation! 🚀**
