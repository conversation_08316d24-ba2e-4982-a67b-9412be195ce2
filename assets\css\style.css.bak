@import url("https://fonts.googleapis.com/css2?family=Mulish:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,200;1,300;1,400&display=swap");

#root {
  width: 100%;
}

body {
  font-family: "Mulish", sans-serif !important;
  font: "Mulish", sans-serif !important;
  padding: 0;
  margin: 0 !important;
  font-size: 14px;
  position: relative;
  background: #f6f7fb;
  margin-top: -32px;
}

input,
textarea,
select,
td,
th {
  font-family: "Mulish", sans-serif !important;
}

input:hover,
input:focus,
select:hover,
select:focus  {
  outline: none !important;
  box-shadow: 0px 0px 0px 0px transparent !important;
}

:focus {
  outline: -webkit-focus-ring-color auto 0;
}

a {
  text-decoration: none;
}

a:focus,
.button:focus,
button:focus,
.btn:focus {
  text-decoration: none;
  outline: none;
  box-shadow: none;
  -webkit-transition: 1s;
  transition: 1s;
}


/* line 891, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.note-btn-group.note-style {
  display: none;
}

/* line 898, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.plr_30 {
  padding: 0 30px !important;
}

@media (max-width: 767.98px) {

  /* line 898, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .plr_30 {
    padding: 0 15px !important;
  }
}

/* line 905, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.mb_8 {
  margin-bottom: 8px !important;
}

/* line 908, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.mb_7 {
  margin-bottom: 7px !important;
}

/* line 919, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.ml-10 {
  margin-left: 10px;
}

/* line 922, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.mb_30 {
  margin-bottom: 30px !important;
}

@media (max-width: 575.98px) {

  /* line 925, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .lms_block {
    display: block !important;
  }
}

@media (max-width: 575.98px) {

  /* line 931, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .add_button {
    margin-top: 15px;
    margin-left: 0;
  }
}

/* line 939, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.lms_pagination_wrap {
  margin-top: 30px;
}

/* line 942, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.lms_pagination_wrap ul li {
  display: inline-block;
  margin: 0 0.8px;
}

/* line 945, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.lms_pagination_wrap ul li a {
  width: 40px;
  height: 40px;
  display: inline-block;
  background: #fff;
  line-height: 40px;
  color: #cec1c2;
  text-align: center;
  border-radius: 3px;
  font-size: 14px;
}

/* line 955, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.lms_pagination_wrap ul li a.active {
  background: #fe1724;
  color: #fff;
}

/* line 959, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.lms_pagination_wrap ul li a:hover {
  background: #fe1724;
  color: #fff;
}

@media (min-width: 768px) {

  /* line 972, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .modal-dialog.custom-modal-dialog {
    max-width: 650px;
  }
}

@media (min-width: 992px) {

  /* line 977, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .modal-dialog.custom-modal-dialog {
    max-width: 950px;
  }
}

@media (min-width: 1200px) {

  /* line 982, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .modal-dialog.custom-modal-dialog {
    max-width: 1050px;
  }
}

/* line 988, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.file_upload {
  border: 1px solid #eee1e2 !important;
  padding: 9px 24px;
  border-radius: 3px;
  /* input file style */
}

/* line 992, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.file_upload label {
  margin-bottom: 0 !important;
}

/* line 995, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.file_upload .form-group {
  padding: 0;
  margin-bottom: 0;
}

/* line 999, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.file_upload .btn_file_upload {
  color: #fff;
  padding: 0;
  height: 30px;
  line-height: 30px;
  border: 0;
  display: inline-block;
}

/* line 1006, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.file_upload .btn_file_upload:hover,
.file_upload .btn_file_upload:focus {
  color: #888888;
  border-color: #888888;
}

/* line 1015, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.file_upload .input-file,
.file_upload .input-file2 {
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  z-index: -1;
}

/* line 1022, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.file_upload .input-file+.js-labelFile,
.file_upload .input-file+.js-labelFile2,
.file_upload .input-file2+.js-labelFile,
.file_upload .input-file2+.js-labelFile2 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 10px;
  cursor: pointer;
}

/* line 1030, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.file_upload .btn {
  padding: 0px 16px !important;
  border-radius: 5px;
  background-color: #fe1724;
  display: inline-block !important;
  font-size: 14px;
  color: #fff !important;
  border-radius: 30px;
  bottom: 0;
  margin-bottom: 0;
}

/* line 1040, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.file_upload .btn:hover {
  background-color: #2e4765;
  color: #fff;
  border: 0;
}

/* line 1046, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.file_upload .form-group {
  background-color: #fff;
  width: 100%;
  padding: 0px;
  border-radius: 5px;
}

/* line 1053, G:/admin_project/8 admin/management_html/scss/_reset.scss */
td:focus,
th:focus {
  outline: none !important;
}

/* line 1056, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.mb_20 {
  margin-bottom: 20px;
}

/* line 1059, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.mr_10 {
  margin-right: 10px !important;
}

/* line 1064, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.checkbox_wrap p {
  font-size: 13px;
  font-weight: 300;
  color: #7e7172;
  margin-bottom: 0;
}

/* line 1071, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.lms_checkbox_1 {
  display: inline-block;
  height: 30px;
  position: relative;
  width: 50px;
  margin-bottom: 0;
  margin-right: 15px;
}

/* line 1078, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.lms_checkbox_1 input {
  display: none !important;
}

/* line 1081, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.lms_checkbox_1 .slider-check {
  background-color: #f6f7fb;
  bottom: 0;
  cursor: pointer;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  transition: 0.4s;
}

/* line 1090, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.lms_checkbox_1 .slider-check.round {
  border-radius: 34px;
}

/* line 1093, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.lms_checkbox_1 .slider-check::before {
  background-color: #f45b0f;
  bottom: 5px;
  content: "";
  height: 20px;
  left: 4px;
  position: absolute;
  transition: 0.4s;
  width: 20px;
}

/* line 1103, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.lms_checkbox_1 .slider-check.round:before {
  border-radius: 50%;
}

/* line 1107, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.lms_checkbox_1 input:checked+.slider-check {
  background-color: #f6f7fb;
  color: #fff;
  content: "oui";
}

/* line 1112, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.lms_checkbox_1 input:checked+.slider-check:before {
  transform: translateX(20px);
  background: #884ffb;
}

/* line 1122, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.input_wrap label {
  font-size: 12px;
  font-weight: 300;
  color: #222222;
  text-align: left;
  margin: 0;
  margin-bottom: 0px;
  display: block;
  margin-bottom: 8px;
}

@media (min-width: 991px) {

  /* line 1137, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .medium_modal_width .modal-dialog {
    max-width: 780px;
  }
}

/* line 1145, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.list_header_btn_wrapper {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
  -webkit-justify-content: space-between;
  -moz-justify-content: space-between;
  -ms-justify-content: space-between;
  justify-content: space-between;
  -ms-flex-pack: space-between;
  width: 100%;
  margin-right: 60px;
}

@media (max-width: 575.98px) {

  /* line 1145, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .list_header_btn_wrapper {
    display: block;
    margin-bottom: 15px;
    margin-right: 0;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 1145, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .list_header_btn_wrapper {
    margin-right: 0;
    margin-bottom: 15px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {

  /* line 1145, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .list_header_btn_wrapper {
    margin-right: 0;
    display: block;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 1145, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .list_header_btn_wrapper {
    margin-right: 0;
    display: block;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1439px) {

  /* line 1145, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .list_header_btn_wrapper {
    margin-right: 30px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 1173, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .list_header_block {
    display: block;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 1177, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .list_header_block .success_faild_btn {
    margin-top: 15px;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1439px) {

  /* line 1182, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .list_header_block .serach_field_2 {
    width: 300px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 1182, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .list_header_block .serach_field_2 {
    width: 380%;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 1190, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .list_header_block .box_right {
    justify-content: space-between;
  }
}

/* line 1198, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.gj-datepicker .fa-calendar-alt {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  font-size: 14px;
  color: #cec1c2;
  right: 25px;
}

/* line 1208, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.white_box_border {
  border: 1px solid rgba(202, 206, 213, 0.7);
}

/* line 1212, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.nowrap {
  white-space: nowrap;
}

/* line 1216, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.mb-25 {
  margin-bottom: 25px;
}

/* line 1219, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.mb_15 {
  margin-bottom: 15px;
}

/* line 1222, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.mr-10 {
  margin-right: 10px;
}

/* line 1225, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.mr_5 {
  margin-right: 5px;
}

/* line 1228, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.mb_30 {
  margin-bottom: 30px;
}

/* line 1231, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.mt_10px {
  margin-top: 10px;
}

/* line 1234, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.pt_30 {
  padding-top: 30px !important;
}

/* line 1241, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.wrapper .progress {
  margin-bottom: 15px;
}

/* line 1255, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.apexcharts-datalabels-group {
  position: relative;
  top: -13px !important;
}

/* line 1259, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal {
  background-color: #fef1f2;
}

/* line 1261, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-header {
  background-color: #f7faff;
  padding: 23px 30px;
  border-bottom: 0px solid transparent;
}

/* line 1265, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-header h5 {
  font-size: 22px;
  font-weight: 600;
}

/* line 1269, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-header span {
  font-size: 14px;
  color: #707070;
  opacity: 1;
}

/* line 1275, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body {
  padding: 35px 30px;
}

/* line 1277, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body input,
.cs_modal .modal-body .nice_Select {
  height: 50px;
  line-height: 50px;
  padding: 10px 20px;
  border: 1px solid #f1f3f5;
  color: #707070;
  font-size: 14px;
  font-weight: 500;
  background-color: #fff;
  width: 100%;
}

/* line 1288, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body .nice_Select {
  line-height: 29px;
}

/* line 1290, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body .nice_Select:after {
  right: 22px;
}

/* line 1293, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body .nice_Select .list {
  width: 100%;
}

/* line 1297, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body .form-group {
  margin-bottom: 12px;
}

/* line 1300, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body textarea {
  height: 168px;
  padding: 15px 20px;
  border: 1px solid #f1f3f5;
  color: #707070;
  font-size: 14px;
  font-weight: 500;
}

/* line 1308, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body p {
  font-size: 16px;
  font-weight: 500;
  margin-top: 25px;
  color: #707070;
  text-align: center;
}

/* line 1314, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body p a {
  color: #2e4765;
}

/* line 1319, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body .btn_1 {
  width: 100%;
  display: block;
  margin-top: 20px;
}

/* line 1325, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body .social_login_btn .btn_1 {
  color: #fff;
  background-color: #3b5998;
  border: 1px solid #3b5998;
  margin-top: 0;
}

/* line 1330, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body .social_login_btn .btn_1:hover {
  color: #fff !important;
}

/* line 1333, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body .social_login_btn .btn_1 i {
  margin-right: 10px;
}

/* line 1339, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body .social_login_btn .form-group:last-child .btn_1 {
  background-color: #4285f4;
  border: 1px solid #4285f4;
}

/* line 1346, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body .pass_forget_btn {
  color: #707070;
  margin-top: 20px;
  font-size: 16px;
  text-align: center;
  font-weight: 500;
  display: inline-block;
}

/* line 1353, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_modal .modal-body .pass_forget_btn:hover {
  color: #2e4765;
}

/* line 1361, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.border_style {
  width: 100%;
  display: block;
  text-align: center;
  padding: 13px 0 17px;
  position: relative;
  z-index: 1;
}

/* line 1368, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.border_style span {
  padding: 0 25px;
  text-align: center;
  display: inline-block;
  background-color: #fff;
}

/* line 1374, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.border_style:after {
  position: absolute;
  content: "";
  height: 1px;
  width: 100%;
  background-color: #bbc1c9;
  top: 24px;
  left: 0;
  z-index: -1;
}

/* line 1386, G:/admin_project/8 admin/management_html/scss/_reset.scss */
p a {
  color: #7e7172;
}

/* line 1388, G:/admin_project/8 admin/management_html/scss/_reset.scss */
p a:hover {
  color: #2e4765;
}

/* line 1394, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_check_box {
  display: flex;
  margin-top: 20px;
}

/* line 1397, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_check_box input,
.cs_check_box label {
  line-height: 20px !important;
  height: auto !important;
  width: auto !important;
  max-width: auto;
}

/* line 1403, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_check_box .common_checkbox {
  display: none;
}

/* line 1406, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_check_box label {
  position: relative;
  padding-left: 34px;
  margin-bottom: 0;
}

/* line 1411, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_check_box .common_checkbox+label {
  display: block;
  cursor: pointer;
  color: #7e7172;
}

/* line 1416, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_check_box .common_checkbox+label:after {
  position: absolute;
  left: 0;
  top: 0;
  height: 20px;
  width: 20px;
  border: 1px solid #7e7172;
  border-radius: 5px;
  content: "";
}

/* line 1426, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_check_box .common_checkbox:checked+label:after {
  content: "\f00c";
  font-family: "Font Awesome\ 5 Free";
  font-weight: 900;
  padding: 2px;
  font-size: 12px;
  line-height: 15px;
  text-align: center;
}

/* line 1436, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.cs_check_box .common_checkbox {
  display: none !important;
}

/* line 1440, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.hide_pils.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
  border: 0 !important;
}

/* line 1451, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.mr_20 {
  margin-right: 20px;
}

@media (max-width: 575.98px) {

  /* line 1454, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .mb-sm-15 {
    margin-bottom: 15px;
  }
}

/* line 1460, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.heading_6 {
  font-size: 14px;
  font-weight: 600;
  font-family: "Mulish", sans-serif;
  color: #2e4765;
  margin-bottom: 0;
}

/*line 1468, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.card_height_100 {
  height: calc(100% - 30px);
}

/* line 1471, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.height_100 {
  height: 100%;
}

/* line 1474, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.card_height_50 {
  height: calc(50% - 30px);
}

/* line 1478, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.page-item.active .page-link {
  background-color: #884ffb;
  border-color: #884ffb;
}

/* line 1481, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.page-item.active .page-link:hover {
  color: #fff;
  background-color: #884ffbe6;
  border-color: #884ffbe6;
}

/* line 1487, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.page-link {
  color: #884ffb;
}

/* line 1489, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.page-link:hover {
  color: #fff;
  background-color: #884ffbe6;
  border-color: #884ffbe6;
}

/* line 1495, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.btn-primary {
  color: #fff;
  background-color: #884ffb;
  border-color: #884ffb;
}

.btn-success {
  color: #fff;
  background-color: #218838;
  border-color: #28a745;
}

/* line 1500, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.btn-primary:hover {
  color: #fff;
  background-color: #884ffbe6;
  border-color: #884ffbe6;
}

@media (max-width: 991px) {

  /* line 1506, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .sm_padding_15px {
    padding: 15px !important;
  }
}

/* line 1513, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.dashboard_header .dashboard_header_title h3 {
  font-size: 33px;
  font-weight: 600;
  color: #2e4765;
  margin-bottom: 7px;
}

/* line 1518, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.dashboard_header .dashboard_header_title h3 span {
  color: #884ffb;
}

/* line 1522, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.dashboard_header .dashboard_header_title p {
  font-size: 20px;
  font-weight: 400;
  color: #7f8b9f;
}

@media (max-width: 991px) {

  /* line 1530, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .dashboard_breadcam {
    text-align: left !important;
    padding-top: 10px !important;
  }
}

/* line 1535, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.dashboard_breadcam ul {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* line 1539, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.dashboard_breadcam ul li {
  font-size: 17px;
  font-weight: 400;
  color: #884ffb;
  padding-left: 14px;
  position: relative;
}

/* line 1545, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.dashboard_breadcam ul li:first-child {
  padding-left: 0;
}

/* line 1547, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.dashboard_breadcam ul li:first-child::before {
  display: none;
}

/* line 1551, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.dashboard_breadcam ul li::before {
  color: #884ffb;
  content: "/";
  position: absolute;
  left: 4px;
  top: 50%;
  transform: translateY(-50%);
}

/* line 1559, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.dashboard_breadcam ul li a {
  width: 30px;
  height: 30px;
  background: #ffe7df;
  border-radius: 50%;
  display: inline-block;
  line-height: 26px;
  text-align: center;
  margin-right: 10px;
}

/* line 1568, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.dashboard_breadcam ul li a img {
  line-height: 30px;
}

/* line 1576, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.header_prise h4 {
  font-size: 18px;
  font-weight: 700;
  color: #000;
  margin-bottom: 0;
}

/* line 1582, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.header_prise h5 {
  font-size: 16px;
  font-weight: 500;
  color: #884ffb;
  margin-bottom: 0;
}

/* line 1590, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.pink_bg {
  background: #fe80b2 !important;
}

/* line 1593, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.parpel_bg {
  background: #833cdf !important;
}

/* line 1596, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.parpel_bg2 {
  background: #f65365 !important;
}

/* line 1601, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.map-js-height {
  height: 450px;
}

@media (max-width: 1365.98px) {

  /* line 1601, G:/admin_project/8 admin/management_html/scss/_reset.scss */
  .map-js-height {
    height: 350px;
  }
}

/* line 1608, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.border_radius_0 {
  border-radius: 0 !important;
}

/* line 1611, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.white_bg {
  background: #fff;
}

/* line 1614, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.border_1px {
  border: 1px solid #eee;
}

/* line 1618, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.card_footer {
  padding: 50px;
  border-top: 1px solid #f2f4ff;
  position: relative;
  overflow: hidden;
}

/* line 1624, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.SubTitle {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 5px;
  margin-bottom: 8px;
  font-size: 18px;
}

/* line 1630, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.p-20 {
  padding: 20px;
}

/* line 1633, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.bg-primary {
  background-color: #4c2fbf !important;
  color: #fff;
}

/* line 1637, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.bg-secondary {
  background-color: #2e9de4 !important;
  color: #fff;
}

/* line 1641, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.bg-success {
  background-color: #80cf00 !important;
  color: #fff;
}

/* line 1645, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.bg-info {
  background-color: #06b5dd !important;
  color: #fff;
}

/* line 1649, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.bg-warning {
  background-color: #ffc717 !important;
  color: #fff;
}

/* line 1653, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.bg-danger {
  background-color: #fd517d !important;
  color: #fff;
}

/* line 1657, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.bg-light {
  background-color: #f8f5fd !important;
  color: #fff;
}

/* line 1661, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.bg-dark {
  background-color: #2c323f !important;
  color: #fff;
}

/* line 1665, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.color-box>div>div {
  border-radius: 15px;
}

/* line 1668, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.m-b-10 {
  margin-bottom: 10px;
}

/* line 1672, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.btn-light {
  border: 1px solid #e8ecf4;
}

/* line 1675, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.font-size-10 {
  font-size: 10px !important;
}

/* line 1678, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.text-primary {
  color: #5664d2 !important;
}

/* line 1681, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.font-size-14 {
  font-size: 14px !important;
}

/* line 1684, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.line-height-2 {
  line-height: 2;
}

/* line 1688, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.text_white {
  color: #fff;
}

/* line 1691, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.f_w_400 {
  font-weight: 400;
}

/* line 1694, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.f_w_700 {
  font-weight: 700;
}

/* line 1697, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.f_s_11 {
  font-size: 11px;
}

/* line 1700, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.f_s_12 {
  font-size: 12px;
}

/* line 1703, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.f_s_13 {
  font-size: 13px;
}

/* line 1706, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.f_s_14 {
  font-size: 14px;
}

/* line 1709, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.f_s_15 {
  font-size: 15px;
}

/* line 1712, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.f_s_16 {
  font-size: 16px;
}

/* line 1715, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.f_s_18 {
  font-size: 18px;
}

/* line 1718, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.f_s_19 {
  font-size: 19px;
}

/* line 1721, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.f_s_20 {
  font-size: 20px !important;
}

/* line 1724, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.f_s_17 {
  font-size: 17px;
}

/* line 1727, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.f_s_28 {
  font-size: 28px;
}

/* line 1730, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.f_s_40 {
  font-size: 40px;
}

/* line 1733, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.f_s_60 {
  font-size: 60px;
}

/* line 1736, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.ml_25 {
  margin-left: 25px;
}

/* line 1739, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.mr_30 {
  margin-right: 30px;
}

/* line 1742, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.ml_15 {
  margin-left: 15px;
}

/* line 1745, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.ml_18 {
  margin-left: 18px;
}

/* line 1748, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.color_theme {
  color: #474d58;
}

/* line 1751, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.color_theme2 {
  color: #101038;
}

/* line 1754, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.color_gray {
  color: #8890b5;
}

/* line 1757, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.color_gray2 {
  color: #b1b6c9;
}

/* line 1760, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.pt_25 {
  padding-top: 25px !important;
}

/* line 1763, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.pulse .pulse-ring {
  display: block;
  border-radius: 40px;
  height: 40px;
  width: 40px;
  position: absolute;
  -webkit-animation: animation-pulse 3.5s ease-out;
  animation: animation-pulse 3.5s ease-out;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  opacity: 0;
  border-width: 3px;
  border-style: solid;
  border-color: #e4e6ef;
}

/* line 1779, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.pulse .pulse-ring.notification_count_pulse {
  right: -18px;
  top: -14px;
}

/* line 1783, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.pulse.pulse-primary .pulse-ring {
  border-color: rgba(54, 153, 255, 0.75);
}

/* line 1786, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.pulse.pulse-secondary .pulse-ring {
  border-color: rgba(228, 230, 239, 0.75);
}

/* line 1790, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.pulse.pulse-success .pulse-ring {
  border-color: rgba(27, 197, 189, 0.75);
}

/* line 1794, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.pulse.pulse-info .pulse-ring {
  border-color: rgba(137, 80, 252, 0.75);
}

/* line 1798, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.pulse.pulse-warning .pulse-ring {
  border-color: rgba(255, 168, 0, 0.75);
}

/* line 1802, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.pulse.pulse-danger .pulse-ring {
  border-color: rgba(246, 78, 96, 0.75);
}

/* line 1806, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.pulse.pulse-light .pulse-ring {
  border-color: rgba(243, 246, 249, 0.75);
}

/* line 1810, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.pulse.pulse-dark .pulse-ring {
  border-color: rgba(24, 28, 50, 0.75);
}

/* line 1814, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.pulse.pulse-white .pulse-ring {
  border-color: rgba(255, 255, 255, 0.75);
}

/* line 1818, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.pulse.theme_color .pulse-ring {
  border-color: rgba(255, 255, 255, 0.75);
}

@-webkit-keyframes animation-pulse {
  0% {
    -webkit-transform: scale(0.1, 0.1);
    opacity: 0;
  }

  60% {
    -webkit-transform: scale(0.1, 0.1);
    opacity: 0;
  }

  65% {
    opacity: 1;
  }

  100% {
    -webkit-transform: scale(1.2, 1.2);
    opacity: 0;
  }
}

@keyframes animation-pulse {
  0% {
    -webkit-transform: scale(0.1, 0.1);
    opacity: 0;
  }

  60% {
    -webkit-transform: scale(0.1, 0.1);
    opacity: 0;
  }

  65% {
    opacity: 1;
  }

  100% {
    -webkit-transform: scale(1.2, 1.2);
    opacity: 0;
  }
}

/* line 1861, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.header_more_tool .dropdown .dropdown-toggle::after {
  display: none !important;
}

/* line 1864, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.header_more_tool .dropdown span {
  padding-left: 0px;
  background: #f5f5ff;
  padding: 5px 12px;
  border-radius: 5px;
}

/* line 1869, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.header_more_tool .dropdown span i {
  font-size: 12px;
  font-weight: 400;
  display: inline-block;
  color: #464e5f;
  position: relative;
  top: 0px;
  margin-right: 0px;
}

/* line 1879, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.header_more_tool .dropdown .dropdown-menu {
  border: 0;
  margin: 0;
  padding: 0;
  font-size: 13px;
  top: 5px !important;
  border-radius: 5px;
  -webkit-box-shadow: 0px 0px 20px 0px rgba(80, 143, 244, 0.2);
  -moz-box-shadow: 0px 0px 20px 0px rgba(80, 143, 244, 0.2);
  box-shadow: 0px 0px 20px 0px rgba(80, 143, 244, 0.2);
}

/* line 1889, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.header_more_tool .dropdown .dropdown-menu a {
  padding: 8px 15px;
  font-size: 13px;
}

/* line 1892, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.header_more_tool .dropdown .dropdown-menu a:hover {
  color: #fff;
}

/* line 1894, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.header_more_tool .dropdown .dropdown-menu a:hover i {
  color: #fff;
}

/* line 1898, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.header_more_tool .dropdown .dropdown-menu a i {
  font-size: 13px;
  vertical-align: middle;
  position: relative;
  top: -3px;
  margin-right: 5px;
}

/* line 1911, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.common_input input {
  height: 47px;
  line-height: 47px;
  background-color: transparent;
  border: 0;
  padding: 10px 25px;
  color: #81879f;
  font-weight: 400;
  font-size: 14px;
  width: 100%;
  display: block;
  border-radius: 10px;
  border: 1px solid #e5ecff;
}

/* line 1924, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.common_input input::placeholder {
  color: #81879f;
  font-weight: 400;
  font-size: 14px;
  opacity: 1;
  background: transparent;
}

/* line 1935, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.nav-link-notify {
  position: relative;
}

/* line 1939, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.nav-link-notify:before {
  content: "";
  position: absolute;
  width: 6px;
  height: 6px;
  right: -7px;
  border-radius: 50%;
  top: -10px;
  background-color: #f65365;
  -webkit-animation: notify-pulse 1s infinite;
}

@-webkit-keyframes notify-pulse {
  0% {
    box-shadow: 0 0 0 0px rgba(246, 83, 101, 0.7);
  }

  100% {
    box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);
  }
}

@keyframes notify-pulse {
  0% {
    box-shadow: 0 0 0 0px rgba(246, 83, 101, 0.7);
  }

  100% {
    box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);
  }
}

/* line 1971, G:/admin_project/8 admin/management_html/scss/_reset.scss */
#back-top {
  position: fixed;
  right: 20px;
  bottom: 30px;
  cursor: pointer;
  z-index: 9999999;
  display: none;
}

/* line 1978, G:/admin_project/8 admin/management_html/scss/_reset.scss */
#back-top a {
  display: inline-block;
  height: 40px;
  width: 40px;
  text-align: center;
  display: block;
  color: #fff;
  line-height: 40px;
  background: #ff3b00 !important;
  font-size: 15px;
  border-radius: 30px;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0px 10px 20px 0px rgba(76, 110, 248, 0.4);
  -moz-box-shadow: 0px 10px 20px 0px rgba(76, 110, 248, 0.4);
  box-shadow: 0px 10px 20px 0px rgba(76, 110, 248, 0.4);
}

/* line 2000, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.img-thumbnail {
  background-color: #f5f6ff !important;
  border: 1px solid #f5f6ff !important;
  border-radius: 20px !important;
}

/* line 2006, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.default_border_1px {
  border: 1px solid #e0e2e8;
  box-shadow: none !important;
}

/* line 2010, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.shadow_none {
  box-shadow: none !important;
}

/* line 2017, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.overflow_hidden {
  overflow: hidden !important;
}

/* line 2020, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.chart_wrap {
  border-radius: 15px;
}

/* line 2024, G:/admin_project/8 admin/management_html/scss/_reset.scss */
.round_badge {
  font-size: 11px;
  font-weight: 500;
  padding: 0 6.5px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  background: rgba(95, 99, 242, 0.06);
  color: #884ffb;
}

/******** base color css ********/
/* line 2, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_1 {
  background-color: #884ffb !important;
  border: 1px solid #884ffb !important;
  color: #fff;
  display: inline-block !important;
  padding: 9px 25px !important;
  text-transform: capitalize !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  border-radius: 5px !important;
  white-space: nowrap;
  -webkit-transition: 0.5s !important;
  transition: 0.5s !important;
}

/* line 14, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_1 i {
  font-size: 15px;
  padding-right: 7px;
}

/* line 18, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_1:hover {
  color: #fff;
  background-color: #884ffb;
  box-shadow: 0 3px 11px rgba(136, 79, 251, 0.4);
}

/* line 23, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_1.light {
  background-color: rgba(46, 71, 101, 0.1);
  border: 1px solid transparent;
  color: #2e4765;
}

/* line 28, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_1.green {
  background-color: rgba(46, 201, 184, 0.1);
  border: 1px solid transparent;
  color: #2ec9b8;
}

/* line 33, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_1.orange_btn {
  background: #2d1967;
  font-size: 16px;
  border: 0;
  font-weight: 400;
  padding: 22px 29px;
}

/* line 39, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_1.orange_btn:hover {
  background: #2d1967;
  color: #fff;
}

/* line 44, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_1.sm {
  font-size: 12px;
  padding: 6px 15px;
}

/* line 48, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_1.big_btn {
  padding: 11px 36px;
}

/* line 51, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_1.form_big_btn_1 {
  padding: 16px 62px !important;
}

@media (max-width: 575.98px) {

  /* line 51, G:/admin_project/8 admin/management_html/scss/_button.scss */
  .btn_1.form_big_btn_1 {
    padding: 16px 30px !important;
  }
}

/* line 57, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_1.big_btn2 {
  padding: 11px 37px;
}

/* line 60, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_1.radius_btn {
  border-radius: 15px;
  font-size: 14px;
  color: #fff;
  font-weight: 400;
  background: #5b6ce1;
  padding: 16px 10px;
}

/* line 67, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_1.radius_btn:hover {
  color: #fff;
  background-color: #5b6ce1;
  box-shadow: 0 3px 6px #cbd1ff;
}

/* line 74, G:/admin_project/8 admin/management_html/scss/_button.scss */
.Euro_btn {
  background-color: #f5f6ff;
  color: #fff;
  display: inline-block;
  padding: 11px 43px;
  text-transform: capitalize;
  line-height: 16px;
  font-size: 23px;
  font-weight: 700;
  border-radius: 5px;
  white-space: nowrap;
  -webkit-transition: 0.5s;
  transition: 0.5s;
  color: #9267ff;
}

/* line 88, G:/admin_project/8 admin/management_html/scss/_button.scss */
.Euro_btn:hover {
  background-color: #9267ff;
  color: #000;
}

/* line 93, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_2 {
  color: #f65365;
  border: 1px solid #f65365;
  display: inline-block;
  padding: 11px 23px;
  text-transform: capitalize;
  line-height: 16px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 5px;
  background-color: transparent;
  white-space: nowrap;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

/* line 106, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_2:hover {
  background-color: #f65365;
  border: 1px solid #f65365;
  color: #fff;
}

/* line 112, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_3 {
  color: #474d58;
  border: 1px solid #e4e8ec;
  display: inline-block;
  padding: 11px 27px;
  text-transform: capitalize;
  line-height: 16px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 5px;
  background-color: #fff;
  white-space: nowrap;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

/* line 125, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_3.border_color_1 {
  border: 1px solid #eee1e2;
}

/* line 129, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_3:hover {
  background-color: #2e4765;
  border: 1px solid #2e4765;
  color: #fff;
}

/* line 135, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_4 {
  display: inline-block;
  border: 1px solid #e4e8ec;
  border-radius: 5px;
  color: #7e7172;
  text-align: center;
  padding: 9px 15px;
  line-height: 20px;
  font-size: 13px;
  font-weight: 300;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

/* line 146, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_4:hover {
  background-color: #2e4765;
  border: 1px solid #2e4765;
  color: #fff;
}

/* line 153, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_5 {
  border: 1px solid #fff;
  display: inline-block;
  padding: 16px 26px !important;
  text-transform: capitalize;
  line-height: 16px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 5px;
  background-color: #fff;
  white-space: nowrap;
  -webkit-transition: 0.5s;
  transition: 0.5s;
  background: #fff !important;
  text-shadow: none !important;
  opacity: 1;
  color: #222222;
}

/* line 169, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_5:hover {
  background-color: #2e4765 !important;
  border: 1px solid #2e4765 !important;
  color: #fff;
  opacity: 1 !important;
}

/* line 175, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_5.small_btn {
  padding: 11px 26px !important;
}

@media (max-width: 575.98px) {

  /* line 175, G:/admin_project/8 admin/management_html/scss/_button.scss */
  .btn_5.small_btn {
    padding: 11px 19px !important;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 175, G:/admin_project/8 admin/management_html/scss/_button.scss */
  .btn_5.small_btn {
    padding: 11px 19px !important;
  }
}

/* line 185, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_6 {
  color: #474d58;
  border: 1px solid #eee1e2;
  display: inline-block;
  padding: 11px 25px;
  text-transform: capitalize;
  line-height: 16px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 5px;
  background-color: #fff;
  white-space: nowrap;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

/* line 198, G:/admin_project/8 admin/management_html/scss/_button.scss */
.btn_6:hover {
  background-color: #2e4765;
  border: 1px solid #2e4765;
  color: #fff;
}

/* line 204, G:/admin_project/8 admin/management_html/scss/_button.scss */
.sm_btn {
  line-height: 15px;
  background-color: #fff;
  height: 34px;
  padding: 9px 20px;
  color: #212e40;
  width: 150px;
  text-transform: capitalize;
  border: 1px solid #eee1e2;
}

/* line 214, G:/admin_project/8 admin/management_html/scss/_button.scss */
.view_archive_btn {
  border: 1px solid #e4e8ec;
  color: #7e7172;
  padding: 16px 30px;
  display: inline-block;
  width: 100%;
  text-align: center;
  line-height: 16px;
  background-color: #fff;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

/* line 224, G:/admin_project/8 admin/management_html/scss/_button.scss */
.view_archive_btn i {
  margin-right: 15px;
}

/* line 227, G:/admin_project/8 admin/management_html/scss/_button.scss */
.view_archive_btn:hover {
  color: #2e4765;
  border: 1px solid #2e4765;
}

/* line 233, G:/admin_project/8 admin/management_html/scss/_button.scss */
.white_btn {
  display: inline-block;
  color: #7e7172;
  font-size: 12px;
  font-weight: 400;
  background: #fff;
  transition: 0.3s;
  padding: 6px 20px;
  border-radius: 3px;
}

/* line 242, G:/admin_project/8 admin/management_html/scss/_button.scss */
.white_btn:hover {
  background: #2d1967;
  color: #fff;
}

/* line 248, G:/admin_project/8 admin/management_html/scss/_button.scss */
.default_btn {
  display: inline-block;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  background: #2d1967;
  transition: 0.3s;
  padding: 15px 25px;
  border-radius: 3px;
}

/* line 257, G:/admin_project/8 admin/management_html/scss/_button.scss */
.default_btn:hover {
  color: #fff;
}

/* line 261, G:/admin_project/8 admin/management_html/scss/_button.scss */
.close.white_btn2 {
  border: 1px solid #fff;
  display: inline-block;
  padding: 16px 26px !important;
  text-transform: capitalize;
  line-height: 16px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 5px;
  background-color: #fff;
  white-space: nowrap;
  -webkit-transition: 0.5s;
  transition: 0.5s;
  background: #fff !important;
  text-shadow: none !important;
  opacity: 1;
  color: #222222;
  margin-right: 10px;
}

/* line 278, G:/admin_project/8 admin/management_html/scss/_button.scss */
.close.white_btn2:hover {
  background-color: #2e4765 !important;
  border: 1px solid #2e4765 !important;
  color: #fff;
  opacity: 1 !important;
}

/* line 285, G:/admin_project/8 admin/management_html/scss/_button.scss */
.white_btn3 {
  border: 1px solid #fff;
  display: inline-block;
  padding: 14px 30px !important;
  text-transform: capitalize;
  line-height: 16px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 5px;
  background-color: #fff;
  white-space: nowrap;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  background: #fff !important;
  text-shadow: none !important;
  opacity: 1;
  color: #101038;
  box-shadow: 0 3px 11px rgba(0, 0, 0, 0.17);
}

/* line 302, G:/admin_project/8 admin/management_html/scss/_button.scss */
.white_btn3:hover {
  background-color: #884ffb !important;
  border: 1px solid #884ffb !important;
  color: #fff;
  opacity: 1 !important;
}

/* line 310, G:/admin_project/8 admin/management_html/scss/_button.scss */
/*.status_btn {
  display: inline-block;
  padding: 2px 15px;
  font-size: 11px;
  font-weight: 300;
  color: #fff !important;
  background: #05d34e;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  border-radius: 30px;
  text-transform: capitalize;
  white-space: nowrap;
  min-width: 70px;
  text-align: center;
}*/

/* line 322, G:/admin_project/8 admin/management_html/scss/_button.scss */
.status_btn.yellow_btn {
  background: #ffba00;
}

/* line 325, G:/admin_project/8 admin/management_html/scss/_button.scss */
.status_btn:hover {
  color: #fff;
}

/* line 330, G:/admin_project/8 admin/management_html/scss/_button.scss */
.dropdown .dropdown-toggle.lms_drop_1 {
  background: #fff;
  padding: 4px 15px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 300;
  text-transform: capitalize;
  text-align: left;
  color: #7e7172;
  display: inline-block;
  width: 102px;
  border: 0;
  border: 1px solid #eee1e2;
  position: relative;
  transition: 0.3s;
}

/* line 345, G:/admin_project/8 admin/management_html/scss/_button.scss */
.dropdown .dropdown-toggle.lms_drop_1::after {
  content: "\f0d7";
  font-family: "Font Awesome 5 Free";
  position: absolute;
  right: 0;
  top: 12px;
  color: #cec1c2;
  font-size: 12px;
  font-weight: 900;
  margin: 0;
  padding: 0;
  border: 0;
  border: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 14px;
}

/* line 362, G:/admin_project/8 admin/management_html/scss/_button.scss */
.dropdown .dropdown-toggle.lms_drop_1.lms_drop_2 {
  min-width: 140px;
}

/* line 367, G:/admin_project/8 admin/management_html/scss/_button.scss */
.dropdown.show .dropdown-toggle.lms_drop_1 {
  background: #2d1967;
  padding: 4px 15px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 300;
  text-transform: capitalize;
  text-align: left;
  color: #7e7172;
  display: inline-block;
  width: 102px;
  border: 0;
  color: #fff;
  border: 1px solid #2d1967;
}

/* line 381, G:/admin_project/8 admin/management_html/scss/_button.scss */
.dropdown.show .dropdown-toggle.lms_drop_1::after {
  content: "\f0d8";
}

/* line 386, G:/admin_project/8 admin/management_html/scss/_button.scss */
.dropdown .dropdown-menu {
  border: 0;
  padding: 6px 0 10px 0;
  margin: 0;
  border-radius: 0 !important;
}

/* line 391, G:/admin_project/8 admin/management_html/scss/_button.scss */
.dropdown .dropdown-menu .dropdown-item {
  font-size: 16px;
  font-weight: 300;
  padding: 4px 20px;
  color: #242934;
}

/* line 396, G:/admin_project/8 admin/management_html/scss/_button.scss */
.dropdown .dropdown-menu .dropdown-item:hover {
  color: #fff !important;
  background: #5b6ce1;
}

/* line 401, G:/admin_project/8 admin/management_html/scss/_button.scss */
.dropdown .dropdown-menu.dropdown-menu-w140 {
  min-width: 140px;
}

/* line 406, G:/admin_project/8 admin/management_html/scss/_button.scss */
.white_btn_line {
  white-space: nowrap;
  height: 50px;
  background: transparent;
  margin-left: 10px;
  font-size: 13px;
  font-weight: 500;
  text-transform: capitalize;
  border: 0;
  padding: 0 19px;
  border-radius: 3px;
  border: 1px solid #eee1e2;
  color: #7e7172;
  transition: 0.3s;
}

/* line 420, G:/admin_project/8 admin/management_html/scss/_button.scss */
.white_btn_line i {
  font-size: 13px;
  color: #7e7172;
  margin-right: 6px;
}

/* line 425, G:/admin_project/8 admin/management_html/scss/_button.scss */
.white_btn_line:hover {
  background: #2d1967;
  color: #fff;
  border: 1px solid #2d1967;
}

/* line 429, G:/admin_project/8 admin/management_html/scss/_button.scss */
.white_btn_line:hover i {
  color: #fff;
  transition: 0.3s;
}

/* line 435, G:/admin_project/8 admin/management_html/scss/_button.scss */
.white_btn_line2 {
  white-space: nowrap;
  height: 30px;
  background: transparent;
  font-size: 12px;
  font-weight: 300;
  display: inline-block;
  text-transform: capitalize;
  border: 0;
  border: 1px solid #eee1e2;
  color: #7e7172;
  transition: 0.3s;
  border-radius: 30px;
  line-height: 28px;
  padding: 0 18px;
}

/* line 450, G:/admin_project/8 admin/management_html/scss/_button.scss */
.white_btn_line2:hover {
  background: #2d1967;
  color: #fff !important;
  border: 1px solid #2d1967;
}

/* line 454, G:/admin_project/8 admin/management_html/scss/_button.scss */
.white_btn_line2:hover i {
  color: #fff;
  transition: 0.3s;
}

/* line 460, G:/admin_project/8 admin/management_html/scss/_button.scss */
.theme_text_btn {
  font-size: 14px;
  color: #46d293;
  font-weight: 700;
}

/* line 464, G:/admin_project/8 admin/management_html/scss/_button.scss */
.theme_text_btn:hover {
  color: #46d293;
}

/* line 469, G:/admin_project/8 admin/management_html/scss/_button.scss */
table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>td:first-child::before,
table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>th:first-child::before {
  z-index: 9;
  top: 50%;
  transform: translateY(-50%);
  content: "\e61a";
  font-family: "themify";
  font-size: 8px;
  line-height: 14px;
  font-weight: 600;
  background-color: #fef1f2;
  box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.08), -6px -6px 12px white;
}

/* line 485, G:/admin_project/8 admin/management_html/scss/_button.scss */
table.dataTable.dtr-inline.collapsed>tbody>tr.parent>td:first-child::before,
table.dataTable.dtr-inline.collapsed>tbody>tr.parent>th:first-child::before {
  content: "\e622";
  box-shadow: inset 6px 6px 12px rgba(0, 0, 0, 0.08), inset -6px -6px 12px white;
}

/* line 499, G:/admin_project/8 admin/management_html/scss/_button.scss */
.CRM_dropdown.dropdown .dropdown-toggle {
  background: transparent;
  color: #415094;
  font-size: 13px;
  font-weight: 500;
  border: 1px solid #7c32ff;
  border-radius: 32px;
  padding: 5px 20px;
  text-transform: uppercase;
  overflow: hidden;
  transition: 0.3s;
}

/* line 510, G:/admin_project/8 admin/management_html/scss/_button.scss */
.CRM_dropdown.dropdown .dropdown-toggle:after {
  content: "\e62a";
  font-family: "themify";
  border: none;
  border-top-color: currentcolor;
  border-top-style: none;
  border-top-width: medium;
  border-top: 0px;
  font-size: 10px;
  position: relative;
  top: 3px;
  left: 0;
  font-weight: 600;
  transition: 0.3s;
}

/* line 525, G:/admin_project/8 admin/management_html/scss/_button.scss */
.CRM_dropdown.dropdown .dropdown-toggle:hover,
.CRM_dropdown.dropdown .dropdown-toggle:focus {
  color: #fff;
  border: 1px solid transparent;
  box-shadow: none;
}

/* line 540, G:/admin_project/8 admin/management_html/scss/_button.scss */
.CRM_dropdown.dropdown .dropdown-menu {
  border-radius: 5px 5px 10px 10px;
  border: 0px;
  padding: 15px 0px;
  box-shadow: 0px 10px 20px rgba(108, 39, 255, 0.3);
}

/* line 545, G:/admin_project/8 admin/management_html/scss/_button.scss */
.CRM_dropdown.dropdown .dropdown-menu .dropdown-item {
  color: #828bb2;
  text-align: right;
  font-size: 12px;
  padding: 4px 1.5rem;
  text-transform: uppercase;
  cursor: pointer;
}

/* line 552, G:/admin_project/8 admin/management_html/scss/_button.scss */
.CRM_dropdown.dropdown .dropdown-menu .dropdown-item:hover {
  color: #2e4765;
}

/* line 559, G:/admin_project/8 admin/management_html/scss/_button.scss */
.dropdown-menu.option_width_8 {
  min-width: 150px;
}

/* line 562, G:/admin_project/8 admin/management_html/scss/_button.scss */
.action_btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: transparent;
  line-height: 30px;
  text-align: center;
  color: #67349d;
  font-size: 12px;
  transition: 0.3s;
  display: inline-block;
  flex: 32px 0 0;
}

/* line 574, G:/admin_project/8 admin/management_html/scss/_button.scss */
.action_btn:hover {
  background: #67349d;
  color: #fff;
  box-shadow: 0 5px 10px rgba(136, 79, 251, 0.26);
}

/* line 581, G:/admin_project/8 admin/management_html/scss/_button.scss */
.color_button {
  background-color: #8950fc;
  border: 0;
  color: #fff;
  display: inline-block;
  padding: 13px 25px;
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  white-space: nowrap;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

/* line 593, G:/admin_project/8 admin/management_html/scss/_button.scss */
.color_button.color_button2 {
  background: #9afdd2;
  color: #313131;
}

/* line 596, G:/admin_project/8 admin/management_html/scss/_button.scss */
.color_button.color_button2:hover {
  background: #9afdd2;
  color: #313131;
}

/* line 601, G:/admin_project/8 admin/management_html/scss/_button.scss */
.color_button.color_button3 {
  background: #ffca60;
  color: #313131;
}

/* line 604, G:/admin_project/8 admin/management_html/scss/_button.scss */
.color_button.color_button3:hover {
  background: #ffca60;
  color: #313131;
}

/* line 609, G:/admin_project/8 admin/management_html/scss/_button.scss */
.color_button:hover {
  background-color: #8950fc;
  color: #fff;
}

/* line 614, G:/admin_project/8 admin/management_html/scss/_button.scss */
.export_btn {
  background: #ff7a88;
  font-size: 16px;
  font-weight: 400;
  color: #fff;
  border-radius: 4px;
  padding: 10px 30px;
  display: inline-block;
  border: 0;
}

/* line 623, G:/admin_project/8 admin/management_html/scss/_button.scss */
.export_btn:focus {
  outline: none;
}

/******** header css here *********/

/* line 29, G:/admin_project/8 admin/management_html/scss/_header.scss */
.header_iner.default_header {
  background: #f65365;
  border-radius: 0;
}

@media (max-width: 575.98px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar {
    justify-content: flex-end !important;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar {
    justify-content: flex-end !important;
  }
}

@media (max-width: 991px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar {
    padding: 20px;
    margin: 0;
  }
}

@media (max-width: 575.98px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar {
    margin: 0;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar {
    margin: 0;
  }
}

/* line 74, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .page_tittle h3 {
  font-size: 18px;
  font-weight: 500;
  text-transform: capitalize;
  color: #222222;
  margin-bottom: 2px;
}

@media (max-width: 991px) {

  /* line 74, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar .page_tittle h3 {
    margin-right: 15px;
  }
}

/* line 84, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .page_tittle .page_subtitle {
  font-size: 12px;
  font-weight: 300;
  color: #7e7172;
  margin-bottom: 0;
}

/* line 89, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .page_tittle .page_subtitle .page_subtitle_inenr {
  color: #7e7172;
}

/* line 91, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .page_tittle .page_subtitle .page_subtitle_inenr.active_subcat {
  color: #fe1724;
  white-space: nowrap;
}

/* line 95, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .page_tittle .page_subtitle .page_subtitle_inenr i {
  margin-left: 10px;
  margin-right: 7px;
  font-size: 12px;
}

/* line 100, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .page_tittle .page_subtitle .page_subtitle_inenr svg {
  position: relative;
  margin: 0 7px 0 10px;
  top: -1px;
}

@media (max-width: 575.98px) {

  /* line 100, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar .page_tittle .page_subtitle .page_subtitle_inenr svg {
    margin: 0 2px 0 2px;
  }
}

/* line 112, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .btn_1 {
  margin: 0 30px;
}

@media (max-width: 991px) {

  /* line 115, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar .notification {
    margin: 0 10px;
  }
}

/* line 119, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .notification i {
  color: #707070;
  position: relative;
  font-size: 20px;
}

/* line 123, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .notification i:after {
  position: absolute;
  content: "";
  right: -1px;
  top: 0;
  height: 9px;
  width: 9px;
  background-color: #2e4765;
  border-radius: 50%;
}

/* line 135, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .serach_field-area {
  margin-right: auto;
  margin-left: 0px;
  width: 268px;
}

@media (max-width: 767.98px) {

  /* line 135, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar .serach_field-area {
    display: none !important;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {

  /* line 135, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar .serach_field-area {
    width: auto;
    left: 40px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {

  /* line 135, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar .serach_field-area {
    width: auto;
    margin-right: 70px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 135, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar .serach_field-area {
    width: auto;
    margin-right: 70px;
  }
}

/* line 156, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .header_notification_warp {
  margin-right: 50px;
  margin-left: 40px;
}

@media (max-width: 575.98px) {

  /* line 156, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar .header_notification_warp {
    margin-right: 25px;
    margin-left: 20px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {

  /* line 156, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar .header_notification_warp {
    margin-right: 35px;
    margin-left: 25px;
  }
}

/* line 167, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .header_notification_warp {
  display: inline-block;
  position: relative;
}

/* line 170, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .header_notification_warp .serach_button {
  width: 32px;
  height: 32px;
  background: #884ffb;
  border-radius: 50%;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
  margin-right: 20px;
  position: relative;
}

/* line 180, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .header_notification_warp .serach_button i {
  color: #fff;
  font-size: 16px;
  line-height: 32px;
}

/* line 185, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .header_notification_warp .serach_button .serach_field-area {
  position: absolute;
  right: 0;
  top: 0px;
  top: 0px;
  background: #fff;
  border-radius: 10px;
  opacity: 0;
  transition: 0.3s;
  visibility: hidden;
  right: 50px;
  top: -4px;
}

/* line 197, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .header_notification_warp .serach_button .serach_field-area.active {
  opacity: 1;
  visibility: visible;
}

/* line 201, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .header_notification_warp .serach_button .serach_field-area i {
  color: rgba(137, 80, 252, 0.7);
}

/* line 204, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .header_notification_warp .serach_button .serach_field-area input {
  border-radius: 10px;
}

/* line 214, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .header_notification_warp a {
  margin-right: 0;
}

/* line 217, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .header_notification_warp>a {
  position: relative;
  margin-right: 0px;
}

/* line 220, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .header_notification_warp>a img {
  width: 17px;
}

/* line 223, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .header_notification_warp>a span {
  position: absolute;
  width: 22px;
  height: 22px;
  text-align: center;
  color: #fff;
  display: inline-block;
  background: #fff;
  font-size: 11px;
  border-radius: 50%;
  right: -3px;
  top: -6px;
  background: #f45b0f;
  line-height: 22px;
}

@media (max-width: 575.98px) {

  /* line 242, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar .select_style {
    display: none !important;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 242, G:/admin_project/8 admin/management_html/scss/_header.scss */
  #wpadminbar .select_style {
    display: none !important;
  }
}

/* line 251, G:/admin_project/8 admin/management_html/scss/_header.scss */
#wpadminbar .nice_Select {
  color: #929bb5;
}

/* line 255, G:/admin_project/8 admin/management_html/scss/_header.scss */
.profile_info {
  position: relative;
  display: flex;
}

/* line 259, G:/admin_project/8 admin/management_html/scss/_header.scss */
.profile_info img {
  max-width: 42px;
  max-height: 42px;
  border-radius: 50%;
  border: 2px solid #94c0d4;
  cursor: pointer;
}

@media (max-width: 991px) {

  /* line 259, G:/admin_project/8 admin/management_html/scss/_header.scss */
  .profile_info img {
    max-width: 30px;
  }
}

/* line 269, G:/admin_project/8 admin/management_html/scss/_header.scss */

/* line 282, G:/admin_project/8 admin/management_html/scss/_header.scss */
.profile_info_iner::before {
  position: absolute;
  content: "";
  width: 0;
  height: 0;
  border-style: solid;
  top: -10px;
  right: 15px;
  border-width: 0 11px 12px 11px;
  border-color: transparent transparent #fff transparent;
  z-index: 2;
}

/* line 294, G:/admin_project/8 admin/management_html/scss/_header.scss */
.profile_info_iner .profile_author_name {
  padding: 20px 25px;
  background: #567aed;
  border-radius: 10px 15px 0px 0px;
}

/* line 300, G:/admin_project/8 admin/management_html/scss/_header.scss */
.profile_info .profile_info_iner a {
  display: block;
}

/* line 304, G:/admin_project/8 admin/management_html/scss/_header.scss */
.profile_info .profile_info_iner p {
  font-size: 16px;
  font-weight: 400;
  color: #fff;
}

/* line 309, G:/admin_project/8 admin/management_html/scss/_header.scss */
.profile_info .profile_info_iner h5 {
  font-size: 20px;
  color: #fff;
  font-weight: 600;
}

/* line 316, G:/admin_project/8 admin/management_html/scss/_header.scss */

/* line 322, G:/admin_project/8 admin/management_html/scss/_header.scss */
.profile_info .profile_info_details {
  padding: 20px 25px;
}

/* line 324, G:/admin_project/8 admin/management_html/scss/_header.scss */
.profile_info .profile_info_details a {
  color: #2e4765 !important;
  font-size: 14px !important;
  display: block;
  padding: 5px 0 !important;
  font-weight: 400 !important;
}

/* line 330, G:/admin_project/8 admin/management_html/scss/_header.scss */
.profile_info .profile_info_details a:hover {
  color: #2e4765;
}

/* line 332, G:/admin_project/8 admin/management_html/scss/_header.scss */
.profile_info .profile_info_details a:hover i {
  color: #2e4765;
}

/* line 337, G:/admin_project/8 admin/management_html/scss/_header.scss */
.profile_info .profile_info_details i {
  color: #2e4765;
  font-size: 12px;
  margin-left: 20px;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

/* line 345, G:/admin_project/8 admin/management_html/scss/_header.scss */
.sidebar_icon {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  right: auto;
  left: 20px;
}

@media (max-width: 575.98px) {

  /* line 345, G:/admin_project/8 admin/management_html/scss/_header.scss */
  .sidebar_icon {
    right: auto;
    left: 20px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 345, G:/admin_project/8 admin/management_html/scss/_header.scss */
  .sidebar_icon {
    right: auto;
    left: 20px;
  }
}

/* line 360, G:/admin_project/8 admin/management_html/scss/_header.scss */
.sidebar_icon i {
  font-size: 20px;
  color: #000;
}

/******** header css end *********/
/* line 370, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field-area {
  width: 268px !important;
  position: relative !important;
  background: #ffffff;
  border-radius: 6px !important;
}

/* line 376, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field-area.theme_bg {
  background: #f6f7fb;
}

/* line 378, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field-area.theme_bg input {
  background: transparent !important;
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 370, G:/admin_project/8 admin/management_html/scss/_header.scss */
  .serach_field-area {
    width: 268px;
  }
}

/* line 385, G:/admin_project/8 admin/management_html/scss/_header.scss */

/* line 387, G:/admin_project/8 admin/management_html/scss/_header.scss */

/* line 400, G:/admin_project/8 admin/management_html/scss/_header.scss */

/* line 406, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field-area .search_inner input:focus {
  outline: none;
}

/* line 410, G:/admin_project/8 admin/management_html/scss/_header.scss */

/* line 422, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field-area .search_inner button img {
  position: relative;
  top: -2px;
}

/* line 426, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field-area .search_inner button i {
  font-size: 14px;
  color: #818e94;
}

/* line 435, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field-area2 {
  width: 480px;
  position: relative;
}

@media (max-width: 991px) {

  /* line 435, G:/admin_project/8 admin/management_html/scss/_header.scss */
  .serach_field-area2 {
    width: auto;
  }
}

/* line 444, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field-area2 .search_inner input {
  color: #000;
  font-size: 17px;
  height: 55px;
  width: 100%;
  padding-left: 82px;
  border: 0;
  padding-right: 15px;
  border-bottom: 1px solid #f4f7fc;
  background: #f5f7fd;
  border-radius: 30px;
}

/* line 455, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field-area2 .search_inner input::placeholder {
  font-size: 16px;
  font-weight: 400;
  color: #818e94;
  font-family: "Mulish", sans-serif;
}

/* line 461, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field-area2 .search_inner input:focus {
  outline: none;
}

/* line 465, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field-area2 .search_inner button {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: transparent;
  font-size: 12px;
  border: 0;
  padding-left: 40px;
  padding-right: 11px;
}

/* line 475, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field-area2 .search_inner button i {
  font-size: 14px;
  color: #818e94;
}

/* line 485, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field_2 {
  width: 430px;
  position: relative;
  margin-right: 0px;
}

@media (max-width: 575.98px) {

  /* line 485, G:/admin_project/8 admin/management_html/scss/_header.scss */
  .serach_field_2 {
    width: 100%;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 485, G:/admin_project/8 admin/management_html/scss/_header.scss */
  .serach_field_2 {
    width: 190px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {

  /* line 485, G:/admin_project/8 admin/management_html/scss/_header.scss */
  .serach_field_2 {
    width: 230px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {

  /* line 485, G:/admin_project/8 admin/management_html/scss/_header.scss */
  .serach_field_2 {
    width: 300px;
  }
}

/* line 502, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field_2 .search_inner input {
  color: #000;
  font-size: 13px;
  height: 40px;
  width: 100%;
  border-radius: 5px;
  padding-left: 55px;
  border: 1px solid #2d1967;
  padding-right: 15px;
}

/* line 511, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field_2 .search_inner input::placeholder {
  font-size: 13px;
  font-weight: 300;
  color: #7e7172;
}

/* line 516, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field_2 .search_inner input:focus {
  outline: none;
}

/* line 520, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field_2 .search_inner button {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: transparent;
  font-size: 12px;
  border: 0;
  padding-left: 19px;
  padding-right: 11px;
}

.serach_field-area .search_inner {
  position: relative !important;
}

/* line 531, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field_2 .search_inner button i {
  font-size: 12px;
  color: #2d1967;
}

/* line 535, G:/admin_project/8 admin/management_html/scss/_header.scss */
.serach_field_2 .search_inner button::before {
  position: absolute;
  width: 1px;
  height: 24px;
  content: "";
  background: #2d1967;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 0;
}

/* line 553, G:/admin_project/8 admin/management_html/scss/_header.scss */
table.dataTable {
  width: 100% !important;
  margin: 0 auto;
  clear: both;
  border-collapse: separate;
  border-spacing: 0;
}

/* line 566, G:/admin_project/8 admin/management_html/scss/_header.scss */
.table td,
.table th {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 0 !important;
}

/* line 574, G:/admin_project/8 admin/management_html/scss/_header.scss */
#calendar {
  width: 100%;
  height: 800px;
}

/* line 580, G:/admin_project/8 admin/management_html/scss/_header.scss */
.fc-day-grid-event .fc-content {
  white-space: nowrap;
  overflow: hidden;
  height: 25px;
  line-height: 25px;
  padding: 0 10px 0 10px;
  color: #fff;
  font-size: 12px;
  text-transform: capitalize;
  font-weight: 700;
}

/************ sidebar css here ************/
/* line 2, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain {
  height: 100vh;
  width: 270px;
  left: 0;
  top: 0;
  padding-bottom: 50px;
  position: fixed;
  z-index: 999;
  overflow: hidden;
  overflow-y: auto;
  background: #ffffff;
  box-shadow: 0 12px 30px rgba(80, 143, 244, 0.1);
  transition: 0.3s;
}

.sidebar.mini_sidebar {
  position: absolute !important;
  width: 136px;
  overflow: visible !important;
  min-width: 136px;
  max-width: 136px;
  z-index: 9999999 !important;
  height: auto !important;
  top: 0 !important;
  bottom: 0;
  left: 0 !important;
}

/* line 18, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar .small_logo {
  display: none;
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 21, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
  .sidebar.hide_vertical_menu {
    left: -20%;
  }
}

@media (min-width: 1201px) {

  /* line 21, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
  .sidebar.hide_vertical_menu {
    left: -270px;
  }
}

@media (max-width: 991px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
  #adminmenumain {
    left: -280px;
    -webkit-transition: 0.5s;
    transition: 0.5s;
    top: 0;
    width: 280px;
    background: #fff;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
  #adminmenumain {
    width: 20%;
  }
}

@media (max-width: 991px) {

  /* line 40, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
  #adminmenumain .logo {
    padding: 30px;
  }
}

/* line 55, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .menu-text {
  font-size: 12px;
  color: #b1bdcb;
  font-weight: 600;
  margin: 0;
  padding: 12px 0 12px 24px;
  text-transform: uppercase;
}

/* line 62, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .menu-text i {
  display: none;
}

/* line 67, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .single_sidebar_tittle i {
  font-size: 18px;
  margin-right: 12px;
  color: #f8dbdd;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

/* line 73, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .single_sidebar_tittle a {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #222222;
  padding: 10px 25px 10px 40px;
  width: 100%;
  display: flex;
  align-items: center;
  transition: 0.3s;
  position: relative;
  z-index: 0;
  border-top: 1px solid transparent;
  background: transparent;
}

/* line 88, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .single_sidebar_tittle a::before {
  position: absolute;
  right: 0;
  background: #2e4765;
  width: 0px;
  top: 0;
  bottom: 0;
  content: "";
  opacity: 0;
  transition: 0.3s;
  right: 0px;
  visibility: hidden;
}

/* line 102, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .single_sidebar_tittle a.current_active i {
  color: #2e4765;
}

/* line 105, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .single_sidebar_tittle a.current_active::before {
  opacity: 1;
  width: 3px;
  visibility: visible;
}

/* line 111, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .single_sidebar_tittle a:hover::before {
  opacity: 1;
  right: 0;
  visibility: visible;
  width: 3px;
}

/* line 117, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .single_sidebar_tittle a.normal_title {
  padding: 28px 25px 18px 40px;
  background: #fff7f8;
  border-top: 1px solid #eee1e2;
}

/* line 121, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .single_sidebar_tittle a.normal_title:after {
  position: absolute;
  right: 40px;
  top: 55%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  content: "\f0d8";
  font-family: "Font Awesome 5 Free";
  color: #cec1c2;
  font-weight: 600;
  font-size: 14px;
}

/* line 132, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .single_sidebar_tittle a.normal_title.collapsed {
  padding: 10px 25px 10px 40px;
  background: #fff;
  border-top: 1px solid transparent;
}

/* line 136, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .single_sidebar_tittle a.normal_title.collapsed:after {
  position: absolute;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  content: "\f0d7";
  font-family: "Font Awesome 5 Free";
  color: #cec1c2;
  font-weight: 600;
  font-size: 14px;
}

/* line 150, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .single_sidebar_tittle:hover {
  background-color: #192434;
}

/* line 152, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .single_sidebar_tittle:hover i {
  color: #2e4765;
}

/* line 157, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .single_menu_item {
  padding-left: 36px;
  margin: 6px 0;
  line-height: 21px;
}

/* line 161, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .single_menu_item:hover {
  padding-left: 55px !important;
}

/* line 167, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .sidebar_iner ul li {
  list-style: none;
}

/* line 169, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .sidebar_iner ul li .submenu {
  background: #fff7f8;
  padding: 0px 0px 0px 70px !important;
  border-bottom: 1px solid #eee1e2;
}

/* line 174, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .sidebar_iner ul li .submenu li a {
  font-size: 13px;
  line-height: 36px;
  color: #7e7172;
  font-weight: 400;
}

/* line 180, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain .sidebar_iner ul li .submenu li:last-child {
  padding-bottom: 18px;
}

@media (max-width: 991px) {

  /* line 193, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
  .active_sidebar {
    left: 0 !important;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2) !important;
  }
}

/* line 198, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar_close_icon i {
  width: 40px;
  height: 40px;
  display: inline-block;
  text-align: center;
  line-height: 40px;
  position: absolute;
  right: 30px;
  font-size: 25px;
  top: 29px;
  cursor: pointer;
  color: #222222;
}

/* line 213, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar_widget .white_box {
  padding: 30px;
}

@media (max-width: 991px) {

  /* line 213, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
  .sidebar_widget .white_box {
    padding: 25px 15px;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1439px) {

  /* line 213, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
  .sidebar_widget .white_box {
    padding: 15px;
  }
}

@media only screen and (min-width: 1440px) and (max-width: 1679px) {

  /* line 213, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
  .sidebar_widget .white_box {
    padding: 25px 15px;
  }
}

/* line 224, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar_widget .white_box .btn_2 {
  margin-top: 25px;
  width: 100%;
  text-align: center;
  padding: 16px 25px;
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 224, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
  .sidebar_widget .white_box .btn_2 {
    padding: 12px 7px;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1439px) {

  /* line 233, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
  .sidebar_widget .white_box .btn_1 {
    padding: 8px 10px;
    font-size: 11px;
  }
}

/* line 240, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar_widget .date_range {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 6px 0 18px;
}

/* line 245, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar_widget .date_range .btn_3 {
  border-radius: 5px;
  font-size: 13px;
  font-weight: 300;
}

/* line 250, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar_widget .date_range i {
  display: none;
}

/* line 253, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar_widget .date_range .start_date,
.sidebar_widget .date_range .end_date {
  width: 115px;
}

/* line 255, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar_widget .date_range .start_date input,
.sidebar_widget .date_range .end_date input {
  width: 100%;
  border: 1px solid #e4e8ec;
  color: #7e7172;
  font-size: 13px;
  padding: 11px 20px;
  border-radius: 5px;
  font-weight: 300;
}

/* line 264, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar_widget .date_range .start_date ::placeholder,
.sidebar_widget .date_range .end_date ::placeholder {
  color: #7e7172;
}

/************ sidebar css end ************/
/* line 275, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li {
  margin: 12px 16px;
}

/*.sidebar::after{
      font-family: "Font Awesome 5 Free"; font-weight: 900; content: "\f104";

}*/

/* line 278, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li a {
  font-size: 15px;
  font-weight: 400;
  color: #144339;
  padding: 0px 25px 0px 0px;
  background: #fff;
  transition: 0.3s;
  position: relative;
  z-index: 0;
  display: grid;
  grid-template-columns: 34px auto;
  grid-gap: 15px;
  align-items: center;
  position: relative;
  background: transparent;
  color: #8890b5;
  font-family: "Mulish", sans-serif;
  padding: 14px 10px 14px 20px;
  background: #fff;
  border-radius: 0 30px 30px 0;
  border-radius: 15px;
}

#adminmenu div.wp-menu-image {
  width: 17px !important;
  height: auto !important;
  margin: 0;
  text-align: center;
  margin-top: -8px;
  position: relative !important;
}

/* line 299, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li a:hover {
  color: #fff !important;
  /*background: #F6F7FB;*/
  background: transparent linear-gradient(270deg, #1658a5 0%, #0089c2 100%) 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 10px #0000000d;
  border-radius: 10px;
}

/* line 303, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
/* .sidebar #adminmenu > li a.active {
    color: #ffff;
    background: transparent linear-gradient(
270deg
, #1658A5 0%, #0089C2 100%) 0% 0% no-repeat padding-box;
  border-radius: 15px;
  box-shadow: 4px 4px 15px 0px #0089c2b0;
} */

/* line 332, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li a i {
  font-size: 15px;
  margin-right: 12px;
  color: #a5adc6;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

/* line 345, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li a:hover::before {
  width: 6px;
  opacity: 1;
  visibility: visible;
}

/* line 353, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li>a {
  color: #144339;
}

/* line 358, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li.mm-active>a {
  background: #fff;
  border-radius: 30px;
  color: #ffffff;
  border-radius: 4px 4px 0 0;
  background: transparent;
}

/* line 364, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li.mm-active>a::after {
  color: #8890b5;
  transform: rotate(0deg) translate(0, -50%) !important;
  opacity: 1;
  font-size: 10px;
  right: 15px;
  /*top:70%;*/
}

/* line 373, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li.mm-active.metis_dropmenu>a {
  padding-top: 29px;
  padding-bottom: 30px;
  color: #a5adc6;
}

/* line 380, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li.mm-active>a i {
  color: #a5adc6;
}

/* line 383, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li.mm-active>a::before {
  opacity: 1;
  width: 6px;
  visibility: visible;
}

/* line 390, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li ul {
  padding: 0px 0px 5px 40px;
  background: transparent;
  border-radius: 0 0 4px 4px;
  margin-left: 30px;
  width: 200px;
  /*background: #fff;*/
}

/* line 403, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li ul li:last-child a {
  padding-bottom: 30px;
}

/* line 407, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li ul li a {
  font-size: 14px;
  color: #8890b5;
  font-weight: 400;
  padding: 0;
  display: block;
  padding: 0;
  padding: 10px 0 10px 8px !important;
  margin-top: 0;
  margin-left: 0;
  text-transform: capitalize;
}

/* line 418, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li ul li a::before {
  display: none;
}

/* line 421, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li ul li a.active,
#adminmenumain #adminmenu>li ul li a.current {
  color: #f65365 !important;
}

#adminmenumain #adminmenu>li a.active,
#adminmenumain #adminmenu>li a.current {
  background: transparent linear-gradient(270deg, #1658a5 0%, #0089c2 100%) 0% 0% no-repeat padding-box;
  box-shadow: 4px 4px 15px 0px #0089c2b0;
  color: #fff;
}

/* line 426, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu>li ul li ul {
  padding-left: 5px;
}

/* line 436, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.admin_profile_Wrap {
  margin-bottom: 30px;
}

/* line 440, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.admin_profile_Wrap ul li {
  background: #fff7f8;
  border-top: 1px solid #eee1e2;
  border-bottom: 1px solid #eee1e2;
  padding: 30px 0 30px 30px;
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 440, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
  .admin_profile_Wrap ul li {
    padding: 30px 0 30px 20px;
  }
}

/* line 451, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.admin_profile_Wrap ul li a {
  display: block;
}

/* line 456, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.admin_profile_Wrap ul li a .admin_profile_inner .thumb img {
  width: 50px;
  height: 50px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}

/* line 462, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.admin_profile_Wrap ul li a .admin_profile_inner .welcome_rext {
  margin-left: 15px;
}

/* line 464, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.admin_profile_Wrap ul li a .admin_profile_inner .welcome_rext h5 {
  font-size: 14px;
  font-weight: 500;
  color: #222222;
  margin-bottom: 1px;
  white-space: nowrap;
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 464, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
  .admin_profile_Wrap ul li a .admin_profile_inner .welcome_rext h5 {
    font-size: 12px;
  }
}

/* line 474, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.admin_profile_Wrap ul li a .admin_profile_inner .welcome_rext span {
  display: block;
  font-size: 12px;
  color: #7e7172;
  font-weight: 300;
  white-space: nowrap;
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 474, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
  .admin_profile_Wrap ul li a .admin_profile_inner .welcome_rext span {
    font-size: 11px;
  }
}

/* line 487, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.admin_profile_Wrap ul li ul {
  padding-left: 70px;
}

/* line 490, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.admin_profile_Wrap ul li ul li {
  padding: 0;
  border: 0;
}

/* line 493, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.admin_profile_Wrap ul li ul li a {
  font-size: 13px;
  color: #7e7172;
  font-weight: 400;
  padding-bottom: 17px;
}

/* line 500, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.admin_profile_Wrap ul li ul li:last-child a {
  padding-bottom: 0;
}

/* line 505, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.admin_profile_Wrap ul li ul li:first-child a {
  margin-top: 29px;
}

/* line 513, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.admin_profile_Wrap .metismenu .wp-has-submenu::after {
  top: 38%;
}

/* line 520, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain::-webkit-scrollbar-track,
body::-webkit-scrollbar-track {
  background-color: #ddd;
}

/* line 525, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain::-webkit-scrollbar,
body::-webkit-scrollbar {
  width: 6px;
  background-color: #ddd;
}

/* line 530, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain::-webkit-scrollbar-thumb,
body::-webkit-scrollbar-thumb {
  background-color: #f8dbdd;
}

/* line 536, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
body::-webkit-scrollbar {
  width: 8px;
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 543, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
  .metismenu .wp-has-submenu::after {
    right: 25px;
  }
}

/* line 549, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu li a img {
  width: 17px;
  height: auto;
}

/* line 555, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
#adminmenumain #adminmenu li a .icon_menu {
  width: 34px;
  height: 34px;
  text-align: center;
  line-height: 32px;
}

/* line 567, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar.dark_sidebar {
  /*background: #1e1e2d;*/
  background: transparent linear-gradient(172deg, #1758a8 0%, #0887be 100%) 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 10px #0000000d;
}

/* line 569, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar.dark_sidebar .logo {
  background: transparent;
}

/* line 572, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar.dark_sidebar #adminmenu>li ul {
  background: #1e1e2d;
}

/* line 575, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar.dark_sidebar #adminmenu>li.mm-active>a {
  background: #1e1e2d;
}

/* line 578, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar.dark_sidebar #adminmenu>li>a {
  color: #fff;
  background: transparent;
}

/* line 582, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar.dark_sidebar #adminmenu>li ul li a.active {
  background: transparent;
  color: #f65365 !important;
}

/* line 586, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar.dark_sidebar #adminmenu>li ul li a {
  color: #fff;
  background: transparent;
}

/* line 590, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar.dark_sidebar #adminmenu>li {
  margin-left: 0px;
  margin-right: 20px;
}

/* line 594, G:/admin_project/8 admin/management_html/scss/_sidebar.scss */
.sidebar.dark_sidebar #adminmenu>li a::after {
  color: #fff;
}

/* line 1, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.body_content {
  display: flex;
}

/* line 6, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.main-title h3 {
  font-family: "Mulish", sans-serif;
  font-weight: 700;
  font-size: 18px;
  color: #fff;
}

/* line 15, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.main-title span {
  font-size: 12px;
  font-weight: 400;
  color: #757575;
  display: block;
  margin-top: 6px;
}

.data_container {
  padding: 0px 15px 0px 38px;
}

/* line 23, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.box_header {
  display: flex;
  /* justify-content: space-between; */
  /*margin-bottom: 30px;*/
  align-items: center;
  padding: 0px 0px 0px 0px;
}

.table_tab {
  color: #ffff !important;
  width: 111px;
  text-align: center;
  padding: 0px 10px;
  border: 1px solid transparent;
}

.echecks_page_wc-reports .table_tab {
  padding: 0px 3px !important;
}

.table_tab:hover {
  border: 1px solid #fff;
  padding: 0px 11px;
  border-radius: 11px;
  color: #ffff;
  cursor: pointer;
}

.table_tab_active {
  border: 1px solid #fff;
  padding: 0px 11px;
  border-radius: 11px;
  color: #ffff;
  cursor: pointer;
}

@media (max-width: 575.98px) {

  /* line 28, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
  .box_header.box_header_block {
    flex-direction: column;
    align-items: flex-start !important;
  }
}

@media (max-width: 575.98px) {

  /* line 33, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
  .box_header.box_header_block .main-title {
    margin-bottom: 15px;
  }
}

/* line 40, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.box_header .title_info p {
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 0;
  color: #a3a0fb;
}

/* line 50, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.legend_style li {
  display: inline-block;
  font-size: 13px;
  font-weight: 400;
  text-transform: capitalize;
  margin-right: 10px;
}

/* line 56, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.legend_style li span {
  display: inline-block;
  width: 9px;
  height: 9px;
  border-radius: 50%;
  background: #4400eb;
  margin-right: 4px;
}

/* line 64, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.legend_style li.inactive {
  opacity: 0.29;
}

/* line 69, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.title_btn {
  margin-bottom: 20px;
}

/* line 72, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.title_btn ul li {
  display: inline-block;
}

/* line 74, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.title_btn ul li a {
  display: inline-block;
  padding: 4px;
  background: #fff;
  font-size: 11px;
  text-transform: capitalize;
  color: #706f9a;
  border-radius: 5px;
  line-height: 25px;
  padding: 0 10px;
  transition: 0.3s;
}

/* line 85, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.title_btn ul li a:hover {
  background: #a66dd4;
  color: #fff;
}

/* line 89, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.title_btn ul li a.active {
  background: #a66dd4;
  color: #fff;
}

/* line 99, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.radial_footer .radial_footer_inner .left_footer {
  padding-left: 10px;
  margin-bottom: 20px;
}

/* line 102, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.radial_footer .radial_footer_inner .left_footer h5 {
  font-size: 13px;
  color: #43425d;
  font-weight: 500;
  margin-bottom: 0px;
  position: relative;
}

/* line 108, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.radial_footer .radial_footer_inner .left_footer h5 span {
  width: 9px;
  height: 9px;
  border-radius: 50%;
  background: #a4a1fb;
  display: inline-block;
  position: absolute;
  left: -15px;
  top: 6px;
}

/* line 119, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.radial_footer .radial_footer_inner .left_footer p {
  font-size: 12px;
  color: #4d4f5c;
  opacity: 0.5;
  font-weight: 400;
  margin-bottom: 0;
}

/* line 128, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.radial_footer .radial_bottom {
  border-top: 1px solid #efefef;
  text-align: center;
  padding-top: 15px;
  margin-top: 4px;
}

/* line 134, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.radial_footer .radial_bottom p a {
  font-size: 12px;
  color: #3b86ff;
  font-weight: 600;
}

/* line 143, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.blud_card {
  background: #4567ee !important;
  background-repeat: no-repeat;
  background-position: bottom;
}

/* line 147, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.blud_card p {
  color: #bcd3ff;
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 20px;
  margin-top: 10px;
}

/* line 154, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.blud_card img {
  width: 100%;
}

/* line 161, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.single_user_pil {
  background: #f5f5ff;
  padding: 10px 20px;
  border-radius: 15px;
  transition: 0.3s;
  margin-bottom: 15px;
}

/* line 167, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.single_user_pil.admin_bg {
  background: #f5fdff;
}

/* line 170, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.single_user_pil:hover {
  background: #f5fdff;
}

/* line 174, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.single_user_pil .user_pils_thumb img {
  border: 1px solid #94c0d4;
}

/* line 178, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.single_user_pil .user_info {
  font-size: 14px;
  font-weight: 400;
  color: #b5bacd;
}

/* line 185, G:/admin_project/8 admin/management_html/scss/_body_content.scss */
.min_height_oveflow {
  height: 370px;
  overflow-x: hidden;
  overflow-y: auto;
}

/******** main content css here *********/
/* line 2, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
#wpcontent {
  width: 100%;
  padding-top: 0px !important;
  transition: 0.5s;
  position: relative;
  min-height: 100vh;
  padding-bottom: 70px;
  background: #dce9f4;
  margin-left: 0px !important;
  height: auto;
  padding-left: 285px !important;
}

/* line 13, G:/admin_project/8 admin/management_html/scss/_main_content.scss */

/* line 17, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.main_content.main_content_padding_hide {
  padding-left: 0px;
  padding-top: 100px !important;
}

@media (max-width: 991px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  .main_content {
    padding: 90px 0 100px 0;
  }
}

@media (max-width: 575.98px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  #wpcontent {
    padding: 120px 0 100px 0;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  #wpcontent {
    padding: 120px 0 100px 0;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  #wpcontent {
    padding: 120px 0 100px 0;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  #wpcontent {
    padding-left: 20%;
  }
}

/* line 39, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.main_content.default_content .main_content_iner {
  margin: 0;
  border-radius: 0;
}

/* line 44, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
#wpcontent .main_content_iner {
  min-height: 68vh;
  transition: 0.5s;
  position: relative;
  /*background: #F6F7FB;*/
  margin: 0;
  z-index: 22;
  border-radius: 0px;
}

/* line 54, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.main_content .main_content_iner.default_main_contaner_iner {
  background: #ffffff;
}

/* line 56, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.main_content .main_content_iner.default_main_contaner_iner::before {
  display: none;
}

@media (max-width: 991px) {

  /* line 44, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  .main_content .main_content_iner {
    margin: 0;
    padding: 15px;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {

  /* line 79, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  .main_content .main_content_iner.main_content_iner_padding {
    padding: 0 30px 0 30px;
  }
}

@media only screen and (min-width: 1440px) and (max-width: 1679px) {

  /* line 79, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  .main_content .main_content_iner.main_content_iner_padding {
    padding: 0 60px 0 60px;
  }
}

@media (min-width: 1680px) {

  /* line 79, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  .main_content .main_content_iner.main_content_iner_padding {
    padding: 0 135px 0 135px;
  }
}

@media (max-width: 575.98px) {

  /* line 44, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  .main_content .main_content_iner {
    margin-bottom: 20px;
  }
}

/* line 99, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.address_book_hint p {
  font-size: 14px;
  line-height: 26px;
  margin-bottom: 8px;
}

/* line 106, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.single_element h4 {
  margin-bottom: 30px;
}

/* line 109, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.single_element .quick_activity_wrap {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 30px;
  margin-bottom: 30px;
}

@media (max-width: 575.98px) {

  /* line 109, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  .single_element .quick_activity_wrap {
    grid-template-columns: repeat(1, 1fr);
    grid-gap: 15px;
    margin-bottom: 30px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 109, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  .single_element .quick_activity_wrap {
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 15px;
    margin-bottom: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991.98px) {

  /* line 109, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  .single_element .quick_activity_wrap {
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 15px;
    margin-bottom: 30px;
  }
}

@media (min-width: 992px) and (max-width: 1199.98px) {

  /* line 109, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  .single_element .quick_activity_wrap {
    grid-template-columns: repeat(2, 1fr);
    margin-bottom: 30px;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {

  /* line 109, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  .single_element .quick_activity_wrap {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* line 138, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.single_element .single_quick_activity {
  background-color: #fff;
  border-radius: 10px;
  -webkit-transition: 0.5s;
  transition: 0.5s;
  padding: 20px;
  position: relative;
}

/* line 145, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.single_element .single_quick_activity .notification_btn {
  position: absolute;
  right: 22px;
  top: 32px;
}

@media (max-width: 575.98px) {

  /* line 138, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  .single_element .single_quick_activity {
    padding: 20px 15px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 138, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
  .single_element .single_quick_activity {
    padding: 20px 15px;
  }
}

/* line 165, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.single_element .single_quick_activity .count_content {
  position: relative;
  z-index: 9;
  margin-bottom: 40px;
}

/* line 169, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.single_element .single_quick_activity .count_content .blue_color {
  color: #20b1b9 !important;
}

/* line 172, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.single_element .single_quick_activity .count_content .red_color {
  color: #ff4409 !important;
}

/* line 175, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.single_element .single_quick_activity .count_content .deep_blue {
  color: #016dd8 !important;
}

/* line 178, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.single_element .single_quick_activity .count_content .green_color {
  color: #01d874 !important;
}

/* line 182, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.single_element .single_quick_activity h3 {
  font-size: 28px;
  margin-bottom: 0;
  font-weight: 700;
  -webkit-transition: 0.5s;
  transition: 0.5s;
  color: #474d58;
  margin-bottom: 0px;
  font-family: "Mulish", sans-serif;
  line-height: 1;
}

/* line 192, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.single_element .single_quick_activity p {
  -webkit-transition: 0.5s;
  transition: 0.5s;
  font-size: 14px;
  font-weight: 700;
  color: #8890b5;
  font-family: "Mulish", sans-serif;
  line-height: 1;
  margin-bottom: 15px;
}

/* line 217, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.common_white_text {
  font-size: 18px;
  color: #fff;
  font-weight: 400;
}

/* line 223, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.text_white {
  color: #fff !important;
}

/* line 226, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.orange_bg {
  background: #ff4409 !important;
}

/* line 229, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.blue_bg {
  background: #016dd8 !important;
}

/* line 232, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.green_bg {
  background: #20b1b9 !important;
}

/******** main content css end *********/
/* line 237, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.datepickers-container {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1001;
}

/* line 243, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.serach_field-area.d-flex.align-items-center span {}

/* line 247, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.page_title_box {
  /*padding-bottom: 30px;*/
  background: transparent linear-gradient(92deg, #1658a5 0%, #1659a5 40%, #0089c2 100%) 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 10px #0000000d;
  border-radius: 10px;
  padding: 22px;
  margin-bottom: 22px;
}

.white_card .col-lg-3 {
  margin-bottom: 8px;
}

.page-title-img {
  padding: 0px 9px 5px 10px;
  height: 34px !important;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: unset;
  margin: unset;
}

h4 {
  font-size: 20px;
}

/* line 251, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.breadcrumb.page_bradcam {
  padding-left: 0;
  background: transparent;
  padding: 0;
}

/* line 256, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.breadcrumb.page_bradcam.default_bradcam li {
  color: #9b9aba;
}

/* line 258, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.breadcrumb.page_bradcam.default_bradcam li a {
  color: #9b9aba;
}

/* line 262, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.breadcrumb.page_bradcam.default_bradcam .breadcrumb-item+.breadcrumb-item::before {
  content: ">";
  color: #9b9aba;
}

/* line 267, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.breadcrumb.page_bradcam li {
  font-size: 14px;
  font-weight: 400;
  color: #9b9aba;
}

/* line 271, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.breadcrumb.page_bradcam li a {
  font-size: 13px;
  font-weight: 400;
  color: #9b9aba;
}

/* line 280, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.breadcrumb.page_bradcam .breadcrumb-item+.breadcrumb-item::before {
  content: "/";
  color: #9b9aba;
}

/* line 286, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.page_title_left h3 {
  color: #ffffff;
  font-size: 20px;
}

/* line 293, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.common_bootstrap_button span {
  background: #f64e60;
  padding: 7px 12px;
  text-transform: capitalize;
  display: inline-block;
  font-size: 14px;
  font-weight: 600;
  color: #fff;
  border-radius: 2px;
  cursor: pointer;
  min-height: 36px;
}

/* line 306, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.page_date_button {
  text-transform: capitalize;
  color: #9995b6;
  border-radius: 5px;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: 0.3s;
  min-height: 36px;
}

/* line 315, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.page_date_button img {
  margin-right: 10px;
}

/* line 319, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.page_title_right {
  display: flex;
  flex-wrap: wrap;
}

/* line 322, G:/admin_project/8 admin/management_html/scss/_main_content.scss */
.page_title_right .page_date_button {
  margin-right: 10px;
}

/* line 2, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.dashboard_part .notification_tab {
  padding: 27px 25px;
  background-color: #f5ecec;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 60px;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

/* line 10, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.dashboard_part .notification_tab p {
  font-size: 16px;
  color: #474d58;
}

/* line 13, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.dashboard_part .notification_tab p a {
  color: #2e4765;
}

/* line 18, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.dashboard_part .hide_icon {
  cursor: pointer;
}

/* line 20, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.dashboard_part .hide_icon i {
  color: #2e4765;
  font-size: 10px;
}

/* line 26, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.white_box {
  padding: 30px;
  background-color: #ffffff;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  border-radius: 20px;
}

@media (max-width: 575.98px) {

  /* line 26, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
  .white_box {
    padding: 30px 25px;
  }
}

/* line 34, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.white_box .white_box_tittle h4 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 0;
}

@media (max-width: 991px) {

  /* line 34, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
  .white_box .white_box_tittle h4 {
    margin-bottom: 15px;
  }
}

/* line 44, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.white_card {
  background-color: #ffffff;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  border-radius: 15px;
}

/* line 45, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.white_card .white_card_header {
  padding: 22px 15px;
  /* background: red; */
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  background: transparent linear-gradient(89deg, #1658a5 0%, #0089c2 100%) 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 10px #0000000d;
}

/* line 53, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.white_card .white_card_body {
  padding: 15px 15px 25px 20px;
}

/* line 57, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.white_box_50px {
  padding: 43px 45px 38px 50px;
  background-color: #ffffff;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  border-radius: 15px;
}

@media (max-width: 575.98px) {

  /* line 57, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
  .white_box_50px {
    padding: 30px 25px;
  }
}

/* line 65, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.white_box_50px .white_box_tittle h4 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 0;
}

@media (max-width: 991px) {

  /* line 65, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
  .white_box_50px .white_box_tittle h4 {
    margin-bottom: 15px;
  }
}

/* line 75, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.white_box2 {
  padding: 40px 40px 25px 40px;
  background-color: #fff;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
}

@media (max-width: 575.98px) {

  /* line 75, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
  .white_box2 {
    padding: 30px 25px;
  }
}

/* line 83, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.white_box2 .white_box_tittle h4 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 0;
}

@media (max-width: 991px) {

  /* line 83, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
  .white_box2 .white_box_tittle h4 {
    margin-bottom: 15px;
  }
}

/* line 94, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box .white_box_tittle {
  margin-bottom: 20px;
}

/* line 96, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box .white_box_tittle h4 {
  margin-bottom: 2px;
}

@media (max-width: 991px) {

  /* line 96, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
  .chart_box .white_box_tittle h4 {
    margin-bottom: 15px;
  }
}

/* line 103, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box p {
  margin-bottom: 29px;
  color: #707070;
}

/* line 110, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box label {
  margin-bottom: 0;
}

/* line 113, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box .b {
  display: block !important;
}

/* line 116, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box .toggle {
  position: relative;
  width: 44px;
  height: 22px;
  border-radius: 50px;
  background-color: #222222;
  border: 1px solid #222222;
  overflow: hidden;
}

/* line 125, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box .check {
  position: absolute;
  display: block;
  cursor: pointer;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: 6;
}

/* line 136, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box .check:checked~.switch {
  right: 5px;
  left: 57.5%;
  transition: 0.25s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition-property: left, right;
  transition-delay: 0.08s, 0s;
}

/* line 143, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box .switch {
  position: absolute;
  left: 5px;
  top: 3px;
  bottom: 2px;
  right: 57.5%;
  background-color: #fff;
  border-radius: 50%;
  z-index: 1;
  transition: 0.25s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition-property: left, right;
  transition-delay: 0s, 0.08s;
  height: 13px;
  width: 13px;
  padding: 6px !important;
  margin-right: 0;
}

/* line 159, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box .switch:after {
  display: none;
}

/* line 163, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box .hide {
  display: none !important;
}

/* line 172, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table .table {
  background: #ffffff;
  border-radius: 10px;
  margin-bottom: 35px;
  /*padding-top: 20px;*/
}

/* line 180, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table th,
.QA_section .QA_table td {
  font-weight: 400;
  padding: 10px 15px;
  vertical-align: middle;
}

/* line 185, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table .question_content {
  color: #7e7172;
  font-size: 13px;
}

/* line 188, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table .question_content:hover {
  color: #2e4765;
}

/* line 192, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table thead {
  padding-bottom: 10px;
}

/* line 194, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table thead th {
  white-space: nowrap;
  padding-left: 0;
  border-bottom: 1px solid rgba(130, 139, 178, 0.3) !important;
}

/* line 202, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table tbody tr:first-child td {
  padding-top: 10px;
}

/* line 205, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table tbody th {
  font-size: 14px;
  color: #415094;
  font-weight: 400 !important;
}

/* line 211, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table tbody .btn_1 {
  background: #65f9b3;
  color: #fff;
  border: 0;
  font-size: 8px;
  padding: 0;
  height: 18px;
  width: 26px;
  line-height: 18px;
  text-transform: uppercase;
  font-weight: 700;
}

/* line 223, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table tbody img.user_thumb {
  width: 36px;
  height: 36px;
  border-radius: 3px;
}

/* line 228, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table tbody img.check_status {
  width: 21px;
  height: 18px;
  border-radius: 3px;
}

/* line 233, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table tbody th,
.QA_section .QA_table tbody td {
  font-size: 12px;
  font-weight: 400;
  border-bottom: 0;
}

/* line 245, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table tbody th.pending,
.QA_section .QA_table tbody td.pending {
  color: #e09079;
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 500;
}

/* line 252, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table tbody th.priority_hight,
.QA_section .QA_table tbody td.priority_hight {
  font-size: 14px;
  color: #d7598f;
  font-weight: 400;
}

/* line 261, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table tbody tr {
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

/* line 268, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table tbody tr:hover {
  background: #fff;
}

/* line 274, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table .view_btn {
  color: #2e4765;
}

/* line 276, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table .view_btn:hover {
  text-decoration: underline;
}

/* line 280, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table .table {
  margin-bottom: 0 !important;
}

/* line 282, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table .table thead th {
  border-bottom: 0 solid transparent;
  padding: 9px 15px;
  border: 0px solid transparent;
  font-size: 12px;
  font-weight: 600;
  color: #474d58;
  white-space: nowrap;
  text-transform: capitalize;
  font-family: "Mulish", sans-serif;
  border: 0;
  background: #fff;
  border: 0 !important;
  background: transparent;
  border-top: 1px solid #e5ecff !important;
  border-bottom: 1px solid #e5ecff !important;
  position: relative;
}

/* line 299, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table .table thead th:first-child {
  border-radius: 5px 0px 0px 5px;
  border-left: 1px solid #e5ecff !important;
}

/* line 303, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.QA_section .QA_table .table thead th:last-child {
  border-radius: 0px 5px 5px 0px;
  border-right: 1px solid #e5ecff !important;
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 316, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
  .chart_box_1 .box_header {
    flex-direction: column;
  }
}

/* line 320, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box_1 .box_header .box_header_left {
  flex-basis: 50%;
}

@media (min-width: 768px) and (max-width: 991.98px) {

  /* line 320, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
  .chart_box_1 .box_header .box_header_left {
    flex-basis: 55%;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 320, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
  .chart_box_1 .box_header .box_header_left {
    flex-basis: 55%;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 320, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
  .chart_box_1 .box_header .box_header_left {
    flex-basis: 100%;
    width: 100%;
    margin-bottom: 20px;
  }
}

/* line 333, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box_1 .box_header .box_header_left ul {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

/* line 337, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box_1 .box_header .box_header_left ul li {
  display: inline-block;
  text-align: center;
}

@media (max-width: 575.98px) {

  /* line 337, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
  .chart_box_1 .box_header .box_header_left ul li {
    flex: 50% 0 0;
    margin-bottom: 20px;
  }
}

/* line 344, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box_1 .box_header .box_header_left ul li h4 {
  font-weight: 500;
  font-size: 18px;
  margin-bottom: 0px;
}

/* line 349, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box_1 .box_header .box_header_left ul li p {
  font-weight: 500;
  font-size: 12px;
  margin-bottom: 0;
}

/* line 360, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box_1 .box_header .box_header_right .legend_circle ul li {
  color: #828bb2;
  font-size: 12px;
  font-weight: 400;
  margin-right: 30px;
  display: inline-block;
}

@media (max-width: 575.98px) {

  /* line 360, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
  .chart_box_1 .box_header .box_header_right .legend_circle ul li {
    margin-right: 14px;
  }
}

/* line 369, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box_1 .box_header .box_header_right .legend_circle ul li:last-child {
  margin-right: 0;
}

/* line 372, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.chart_box_1 .box_header .box_header_right .legend_circle ul li span {
  width: 10px;
  height: 10px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 10px;
}

/* line 387, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.dashboard_breadcam p {
  color: #6f658d;
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

/* line 392, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.dashboard_breadcam p i {
  margin: 0 5px;
}

/* line 395, G:/admin_project/8 admin/management_html/scss/_dashboard_part.scss */
.dashboard_breadcam p a {
  color: #6f658d;
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

/* line 4, G:/admin_project/8 admin/management_html/scss/_form_style.scss */
.form_style .form-group label,
.form_style .form-group p {
  font-size: 14px;
  font-weight: 300;
  text-transform: capitalize;
  margin-bottom: 13px;
  color: #7e7172;
}

/* line 11, G:/admin_project/8 admin/management_html/scss/_form_style.scss */
.form_style .form-group input {
  border: 1px solid #e4e8ec;
  font-size: 13px;
  font-weight: 300;
  color: #7e7172;
  padding: 15px 25px;
  background-color: #fef1f2;
  height: auto;
  line-height: 18px;
}

/* line 2, G:/admin_project/8 admin/management_html/scss/_revenue_part.scss */
.revenue_part .btn_2 {
  padding: 8px 13px;
  margin-left: 30px;
}

/* line 7, G:/admin_project/8 admin/management_html/scss/_revenue_part.scss */
.revenue_part .courses_price .btn_1 {
  padding: 13px 32px;
  line-height: 16px;
  height: auto;
  width: auto;
}

/* line 15, G:/admin_project/8 admin/management_html/scss/_revenue_part.scss */
.revenue_part .courses_details span {
  font-size: 16px;
  font-weight: 600;
  color: #7e7172;
  margin-top: 3px;
}

/* line 23, G:/admin_project/8 admin/management_html/scss/_revenue_part.scss */
.revenue_part .courses_duration {
  position: relative;
  padding-left: 25px;
}

/* line 26, G:/admin_project/8 admin/management_html/scss/_revenue_part.scss */
.revenue_part .courses_duration h6 {
  font-size: 14px;
  margin-bottom: 8px;
}

/* line 29, G:/admin_project/8 admin/management_html/scss/_revenue_part.scss */
.revenue_part .courses_duration h6 i {
  color: #2e4765;
  position: absolute;
  left: 0;
  top: 2px;
}

/* line 2, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.payment_methord h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 30px;
}

/* line 7, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.payment_methord .payment_mathord_content {
  background-color: #fff;
  padding: 30px;
}

/* line 10, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.payment_methord .payment_mathord_content h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 28px 0 7px;
}

/* line 15, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.payment_methord .payment_mathord_content .btn_1 {
  margin-top: 28px;
}

/* line 19, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.payment_methord .nav-tabs {
  border-bottom: 0 solid transparent;
}

/* line 22, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.payment_methord .payment_methord_logo {
  display: flex;
  align-items: center;
}

/* line 26, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.payment_methord .payment_methord_logo .single_logo_iner {
  width: 188px;
  height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #bbc1c9;
  border-left: 0px solid transparent;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

/* line 35, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.payment_methord .payment_methord_logo .single_logo_iner:first-child {
  border-left: 1px solid #bbc1c9;
  border-radius: 5px 0 0 5px;
}

/* line 39, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.payment_methord .payment_methord_logo .single_logo_iner:last-child {
  border-radius: 0 5px 5px 0;
}

/* line 42, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.payment_methord .payment_methord_logo .single_logo_iner img {
  max-width: 100px;
}

/* line 45, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.payment_methord .payment_methord_logo .single_logo_iner.active {
  border: 1px solid #2e4765 !important;
}

/* line 54, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.form_group {
  margin-top: 24px;
}

/* line 56, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.form_group input {
  width: 100%;
  background-color: #fef1f2;
  border-radius: 5px;
  border: 1px solid #f1f3f5;
  padding: 12px 20px;
}

/* line 64, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.form_group .single_form_item label {
  width: 100%;
  display: block;
}

/* line 68, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.form_group .single_form_item input {
  width: auto;
  display: inline-block;
  padding: 12px 20px;
  max-width: 85px;
  color: #9c9c9c;
}

/* line 76, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.form_group #CVV {
  max-width: 118px !important;
}

/* line 79, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.form_group .nice_Select {
  background-color: #fef1f2;
  margin-right: 10px;
  color: #9c9c9c;
}

/* line 86, G:/admin_project/8 admin/management_html/scss/_payment_setting.scss */
.checkbox label {
  margin-bottom: 0;
  margin-top: 25px;
  color: #9c9c9c;
  margin-left: 8px;
}

/************** footer css ****************/
/* line 2, G:/admin_project/8 admin/management_html/scss/_footer.scss */
.footer_part {
  text-align: center;
  padding-bottom: 0px;
  padding-top: 6px;
  background: transparent;
  padding-left: 270px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0px;
  padding-bottom: 15px;
}

/* line 15, G:/admin_project/8 admin/management_html/scss/_footer.scss */
.footer_part.fixed_footer {
  position: fixed;
  z-index: 99;
  border-radius: 0;
  padding-bottom: 0;
  background: transparent;
}

/* line 22, G:/admin_project/8 admin/management_html/scss/_footer.scss */
.footer_part.fixed_footer .footer_iner {
  border-top: 2px solid #884ffb;
  background: #fff !important;
}

/* line 27, G:/admin_project/8 admin/management_html/scss/_footer.scss */
.footer_part.full_footer {
  padding-left: 136px;
}

/* line 30, G:/admin_project/8 admin/management_html/scss/_footer.scss */
.footer_part.default_footer {
  background: #f5f6ff;
}

/* line 32, G:/admin_project/8 admin/management_html/scss/_footer.scss */
.footer_part.default_footer .footer_iner {
  background: transparent !important;
}

@media only screen and (min-width: 320px) and (max-width: 768px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_footer.scss */
  .footer_part {
    padding-left: 0 !important;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_footer.scss */
  .footer_part {
    padding-left: 20%;
  }
}

/* line 44, G:/admin_project/8 admin/management_html/scss/_footer.scss */
.footer_part .footer_iner.text-center {
  background: #ffffff;
  padding: 27px 0;
  margin: 0 15px;
  border-radius: 15px;
  background: transparent;
}

/* line 51, G:/admin_project/8 admin/management_html/scss/_footer.scss */
.footer_part p {
  font-size: 12px;
  font-weight: 500;
  color: #818e94;
}

/* line 55, G:/admin_project/8 admin/management_html/scss/_footer.scss */
.footer_part p span {
  margin: 0 10px;
}

/* line 58, G:/admin_project/8 admin/management_html/scss/_footer.scss */
.footer_part p a {
  color: #884ffb;
}

/* line 60, G:/admin_project/8 admin/management_html/scss/_footer.scss */
.footer_part p a:hover {
  text-decoration: underline;
}

/************** footer css end ****************/
/********* breadcrumd css here **********/
/* line 2, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
.breadcrumb_content {
  background-color: #fff;
  padding: 19px 30px;
  line-height: 22px;
}

@media (max-width: 991px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
  .breadcrumb_content {
    padding: 25px 15px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
  .breadcrumb_content {
    display: block !important;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
  .breadcrumb_content {
    display: block !important;
  }
}

/* line 17, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
.breadcrumb_content .active {
  color: #2e4765;
}

/* line 20, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
.breadcrumb_content h2 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 0;
}

@media (max-width: 575.98px) {

  /* line 20, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
  .breadcrumb_content h2 {
    margin-bottom: 15px;
    padding: 0;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {

  /* line 20, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
  .breadcrumb_content h2 {
    margin-bottom: 15px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 20, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
  .breadcrumb_content h2 {
    margin-bottom: 15px;
  }
}

/* line 36, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
.breadcrumb_content .sa_breadcrumb_iner a {
  position: relative;
  color: #7e7172;
  padding-left: 30px;
}

/* line 40, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
.breadcrumb_content .sa_breadcrumb_iner a:after {
  content: "";
  position: absolute;
  left: 10px;
  top: 3px;
  clip-path: polygon(0 0, 100% 55%, 0 100%);
  background-color: #7e7172;
  width: 10px;
  height: 10px;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

/* line 51, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
.breadcrumb_content .sa_breadcrumb_iner a:first-child {
  padding-left: 0;
}

/* line 53, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
.breadcrumb_content .sa_breadcrumb_iner a:first-child:after {
  display: none;
}

/* line 57, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
.breadcrumb_content .sa_breadcrumb_iner a:hover {
  color: #2e4765;
}

/* line 58, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
.breadcrumb_content .sa_breadcrumb_iner a:hover:after {
  background-color: #2e4765;
}

/* line 63, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
.breadcrumb_content .sa_breadcrumb_iner a:last-child {
  color: #2e4765;
}

/* line 65, G:/admin_project/8 admin/management_html/scss/_breadcrumb.scss */
.breadcrumb_content .sa_breadcrumb_iner a:last-child:after {
  background-color: #2e4765;
}

/********* breadcrumd css end **********/
/* line 1, G:/admin_project/8 admin/management_html/scss/_bar-chart.scss */
#morris_bar {
  height: 300px;
}

/* line 4, G:/admin_project/8 admin/management_html/scss/_bar-chart.scss */
#morris_bar_bold {
  height: 300px;
}

/* line 8, G:/admin_project/8 admin/management_html/scss/_bar-chart.scss */
tspan {
  font-size: 13px;
  font-weight: 300;
  color: #7e7172;
  text-transform: capitalize;
}

/* line 15, G:/admin_project/8 admin/management_html/scss/_bar-chart.scss */
.morris-hover.morris-default-style .morris-hover-point {
  white-space: nowrap;
  margin: 0.1em 0;
  font-size: 13px;
  color: #fff !important;
  font-size: 12px;
  font-weight: 500;
  font-family: "Mulish", sans-serif;
}

/* line 25, G:/admin_project/8 admin/management_html/scss/_bar-chart.scss */
.morris-hover.morris-default-style .morris-hover-row-label {
  font-weight: bold;
  margin: 0;
  font-size: 12px;
  font-weight: 300;
  color: #7e7172;
  line-height: 12px;
  font-family: "Mulish", sans-serif;
}

/* line 35, G:/admin_project/8 admin/management_html/scss/_bar-chart.scss */
.morris-hover.morris-default-style {
  font-family: "Roboto", sans-serif;
  font-size: 12px;
  text-align: center;
  background: #000 !important;
  border: 0;
  padding: 7px 22px 5px 22px !important;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  color: #fff;
  border-radius: 5px;
  font-weight: 500;
}

/* line 47, G:/admin_project/8 admin/management_html/scss/_bar-chart.scss */
.morris-hover.morris-default-style span {
  font-weight: bold;
  margin: 0;
  font-size: 12px;
  font-weight: 300;
  color: #7e7172;
  line-height: 12px;
  font-family: "Mulish", sans-serif;
}

/* line 58, G:/admin_project/8 admin/management_html/scss/_bar-chart.scss */
canvas#highlights {
  height: 300px !important;
  overflow: hidden;
}

/* line 1, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.lms_category_wrap {
  margin-bottom: 50px;
}

/* line 3, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.lms_category_wrap .category_header {
  background: #fef1f2;
  border-radius: 5px;
  padding: 17px 30px;
}

/* line 8, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.lms_category_wrap .category_header .category_left h4 {
  font-size: 16px;
  font-weight: 500;
  color: #222;
  margin-bottom: 0;
}

/* line 14, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.lms_category_wrap .category_header .category_left p {
  color: #7e7172;
  font-size: 12px;
  font-weight: 300;
  margin-bottom: 0;
}

@media (max-width: 575.98px) {

  /* line 21, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
  .lms_category_wrap .category_header .category_right {
    margin-top: 15px;
  }
}

/* line 28, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.lms_category_wrap ul li {
  display: flex;
  justify-content: space-between;
  position: relative;
  padding: 15px 30px;
  background: #fff;
  align-items: center;
  border-bottom: 1px solid #eee1e2;
  transition: 0.3s;
}

@media (max-width: 575.98px) {

  /* line 28, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
  .lms_category_wrap ul li {
    padding: 15px 20px;
  }
}

/* line 41, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.lms_category_wrap ul li .category_info p {
  font-size: 13px;
  font-weight: 300;
  color: #7e7172;
}

/* line 47, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.lms_category_wrap ul li:hover {
  background: #fff7f8;
}

/* line 55, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.income_servay .count_content {
  text-align: center;
  margin-bottom: 30px;
}

/* line 58, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.income_servay .count_content h3 {
  font-size: 30px;
  font-weight: 600;
  color: #2e4765;
  line-height: 1;
  margin-bottom: 5px;
}

/* line 64, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.income_servay .count_content h3 span {
  color: #2e4765;
  font-weight: 600;
  line-height: 1;
}

/* line 70, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.income_servay .count_content p {
  font-size: 14px;
  font-weight: 400;
  color: #b2b5c0;
  line-height: 1;
}

/* line 80, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.staf_list_wrapper .single_staf {
  background: #fff;
  box-shadow: 0 7px 15px rgba(80, 143, 244, 0.15);
  text-align: center;
  padding: 50px 0;
  margin-bottom: 30px;
  margin-top: 30px;
}

/* line 87, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.staf_list_wrapper .single_staf .staf_thumb {
  margin-bottom: 25px;
}

/* line 89, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.staf_list_wrapper .single_staf .staf_thumb img {
  margin: auto;
  width: 118px;
  height: 118px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}

/* line 96, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.staf_list_wrapper .single_staf h4 {
  font-size: 22px;
  font-weight: 600;
  color: #2e4765;
  margin-bottom: 3px;
}

/* line 102, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.staf_list_wrapper .single_staf p {
  font-size: 14px;
  color: #818e94;
  font-weight: 500;
}

/* line 112, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.owl-carousel .owl-nav button {
  width: 39px;
  height: 39px;
  border-radius: 7px;
  background: #eff1f7;
  position: absolute;
  top: -89px;
  right: 0;
  font-size: 18px !important;
  background: #eff1f7 !important;
  color: #141124;
}

/* line 123, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.owl-carousel .owl-nav button.owl-prev {
  right: 50px;
}

/* line 126, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.owl-carousel .owl-nav button:hover {
  background: #16bbe5 !important;
  color: #fff;
}

/* line 134, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.Activity_timeline {
  position: relative;
}

/* line 138, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.Activity_timeline ul li {
  position: relative;
  margin-bottom: 15px;
}

/* line 141, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.Activity_timeline ul li:last-child {
  margin-bottom: 0px;
}

/* line 144, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.Activity_timeline ul li .activity_bell {
  background: #884ffb;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  position: absolute;
  top: 6px;
}

/* line 151, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.Activity_timeline ul li .activity_bell.bell_lite {
  opacity: 0.19;
}

/* line 155, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.Activity_timeline ul li .activity_wrap {
  position: relative;
  padding-left: 27px;
}

/* line 158, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.Activity_timeline ul li .activity_wrap h6 {
  font-size: 16px;
  font-weight: 600;
  color: #2d1967;
  margin-bottom: 0;
  line-height: 28px;
}

/* line 165, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.Activity_timeline ul li .activity_wrap p {
  font-size: 14px;
  line-height: 28px;
  color: #adb8c7;
  font-weight: 400;
}

/* line 179, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.activity_progressbar .single_progressbar h6 {
  font-size: 12px;
  text-transform: capitalize;
  margin-bottom: 5px;
}

/* line 186, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.barfiller {
  width: 100%;
  height: 8px;
  background: #f8f9ff;
  position: relative;
  margin-bottom: 30px;
  border-radius: 30px;
}

/* line 193, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.barfiller .fill {
  display: block;
  position: relative;
  width: 0px;
  height: 100%;
  z-index: 1;
  border-radius: 30px;
}

/* line 201, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.barfiller .tipWrap {
  display: none;
}

/* line 204, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.barfiller .tip {
  margin-top: -23px;
  padding: 2px 4px;
  font-size: 11px;
  color: #000;
  left: 0px;
  position: absolute;
  z-index: 2;
}

/* line 214, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.barfiller#bar1 .tip {
  color: #508ff4;
}

/* line 219, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.barfiller#bar2 .tip {
  color: #ffbf43;
}

/* line 224, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.barfiller#bar3 .tip {
  color: #4be69d;
}

/* line 229, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.barfiller#bar4 .tip {
  color: #9267ff;
}

/* line 236, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.single_social_media {
  margin-bottom: 27px;
}

/* line 238, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.single_social_media .icon_media {
  width: 72px;
  height: 68px;
  background: #508ff4;
  font-size: 38px;
  text-align: center;
  line-height: 72px;
  border-radius: 10px;
  margin-right: 20px;
  flex: 72px 0 0;
  color: #fff;
}

/* line 249, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.single_social_media .icon_media.twitter_bg {
  background: #43ccff;
}

/* line 252, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.single_social_media .icon_media.youtube_bg {
  background: #ff0000;
}

/* line 255, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.single_social_media .icon_media.insta_bg {
  background: #ff72b9;
}

/* line 260, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.single_social_media .media_contet span {
  font-size: 16px;
  color: #8890b5;
  font-weight: 700;
  display: block;
  margin-bottom: 10px;
}

/* line 267, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.single_social_media .media_contet h4 {
  font-size: 35px;
  font-weight: 700;
  color: #474d58;
  margin-bottom: 0;
}

/* line 276, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.grid_4rap {
  display: grid;
  grid-gap: 15px;
  grid-template-columns: repeat(auto-fit, minmax(100%, 1fr));
}

@media (min-width: 1600px) {

  /* line 276, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
  .grid_4rap {
    grid-template-columns: repeat(auto-fit, minmax(40%, 1fr));
  }
}

/* line 286, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.weatcher_update_wrapper {
  padding: 20px;
}

/* line 288, G:/admin_project/8 admin/management_html/scss/_lms_category.scss */
.weatcher_update_wrapper .weather_img_2 {
  background-color: #e5fffe;
  border-radius: 15px;
  background-image: url(../img/weather_1.png);
  background-repeat: no-repeat;
  background-size: 100% auto;
  width: 100%;
  height: 100%;
}

/* line 1, G:/admin_project/8 admin/management_html/scss/_summer_note.scss */
.note-editor.note-frame {
  border: 1px solid #eee1e2;
}

/* line 3, G:/admin_project/8 admin/management_html/scss/_summer_note.scss */
.note-editor.note-frame .note-popover .note-popover-content,
.note-editor.note-frame .note-toolbar {
  padding: 4px 15px 8px 15px;
  margin: 0;
  border-radius: 5px 5px 0 0;
  background-color: #f65365;
}

/* line 12, G:/admin_project/8 admin/management_html/scss/_summer_note.scss */
.note-editor.note-frame .note-btn i,
.note-editor.note-frame .note-current-fontname {
  color: #7e7172;
  font-size: 12px;
}

/* line 17, G:/admin_project/8 admin/management_html/scss/_summer_note.scss */
.note-editor.note-frame .note-btn {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.4;
  color: #7e7172;
  text-align: center;
  white-space: nowrap;
  cursor: pointer;
  border-radius: 3px;
}

/* line 26, G:/admin_project/8 admin/management_html/scss/_summer_note.scss */
.note-editor.note-frame .note-btn:hover {
  color: #fff;
  background: #884ffb;
}

/* line 30, G:/admin_project/8 admin/management_html/scss/_summer_note.scss */
.note-editor.note-frame .note-btn:hover i {
  color: #fff;
  background: #884ffb;
}

/* line 34, G:/admin_project/8 admin/management_html/scss/_summer_note.scss */
.note-editor.note-frame .note-btn .note-current-fontname:hover {
  color: #fff;
}

/* line 39, G:/admin_project/8 admin/management_html/scss/_summer_note.scss */
.note-editor.note-frame .show .dropdown-toggle::after {
  transform: rotate(180deg);
  color: red;
}

/* line 43, G:/admin_project/8 admin/management_html/scss/_summer_note.scss */
.note-editor.note-frame .note-icon-font.note-recent-color {
  background: transparent !important;
  color: #7e7172 !important;
}

/* line 48, G:/admin_project/8 admin/management_html/scss/_summer_note.scss */
.note-editor.note-frame .show .dropdown-toggle::after {
  color: #7e7172 !important;
}

/* line 51, G:/admin_project/8 admin/management_html/scss/_summer_note.scss */
.note-editor.note-frame .note-btn:hover span {
  color: #fff !important;
}

/* line 54, G:/admin_project/8 admin/management_html/scss/_summer_note.scss */
.note-editor.note-frame .note-btn:hover i {
  color: #fff !important;
}

/* line 57, G:/admin_project/8 admin/management_html/scss/_summer_note.scss */
.note-editor.note-frame .note-editing-area .note-editable {
  background-color: #fff;
}

/* line 61, G:/admin_project/8 admin/management_html/scss/_summer_note.scss */
.note-editor.note-frame .note-placeholder {
  padding: 18px 25px 0 25px;
  font-size: 14px;
  color: #7e7172;
  line-height: 28px;
  font-weight: 300;
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 1, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
  .lms_sm_block {
    display: block;
  }
}

@media (max-width: 575.98px) {

  /* line 8, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
  .lms_xs_small_btn a {
    padding: 11px 20px;
  }
}

@media (max-width: 575.98px) {

  /* line 14, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
  .mb_xs_20px {
    margin-bottom: 20px !important;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 19, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
  .mb_sm_20px {
    margin-bottom: 20px;
  }
}

/* line 24, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.lms_supper_text {
  vertical-align: super !important;
}

/* line 27, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.gj-picker-md {
  border: 1px solid #fef1f2;
  padding: 10px;
}

/* line 31, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.gj-picker-md table tr td.today div {
  color: #fff;
  background: #ff4409;
  border-radius: 50%;
}

/* line 36, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.gj-picker-md table tr td.gj-cursor-pointer div:hover {
  border-radius: 50%;
  background: #ff4409;
  color: #fff;
}

/* line 42, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.gj-picker-md div[role="navigator"] div:first-child,
.gj-picker-md div[role="navigator"] div:last-child {
  max-width: 42px;
  background: #fef1f2;
  color: #000;
  border-radius: 50%;
  font-size: 14px;
  line-height: 44px;
  height: 42px;
  font-weight: 900;
}

/* line 51, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.gj-picker-md div[role="navigator"] div:first-child i,
.gj-picker-md div[role="navigator"] div:last-child i {
  font-weight: 900;
  font-size: 14px;
}

/* line 60, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.dropdown-menu.show .dropdown-toggle.lms_drop_1::after {
  color: #fff !important;
}

/* line 64, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.dropdown-menu.option_width {
  min-width: 102px;
}

/* line 68, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.dropdown.show .dropdown-toggle.lms_drop_1::after {
  color: #fff;
}

/* line 75, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.lms_common_header .lms_common_title h4 {
  margin-bottom: 3px;
}

/* line 78, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.lms_common_header .lms_common_title p {
  font-size: 12px;
  font-weight: 300;
  color: #7e7172;
  margin-bottom: 0;
}

/* line 87, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.common_line_separator {
  display: inline-block;
  width: 2px;
  height: 11px;
  background: #b9b3b3;
  margin: 0 7px 0 7px;
}

/* line 94, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.h-120px {
  height: 120px !important;
}

/* line 97, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.h-260px {
  height: 260px !important;
}

@media (max-width: 991px) {

  /* line 97, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
  .h-260px {
    height: 160px !important;
  }
}

/* line 103, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.h-230px {
  height: 230px !important;
}

@media (max-width: 991px) {

  /* line 103, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
  .h-230px {
    height: 160px !important;
  }
}

/* line 111, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.discription_list_wrap p {
  font-size: 13px;
  font-weight: 300;
  color: #7e7172;
  line-height: 26px;
  margin-bottom: 17px;
}

/* line 118, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.discription_list_wrap h5 {
  font-size: 12px;
  font-weight: 400;
  color: #222222;
  margin-top: 19px;
  margin-bottom: 14px;
}

/* line 128, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.common_list_style li {
  font-size: 13px;
  font-weight: 300;
  color: #7e7172;
  line-height: 26px;
  position: relative;
  padding-left: 15px;
}

/* line 135, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.common_list_style li:before {
  position: absolute;
  left: 0;
  top: 10px;
  width: 3px;
  height: 3px;
  background: #7e7172;
  content: "";
  border-radius: 50%;
}

/* line 150, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.result_modal_table_form .modal-body {
  margin: 30px 0;
  padding: 120px 100px 104px 100px !important;
}

@media (max-width: 991px) {

  /* line 150, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
  .result_modal_table_form .modal-body {
    padding: 30px !important;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 150, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
  .result_modal_table_form .modal-body {
    padding: 50px 50px !important;
  }
}

/* line 163, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.result_modal_table_form .table thead th {
  border-bottom: 0 solid transparent;
  background-color: #fef1f2;
  padding: 17px 30px;
  line-height: 16px;
  border: 0px solid transparent;
  border-top-color: transparent;
  border-top-style: solid;
  border-top-width: 0px;
  font-size: 12px;
  font-weight: 400;
  color: #222222;
  white-space: nowrap;
}

/* line 179, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.result_modal_table_form .table tbody th,
.result_modal_table_form .table tbody td {
  color: #7e7172;
  font-size: 13px !important;
  color: #7e7172;
  font-weight: 300;
  border-bottom: 1px solid #eee1e2;
  padding: 10px 25px !important;
}

/* line 189, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.result_modal_table_form .result_header_modal {
  margin-bottom: 45px;
}

/* line 192, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.result_modal_table_form .result_header_modal h2 {
  font-size: 36px;
  font-weight: 500;
  color: #222222;
  margin-bottom: 15px;
}

@media (max-width: 575.98px) {

  /* line 192, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
  .result_modal_table_form .result_header_modal h2 {
    font-size: 20px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 192, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
  .result_modal_table_form .result_header_modal h2 {
    font-size: 20px;
  }
}

/* line 204, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.result_modal_table_form .result_header_modal p {
  font-size: 14px;
  font-weight: 300;
  color: #7e7172;
  line-height: 26px;
}

/* line 210, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.result_modal_table_form .result_header_modal .passed_text {
  display: block;
  font-size: 18px;
  font-weight: 500;
  margin-top: 29px;
  margin-bottom: 35px;
}

@media (max-width: 575.98px) {

  /* line 210, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
  .result_modal_table_form .result_header_modal .passed_text {
    margin-top: 15px;
    margin-bottom: 15px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {

  /* line 210, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
  .result_modal_table_form .result_header_modal .passed_text {
    margin-top: 15px;
    margin-bottom: 15px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {

  /* line 210, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
  .result_modal_table_form .result_header_modal .passed_text {
    margin-top: 15px;
    margin-bottom: 15px;
  }
}

/* line 237, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.student_list_img img {
  width: 50px;
  height: 50px;
}

/* line 243, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.common_date_picker input {
  width: 100%;
  border: 1px solid #f1f3f5;
  border-radius: 3px;
  height: 40px;
  line-height: 40px;
  font-size: 13px;
  color: #222;
  padding: 0px 25px;
  font-weight: 500;
}

/* line 256, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.website_settings_wrap .modal-body {
  padding: 0 !important;
}

/* line 260, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.input_form_persent {
  position: relative;
}

/* line 262, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.input_form_persent:before {
  content: "\f295";
  border-bottom: 0;
  border-right: 0;
  display: block;
  margin-top: 0;
  pointer-events: none;
  position: absolute;
  top: 50%;
  width: auto;
  right: 25px;
  border: 0;
  font-family: "Font Awesome 5 Free";
  font-weight: 600;
  color: #cec1c2;
  transform: translateY(-50%);
  font-size: 13px;
}

/* line 286, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.tagInput_field {
  margin-bottom: 21px;
}

/* line 288, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.tagInput_field label {
  font-size: 12px;
  font-weight: 300;
  color: #222222;
  text-align: left;
  margin: 0;
  margin-bottom: 0px;
  display: block;
  margin-bottom: 8px;
}

/* line 298, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.tagInput_field .bootstrap-tagsinput {
  text-align: left;
  border: 1px solid #eee1e2;
  padding: 9px 25px 4px 25px;
  box-shadow: none;
  border-radius: 3px;
  min-height: 50px;
}

/* line 305, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.tagInput_field .bootstrap-tagsinput input {
  border: none !important;
  width: auto !important;
  display: inline-block !important;
  background-color: transparent !important;
  height: 0;
  margin: 0;
  min-width: 20px;
  padding: 7px !important;
}

/* line 316, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.tagInput_field .bootstrap-tagsinput .badge {
  margin: 0 5px 5px 0;
  height: 30px;
  line-height: 30px;
  padding: 0 30px 0 20px;
  font-size: 12px;
  color: #ff4409;
  font-weight: 400;
  background: #ffff;
  border: 1px solid #ff4409;
  border-radius: 30px;
  position: relative;
}

/* line 329, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.tagInput_field .bootstrap-tagsinput .badge [data-role="remove"]::after {
  font-size: 10px;
  font-family: "themify";
  content: "\e646";
  background: none;
  padding: 0;
  margin: 0;
  margin-left: 0px;
  background: transparent;
  margin-left: 5px;
  padding: 0;
  border: 1px solid transparent;
  padding: 2px;
}

/* line 343, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.tagInput_field .bootstrap-tagsinput .badge [data-role="remove"]:hover::after {
  background: #ff4409;
  color: #fff;
  border-color: #ff4409;
}

/* line 348, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.tagInput_field span.badge.badge-info span {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 8px;
}

/* line 357, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_striped_progressbar .progress {
  height: 24px;
  background: transparent;
  border-radius: 30px;
  width: 256px;
}

/* line 364, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_striped_progressbar .progress-bar {
  position: relative;
  border-radius: 30px;
  text-align: left;
  font-size: 15px;
  padding-left: 15px;
  color: #fff;
  font-weight: 300;
  background: transparent;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

/* line 375, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_striped_progressbar .progress-bar.green_bar {
  background-image: url(../img/progress/progress-bg.png);
  background-size: cover;
}

/* line 379, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_striped_progressbar .progress-bar.yellow_bar {
  background-image: url(../img/progress/design-progress-yellow.png);
  background-size: cover;
}

/* line 384, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_striped_progressbar .progress-bar.red_bar {
  background-image: url(../img/progress/design-progress-red.png);
  background-size: cover;
}

/* line 394, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_lms_radio_2 label {
  display: inline-block;
  cursor: pointer;
  position: relative;
  font-size: 13px;
  font-weight: 300;
  color: #7e7172;
  margin-left: 0;
  padding-bottom: 15px;
  margin-top: 0;
}

/* line 405, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_lms_radio_2 label span {
  display: inline-block;
  position: relative;
  background-color: transparent;
  width: 20px;
  height: 20px;
  transform-origin: center;
  border: 1px solid #eee2e3;
  border-radius: 50%;
  vertical-align: -6px;
  margin-right: 15px;
  transition: background-color 150ms 200ms, transform 350ms cubic-bezier(0.78, -1.22, 0.17, 1.89);
}

/* line 419, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_lms_radio_2 label span:before {
  content: "";
  width: 0px;
  height: 1px;
  border-radius: 2px;
  background: #ff4409;
  position: absolute;
  transform: rotate(38deg);
  top: 13px;
  left: 9px;
  transition: width 50ms ease 50ms;
  transform-origin: 0% 0%;
  top: 9.69px;
  left: 4.99px;
  border-radius: 0;
}

/* line 436, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_lms_radio_2 label span:after {
  content: "";
  width: 0;
  height: 1px;
  border-radius: 2px;
  background: #ff4409;
  position: absolute;
  transform: rotate(305deg);
  top: 12.8px;
  left: 7.48px;
  transition: width 50ms ease;
  transform-origin: 0% 0%;
  border-radius: 0;
}

/* line 453, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_lms_radio_2 label:hover span:before {
  width: 4px;
  transition: width 100ms ease;
}

/* line 458, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_lms_radio_2 label:hover span:after {
  width: 10px;
  transition: width 150ms ease 100ms;
}

/* line 465, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_lms_radio_2 input[type="radio"] {
  display: none;
}

/* line 469, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_lms_radio_2 input[type="radio"]:checked+label span {
  background-color: #ff4409;
  transform: scale(1);
  border-color: #ff4409;
}

/* line 474, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_lms_radio_2 input[type="radio"]:checked+label span:after {
  width: 10px;
  background: #fff;
  transition: width 150ms ease 100ms;
}

/* line 480, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_lms_radio_2 input[type="radio"]:checked+label span:before {
  width: 4px;
  background: #fff;
  transition: width 150ms ease 100ms;
}

/* line 487, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_lms_radio_2 input[type="radio"]:checked+label:hover span {
  background-color: #ff4409;
  transform: scale(1.25);
  border-color: #ff4409;
}

/* line 492, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_lms_radio_2 input[type="radio"]:checked+label:hover span:after {
  width: 10px;
  background: #fff;
  transition: width 150ms ease 100ms;
}

/* line 497, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.custom_lms_radio_2 input[type="radio"]:checked+label:hover span:before {
  width: 4px;
  background: #fff;
  transition: width 150ms ease 100ms;
}

/* line 510, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.import_topic_wrapper.modal_btn {
  display: grid !important;
  grid-gap: 10px;
  grid-template-columns: 3fr 9fr;
  align-items: end;
}

/* line 515, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.import_topic_wrapper.modal_btn .btn_1 {
  margin: 0 !important;
}

@media (max-width: 575.98px) {

  /* line 510, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
  .import_topic_wrapper.modal_btn {
    grid-template-columns: repeat(1, 1fr);
  }
}

/* line 528, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.indvisual_form .btn_1.big_btn {
  padding: 16px 36px;
}

/* line 531, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.indvisual_form .chose_thumb_title {
  font-size: 12px;
  font-weight: 300;
  color: #222222;
  text-align: left;
  margin: 0;
  margin-bottom: 0px;
  display: block;
  margin-bottom: 8px;
}

/* line 542, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.indvisual_form .input_wrap label {
  font-size: 12px;
  font-weight: 300;
  color: #222222;
  text-align: left;
  margin: 0;
  margin-bottom: 0px;
  display: block;
  margin-bottom: 8px;
}

/* line 552, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.indvisual_form .input_wrap textarea {
  height: 100px;
  padding: 0;
  line-height: 28px;
  padding: 13px 25px;
}

/* line 557, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.indvisual_form .input_wrap textarea.max_textarea {
  height: 290px;
}

/* line 563, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.indvisual_form .nice_Select,
.indvisual_form input,
.indvisual_form textarea {
  height: 50px;
  line-height: 50px;
  background-color: #fff;
  border: 1px solid #eee1e2;
  padding: 10px 25px;
  color: #7e7172;
  font-weight: 500;
  font-size: 13px;
  width: 100%;
  display: block;
  margin-bottom: 21px;
  font-weight: 300;
  border-radius: 3px;
}

/* line 577, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.indvisual_form .nice_Select::placeholder,
.indvisual_form input::placeholder,
.indvisual_form textarea::placeholder {
  color: #7e7172;
  font-weight: 300;
  opacity: 1;
}

/* line 583, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.indvisual_form .nice_Select {
  line-height: 30px;
}

/* line 585, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.indvisual_form .nice_Select :after {
  left: 22px;
}

/* line 589, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.indvisual_form .nice-select.nice_Select2 {
  height: 30px;
  line-height: 30px;
  background-color: #fff;
  border: 1px solid #eee1e2;
  padding: 0 37px 0 20px;
  color: #7e7172;
  font-weight: 500;
  font-size: 13px;
  width: 100%;
  display: block;
  margin-bottom: 21px;
  font-weight: 300;
  border-radius: 3px;
  margin: 0;
  width: auto;
}

/* line 605, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.indvisual_form .nice-select.nice_Select2::after {
  top: 20%;
}

/* line 615, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.list_counter_wrapper .single_list_counter {
  border-bottom: 1px solid #f1f3f5;
  padding: 35px 0 26px 87px;
}

/* line 618, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.list_counter_wrapper .single_list_counter:last-child {
  border: 0;
}

/* line 621, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.list_counter_wrapper .single_list_counter h3 {
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 0px;
}

/* line 625, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.list_counter_wrapper .single_list_counter h3 span {
  font-size: 30px;
  font-weight: 700;
}

/* line 630, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.list_counter_wrapper .single_list_counter p {
  font-size: 17px;
  color: #6f658d;
  margin-bottom: 0;
  padding-top: 15px;
}

/* line 639, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.green_color {
  color: #01d874 !important;
}

/* line 642, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.blue_color {
  color: #20b1b9 !important;
}

/* line 645, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.deep_blue {
  color: #016dd8 !important;
}

/* line 648, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.deep_blue_2 {
  color: #4567ee !important;
}

/* line 651, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.red_color {
  color: #ff4409 !important;
}

/* line 657, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.activity_progressbar .single_progressbar {
  display: grid !important;
  grid-template-columns: 150px auto;
  margin-bottom: 25px;
  align-items: center;
}

/* line 662, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.activity_progressbar .single_progressbar h6 {
  font-size: 16px;
  font-weight: 400;
  color: #2d1967;
}

/* line 667, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.activity_progressbar .single_progressbar .barfiller {
  margin-bottom: 0;
}

/* line 669, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.activity_progressbar .single_progressbar .barfiller .tip {
  color: #2d1967 !important;
  font-size: 12px;
}

/* line 677, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.legend_style_grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  padding-left: 25px;
  margin-top: 50px;
}

@media (max-width: 991px) {

  /* line 677, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
  .legend_style_grid {
    grid-template-columns: repeat(1, 1fr);
  }
}

/* line 685, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.legend_style_grid li {
  display: flex !important;
  align-items: center !important;
  margin-bottom: 8px;
}

/* line 689, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.legend_style_grid li span {
  width: 18px;
  height: 18px;
  border-radius: 3px;
  display: inline-block;
}

/* line 699, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.bulder_tab_wrapper ul {
  border-bottom: 1px solid #e4e6ef;
}

/* line 702, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.bulder_tab_wrapper ul li .nav-link {
  padding-top: 2rem;
  padding-bottom: 2rem;
  font-weight: 500;
  color: #7e8299;
  text-transform: capitalize;
  border-bottom: 1px solid transparent;
}

/* line 709, G:/admin_project/8 admin/management_html/scss/_common_component.scss */
.bulder_tab_wrapper ul li .nav-link.active {
  color: #3699ff;
  border-bottom: 1px solid #3699ff;
}

/* line 2, G:/admin_project/8 admin/management_html/scss/_projects.scss */
.assign_list li {
  display: inline-block;
  margin-right: 5px;
}

/* line 5, G:/admin_project/8 admin/management_html/scss/_projects.scss */
.assign_list li:last-child {
  margin-right: 0;
}

/* line 9, G:/admin_project/8 admin/management_html/scss/_projects.scss */
.assign_list li a img {
  width: 100%;
  border-radius: 50%;
  -webkit-transition: 0.2s;
  -moz-transition: 0.2s;
  -o-transition: 0.2s;
  transition: 0.2s;
  width: 23px;
  height: 23px;
  transform: translateY(0px);
}

/* line 17, G:/admin_project/8 admin/management_html/scss/_projects.scss */
.assign_list li a img:hover {
  transform: translateY(-2px);
}

/* line 27, G:/admin_project/8 admin/management_html/scss/_projects.scss */
/*.dataTables_paginate {
  margin-top: 0;
  margin-top: 30px;
}
*/
/* line 30, G:/admin_project/8 admin/management_html/scss/_projects.scss */
/*.dataTables_paginate a {
  width: 32px;
  height: 32px;
  background: #FFFFFF !important;
  border-radius: 3px !important;
  text-align: center !important;
  line-height: 32px;
  padding: 0 !important;
  margin: 0 !important;
  margin: 0 5px !important;
}*/

/* line 40, G:/admin_project/8 admin/management_html/scss/_projects.scss */
/*.dataTables_paginate a.current {
  background: #884FFB !important;
  box-shadow: 0px 5px 10px rgba(59, 118, 239, 0.3) !important;
  border: 0 !important;
  color: #fff !important;
}*/

/* line 46, G:/admin_project/8 admin/management_html/scss/_projects.scss */
/*.dataTables_paginate a:hover {
  background: #ff3b00 !important;
  box-shadow: 0px 5px 10px rgba(59, 118, 239, 0.3);
  border: 0 !important;
  color: #fff !important;
}*/

/* line 51, G:/admin_project/8 admin/management_html/scss/_projects.scss */
/*.dataTables_paginate a:hover i {
  color: #fff !important;
}*/

/* line 55, G:/admin_project/8 admin/management_html/scss/_projects.scss */
/*.dataTables_paginate a i {
  line-height: 32px;
  font-size: 12px;
  color: #415094;
}
*/
/* line 62, G:/admin_project/8 admin/management_html/scss/_projects.scss */
/*.dataTables_wrapper .dataTables_paginate .paginate_button.disabled, .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover, .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active {
  cursor: default;
  color: #fff !important;
  border: 0;
}*/

/* line 67, G:/admin_project/8 admin/management_html/scss/_projects.scss */
/*#DataTables_Table_0_info {
  font-size: 14px;
  color: #415094;
  font-weight: 400;
  margin-top: 35px;
  padding-top: 0;
}*/

/* line 74, G:/admin_project/8 admin/management_html/scss/_projects.scss */
/*.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  color: #fff !important;
  border: 1px solid #884FFB;
  background-color: #ff3b00 !important;
  box-shadow: 0px 5px 10px rgba(136, 79, 251, 0.04) !important;
}*/

/* line 80, G:/admin_project/8 admin/management_html/scss/_projects.scss */
/*.dataTables_wrapper .dataTables_paginate .paginate_button {
  border: 0 !important;
}*/

/* line 1, G:/admin_project/8 admin/management_html/scss/_pie.scss */
.min_400 {
  min-height: 380px;
}

@media (min-width: 1900px) {

  /* line 1, G:/admin_project/8 admin/management_html/scss/_pie.scss */
  .min_400 {
    min-height: 450px;
  }
}

/* line 7, G:/admin_project/8 admin/management_html/scss/_pie.scss */
.min_430 {
  min-height: 430px;
}

@media (min-width: 1900px) {

  /* line 7, G:/admin_project/8 admin/management_html/scss/_pie.scss */
  .min_430 {
    min-height: 460px;
  }
}

/* line 13, G:/admin_project/8 admin/management_html/scss/_pie.scss */
.mb-55 {
  margin-bottom: 55px;
}

/* line 1, G:/admin_project/8 admin/management_html/scss/_chart_box.scss */
.widget-chart {
  text-align: center;
  padding: 1rem;
  position: relative;
}

/* line 5, G:/admin_project/8 admin/management_html/scss/_chart_box.scss */
.widget-chart .rounded {
  border-radius: 0.25rem !important;
}

/* line 8, G:/admin_project/8 admin/management_html/scss/_chart_box.scss */
.widget-chart .icon-wrapper {
  width: 54px;
  height: 54px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

/* line 14, G:/admin_project/8 admin/management_html/scss/_chart_box.scss */
.widget-chart .icon-wrapper .icon-wrapper-bg {
  position: absolute;
  height: 100%;
  width: 100%;
  z-index: 3;
  opacity: 0.2;
}

/* line 21, G:/admin_project/8 admin/management_html/scss/_chart_box.scss */
.widget-chart .icon-wrapper i {
  margin: 0 auto;
  font-size: 1.7rem;
  position: relative;
  z-index: 5;
  line-height: 54px;
}

/* line 30, G:/admin_project/8 admin/management_html/scss/_chart_box.scss */
.widget-chart .widget-numbers {
  font-weight: bold;
  font-size: 2.5rem;
  display: block;
  line-height: 1;
  margin: 1rem auto;
}

/* line 36, G:/admin_project/8 admin/management_html/scss/_chart_box.scss */
.widget-chart .widget-numbers span {
  font-weight: bold;
  font-size: 2.5rem;
}

/* line 41, G:/admin_project/8 admin/management_html/scss/_chart_box.scss */
.widget-chart .widget-subheading {
  margin-top: -0.5rem;
}

/* line 44, G:/admin_project/8 admin/management_html/scss/_chart_box.scss */
.widget-chart .widget-description {
  margin: 1rem 0 0;
}

/* line 49, G:/admin_project/8 admin/management_html/scss/_chart_box.scss */
.bg-light {
  background-color: #eee !important;
}

/* line 52, G:/admin_project/8 admin/management_html/scss/_chart_box.scss */
.icon-wrapper .icon-wrapper-bg.bg-light {
  opacity: 0.08;
}

/* line 55, G:/admin_project/8 admin/management_html/scss/_chart_box.scss */
.bg-focus {
  background-color: #444054 !important;
}

/* line 58, G:/admin_project/8 admin/management_html/scss/_chart_box.scss */
.bg-primary {
  background-color: #3f6ad8 !important;
}

/* line 2, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 40px;
}

/* line 7, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom .card {
  margin-bottom: 15px;
  border: 0;
  overflow: visible;
}

/* line 11, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom .card .card-header {
  border: 0;
  padding: 0;
  border-radius: 10px !important;
}

/* line 15, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom .card .card-header h2 {
  padding: 0;
}

/* line 18, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom .card .card-header h2 a {
  padding: 0;
  font-size: 14px;
  font-weight: 500;
  width: 100%;
  text-align: left;
  position: relative;
  position: relative;
  padding: 13px 20px;
  background: #884ffb;
  box-shadow: 0px 5px 10px rgba(76, 110, 248, 0.3);
  color: #fff;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border: 0;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;
}

/* line 36, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom .card .card-header h2 a::before {
  content: "\e64b";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  font-family: "themify";
  font-size: 14px;
  right: 30px;
  color: #fff;
}

/* line 47, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom .card .card-header h2 a.collapsed {
  background: #f5f6ff;
  color: #415094;
  box-shadow: none;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;
}

/* line 52, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom .card .card-header h2 a.collapsed::before {
  content: "\e648";
  color: #415094;
}

/* line 57, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom .card .card-header h2 a:focus {
  text-decoration: none;
}

/* line 63, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom .card .card-body {
  padding: 24px 20px 30px 20px;
}

@media (max-width: 575.98px) {

  /* line 63, G:/admin_project/8 admin/management_html/scss/_faq.scss */
  .accordion_custom .card .card-body {
    padding: 15px;
  }
}

/* line 68, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom .card .card-body p {
  font-size: 14px;
  line-height: 26px;
  color: #555555;
  font-weight: 300;
  margin-bottom: 28px;
}

/* line 74, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom .card .card-body p:last-child {
  margin-bottom: 0;
}

/* line 82, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom2 h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 40px;
}

/* line 87, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom2 .card {
  margin-bottom: 15px;
  border: 0;
  overflow: visible;
}

/* line 91, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom2 .card .card-header {
  border: 0;
  padding: 0;
  border-radius: 0px !important;
}

/* line 95, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom2 .card .card-header h2 {
  padding: 0;
}

/* line 98, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom2 .card .card-header h2 a {
  padding: 0;
  font-size: 14px;
  font-weight: 500;
  width: 100%;
  text-align: left;
  position: relative;
  position: relative;
  padding: 13px 20px;
  background: #884ffb;
  box-shadow: 0px 5px 10px rgba(76, 110, 248, 0.3);
  color: #fff;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border: 0;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  border-radius: 0px;
}

/* line 116, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom2 .card .card-header h2 a::before {
  content: "\e627";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  font-family: "themify";
  font-size: 14px;
  right: 30px;
  color: #fff;
}

/* line 127, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom2 .card .card-header h2 a.collapsed {
  background: #f5f6ff;
  color: #415094;
  box-shadow: none;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  border-radius: 0px;
}

/* line 132, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom2 .card .card-header h2 a.collapsed::before {
  content: "\e62a";
  color: #415094;
}

/* line 137, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom2 .card .card-header h2 a:focus {
  text-decoration: none;
}

/* line 143, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom2 .card .card-body {
  padding: 24px 20px 30px 20px;
  background: #f5f6ff;
}

/* line 149, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom2 .card .card-body p {
  font-size: 14px;
  line-height: 26px;
  color: #555555;
  font-weight: 300;
  margin-bottom: 28px;
}

/* line 155, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.accordion_custom2 .card .card-body p:last-child {
  margin-bottom: 0;
}

/* line 164, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area {
  display: grid;
  grid-template-columns: 4fr 8fr;
  grid-gap: 30px;
}

@media (max-width: 575.98px) {

  /* line 164, G:/admin_project/8 admin/management_html/scss/_faq.scss */
  .messages_box_area {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 991px) {

  /* line 164, G:/admin_project/8 admin/management_html/scss/_faq.scss */
  .messages_box_area {
    grid-template-columns: 1fr;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 164, G:/admin_project/8 admin/management_html/scss/_faq.scss */
  .messages_box_area {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {

  /* line 164, G:/admin_project/8 admin/management_html/scss/_faq.scss */
  .messages_box_area {
    grid-template-columns: 6fr 6fr;
  }
}

@media only screen and (min-width: 1440px) and (max-width: 1679px) {

  /* line 164, G:/admin_project/8 admin/management_html/scss/_faq.scss */
  .messages_box_area {
    grid-template-columns: 5fr 7fr;
  }
}

/* line 184, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_list .serach_field_2 {
  width: 100%;
}

/* line 187, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_list ul {
  margin-top: 30px;
  margin-bottom: 30px;
}

/* line 191, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_list ul li a {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border: 1px solid #f1f5fa;
  padding: 15px;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 5px;
  margin-bottom: 5px;
}

/* line 200, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_list ul li a .message_pre_left {
  display: flex;
  align-items: center;
}

/* line 204, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_list ul li a .message_pre_left .message_preview_thumb {
  position: relative;
}

/* line 206, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_list ul li a .message_pre_left .message_preview_thumb img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

/* line 211, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_list ul li a .message_pre_left .message_preview_thumb .round-10 {
  border: 2px solid #f8f8fc;
  border-radius: 50%;
  position: absolute;
  bottom: 0;
  right: 5px;
  display: inline-block;
  height: 12px;
  width: 12px;
}

/* line 222, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_list ul li a .message_pre_left .messges_info {
  padding-left: 20px;
}

/* line 225, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_list ul li a .message_pre_left h4 {
  font-size: 14px;
  font-weight: 500;
  color: #222222;
  margin-bottom: 6px;
}

/* line 231, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_list ul li a .message_pre_left p {
  font-size: 13px;
  font-weight: 300;
  color: #676b84;
  margin-bottom: 0;
}

/* line 239, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_list ul li a .messge_time span {
  font-size: 12px;
  font-weight: 300;
  color: #192434;
  white-space: nowrap;
}

/* line 247, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_list ul li:hover a {
  background: #f1f5fa;
}

/* line 255, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .messge_time span {
  white-space: nowrap;
}

/* line 259, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .single_message_chat {
  margin-bottom: 50px;
  padding-right: 20%;
}

@media (max-width: 575.98px) {

  /* line 259, G:/admin_project/8 admin/management_html/scss/_faq.scss */
  .messages_box_area .messages_chat .single_message_chat {
    padding-right: 0%;
    margin-bottom: 30px;
  }
}

@media (max-width: 991px) {

  /* line 259, G:/admin_project/8 admin/management_html/scss/_faq.scss */
  .messages_box_area .messages_chat .single_message_chat {
    padding-right: 0%;
    margin-bottom: 30px;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {

  /* line 259, G:/admin_project/8 admin/management_html/scss/_faq.scss */
  .messages_box_area .messages_chat .single_message_chat {
    padding-right: 0%;
    margin-bottom: 30px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 259, G:/admin_project/8 admin/management_html/scss/_faq.scss */
  .messages_box_area .messages_chat .single_message_chat {
    padding-right: 0%;
    margin-bottom: 30px;
  }
}

/* line 278, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .single_message_chat .message_content_view {
  background: #f5f6ff;
  border-radius: 10px;
  padding: 30px;
}

/* line 282, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .single_message_chat .message_content_view.red_border {
  border: 1px solid #884ffb;
  background: #884ffb;
}

/* line 285, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .single_message_chat .message_content_view.red_border span,
.messages_box_area .messages_chat .single_message_chat .message_content_view.red_border p {
  color: #fff;
}

/* line 289, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .single_message_chat .message_content_view span {
  display: block;
  margin: 17px 0 17px 0;
}

/* line 293, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .single_message_chat .message_content_view p {
  font-size: 13px;
  font-weight: 300;
  color: #676b84;
  line-height: 26px;
}

/* line 300, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .single_message_chat .message_pre_left {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

/* line 306, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .single_message_chat .message_pre_left .message_preview_thumb img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

/* line 312, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .single_message_chat .message_pre_left .messges_info {
  padding-left: 20px;
}

/* line 315, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .single_message_chat .message_pre_left h4 {
  font-size: 16px;
  font-weight: 500;
  color: #222222;
  margin-bottom: 2px;
}

/* line 321, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .single_message_chat .message_pre_left p {
  font-size: 12px;
  font-weight: 300;
  color: #676b84;
  margin-bottom: 0;
}

/* line 328, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .single_message_chat.sender_message {
  padding-right: 0%;
  padding-left: 20%;
}

@media (max-width: 575.98px) {

  /* line 328, G:/admin_project/8 admin/management_html/scss/_faq.scss */
  .messages_box_area .messages_chat .single_message_chat.sender_message {
    padding-right: 0%;
    padding-left: 0%;
    margin-bottom: 30px;
  }
}

@media (max-width: 991px) {

  /* line 328, G:/admin_project/8 admin/management_html/scss/_faq.scss */
  .messages_box_area .messages_chat .single_message_chat.sender_message {
    padding-right: 0%;
    padding-left: 0%;
    margin-bottom: 30px;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {

  /* line 328, G:/admin_project/8 admin/management_html/scss/_faq.scss */
  .messages_box_area .messages_chat .single_message_chat.sender_message {
    padding-right: 0%;
    padding-left: 0%;
    margin-bottom: 30px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {

  /* line 328, G:/admin_project/8 admin/management_html/scss/_faq.scss */
  .messages_box_area .messages_chat .single_message_chat.sender_message {
    padding-right: 0%;
    padding-left: 0%;
    margin-bottom: 30px;
  }
}

/* line 351, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .single_message_chat.sender_message .message_pre_left {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* line 357, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .single_message_chat.sender_message .messges_info {
  padding-left: 0px;
  padding-right: 20px;
  text-align: right;
}

/* line 364, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .message_send_field {
  padding-top: 50px;
  display: grid;
  grid-template-columns: auto 110px;
  grid-gap: 10px;
}

@media (max-width: 575.98px) {

  /* line 364, G:/admin_project/8 admin/management_html/scss/_faq.scss */
  .messages_box_area .messages_chat .message_send_field {
    grid-template-columns: 1fr;
    margin-top: 20px;
  }
}

/* line 373, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .message_send_field input {
  color: #676b84;
  font-size: 13px;
  height: 40px;
  width: 100%;
  border-radius: 5px;
  padding-left: 25px;
  border: 1px solid #eee1e2;
  padding-right: 15px;
}

/* line 382, G:/admin_project/8 admin/management_html/scss/_faq.scss */
.messages_box_area .messages_chat .message_send_field input::placeholder {
  color: #676b84;
  font-weight: 300;
  opacity: 1;
}

/* line 1, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.email-sidebar {
  height: calc(100% - 3.9375rem);
}

/* line 8, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.email-sidebar button {
  border-radius: 30px;
}

/* line 11, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.email-sidebar h4 {
  font-weight: 600;
  font-size: 0.875rem;
  color: #333;
  padding: 2.5rem 0 3.125rem 1.5625rem;
}

/* line 17, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.email-sidebar ul {
  margin-bottom: 3.125rem;
}

/* line 19, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.email-sidebar ul li {
  display: block;
}

/* line 30, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.email-sidebar ul li.active a {
  background: rgba(136, 79, 251, 0.1);
  color: #884ffb !important;
}

/* line 35, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.email-sidebar ul li a {
  display: flex;
  padding: 10px 15px;
  transition: all 0.5s ease-in-out;
  font-weight: 600;
  font-size: 15px;
  color: #333;
  align-items: center;
  border-radius: 5px;
}

/* line 44, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.email-sidebar ul li a>span {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

/* line 49, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.email-sidebar ul li a:hover {
  background: rgba(136, 79, 251, 0.1);
  color: #884ffb !important;
}

/* line 52, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.email-sidebar ul li a:hover i {
  color: #884ffb;
}

/* line 56, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.email-sidebar ul li a i {
  margin-right: 0.7rem;
}

/* line 1, ../../../../../../xampp/htdocs/CRM_frontend/admin/public/frontend/scss/_predefine.scss */
/* line 66, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.primary_checkbox {
  display: inline-block;
  position: relative;
  width: 20px;
  height: 20px;
  margin: 0;
  flex: 20px 0 0;
  line-height: 20px;
}

/* line 9, ../../../../../../xampp/htdocs/CRM_frontend/admin/public/frontend/scss/_predefine.scss */
/* line 77, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.primary_checkbox .checkmark {
  position: relative;
  width: 20px;
  height: 20px;
  top: 0;
  left: 0;
  display: block;
  cursor: pointer;
  line-height: 20px;
  flex: 20px 0 0;
  border-radius: 5px;
}

/* line 26, ../../../../../../xampp/htdocs/CRM_frontend/admin/public/frontend/scss/_predefine.scss */
/* line 91, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.primary_checkbox input {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
}

/* line 35, ../../../../../../xampp/htdocs/CRM_frontend/admin/public/frontend/scss/_predefine.scss */
/* line 102, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.primary_checkbox input:checked~.checkmark:after {
  width: 100%;
  height: 100%;
  border: 0;
  transition: 0.3s;
  transform: scale(0);
}

/* line 44, ../../../../../../xampp/htdocs/CRM_frontend/admin/public/frontend/scss/_predefine.scss */
/* line 111, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.primary_checkbox input:checked~.checkmark {
  background: #884ffb !important;
  box-shadow: 0px 5px 10px rgba(59, 118, 239, 0.3) !important;
  transition: 0.3s;
}

/* line 52, ../../../../../../xampp/htdocs/CRM_frontend/admin/public/frontend/scss/_predefine.scss */
/* line 118, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.primary_checkbox input:checked~.checkmark::before {
  content: "\e64c";
  font-family: "themify";
  position: absolute;
  display: block;
  top: 0px;
  left: 3px;
  text-indent: 1px;
  color: #828bb2;
  background-color: transparent;
  border: 0px;
  -webkit-transform: rotate(8deg);
  -moz-transform: rotate(8deg);
  -ms-transform: rotate(8deg);
  -o-transform: rotate(8deg);
  transform: rotate(8deg);
  font-size: 10px;
  font-weight: 600;
  line-height: 20px;
  z-index: 99;
  color: #fff;
  transition: 0.3s;
}

/* line 76, ../../../../../../xampp/htdocs/CRM_frontend/admin/public/frontend/scss/_predefine.scss */
/* line 145, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.primary_checkbox .checkmark:after {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  content: "";
  width: 20px;
  height: 20px;
  background: transparent;
  border-radius: 5px;
  border: 0;
  transition: 0.3s;
  transform: scale(1);
  background: rgba(201, 247, 201, 0.34);
}

/* line 160, G:/admin_project/8 admin/management_html/scss/_mailbox.scss */
.primary_checkbox .checkmark.red_check::after {
  background: rgba(255, 226, 229, 0.34);
}

/* line 2, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.bayer_table tr {
  margin-bottom: 18px;
}

/* line 7, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.bayer_table tr td {
  padding-left: 0;
  padding-right: 0;
  vertical-align: middle;
  padding: 0;
  padding-bottom: 24px;
}

/* line 14, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.bayer_table tr td:first-child {
  width: 100px;
}

/* line 17, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.bayer_table tr td .byder_thumb {
  width: 81px;
  height: 81px;
  border-radius: 50%;
}

/* line 21, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.bayer_table tr td .byder_thumb.wh_56 {
  width: 56px;
  height: 56px;
  border-radius: 50%;
}

/* line 27, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.bayer_table tr td .byer_name {
  margin-bottom: 0;
  white-space: nowrap;
}

/* line 39, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.bayer_table2 tr td {
  padding-left: 0;
  padding-right: 0;
  vertical-align: middle;
  padding: 0;
  padding-bottom: 25px;
}

/* line 46, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.bayer_table2 tr td:first-child {
  width: 70px !important;
}

/* line 50, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.bayer_table2 tr td .byder_thumb {
  width: 56px;
  height: 56px;
  border-radius: 50%;
}

/* line 55, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.bayer_table2 tr td .byer_name {
  margin-bottom: 0;
}

/* line 62, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.blue_button {
  font-size: 12px;
  font-weight: 600;
  color: #2f90f7;
  background: #e7f3fe;
  display: inline-block;
  padding: 4px 16px;
  border-radius: 30px;
}

/* line 71, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.notification_btn {
  font-size: 10px;
  font-weight: 400;
  color: #fff;
  background: #508ff4;
  display: inline-block;
  border-radius: 3px;
  padding: 4px 14px;
}

/* line 79, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.notification_btn:hover {
  color: #fff;
}

/* line 82, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.notification_btn.yellow_btn {
  background: #ffbf43;
  color: #fff;
}

/* line 86, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.notification_btn.green_btn {
  background: #4be69d;
  color: #fff;
}

/* line 90, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.notification_btn.violate_btn {
  background: #9267ff;
  color: #fff;
}

/* line 97, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.f_w_100 {
  font-weight: 100;
}

/* line 100, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.f_w_200 {
  font-weight: 200;
}

/* line 103, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.f_w_300 {
  font-weight: 300;
}

/* line 106, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.f_w_400 {
  font-weight: 400;
}

/* line 109, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.f_w_500 {
  font-weight: 500;
}

/* line 112, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.f_w_600 {
  font-weight: 600;
}

/* line 115, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.f_w_700 {
  font-weight: 700;
}

/* line 118, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.f_w_800 {
  font-weight: 800;
}

/* line 121, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.f_w_900 {
  font-weight: 900;
}

/* line 125, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.f_15 {
  font-size: 15px;
}

/* line 128, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.f_16 {
  font-size: 16px;
}

/* line 132, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.color_blue {
  color: #2f90f7;
}

/* line 135, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.color_blue2 {
  color: #8ac6fb;
}

/* line 138, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.color_parpel {
  color: #833cdf;
}

/* line 142, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.color_parpel2 {
  color: #f65365;
}

/* line 146, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.color_pink {
  color: #fe80b2;
}

/* line 149, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.color_yellow {
  color: #ffcb24;
}

/* line 153, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.theme_bg_1 {
  background: #a37bfd !important;
}

/* line 156, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.theme_color_1 {
  color: #2d1967 !important;
}

/* line 159, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.theme_color_2 {
  color: #833cdf !important;
}

/* line 162, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.theme_color_3 {
  color: #fe80b2 !important;
}

/* line 165, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.theme_color_4 {
  color: #fe80b2 !important;
}

/* line 168, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.theme_color_5 {
  color: #2ff0f7 !important;
}

/* line 171, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.theme_color_6 {
  color: #8950fc !important;
}

/* line 174, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.theme_bg_6 {
  background: #8950fc !important;
}

/* line 177, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.mb_40 {
  margin-bottom: 40px;
}

/* line 181, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.box_shadow {
  box-shadow: 0 12px 30px rgba(46, 71, 101, 0.1);
}

/* line 187, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li {
  padding: 10px 15px;
}

/* line 189, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(1) {
  background-color: #8a75dd;
}

/* line 191, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(1)::before {
  content: "#8a75dd";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 198, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(2) {
  background-color: #846edb;
}

/* line 200, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(2)::before {
  content: "#8a75dd";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 207, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(3) {
  background-color: #7f68da;
}

/* line 209, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(3)::before {
  content: "#7f68da";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 216, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(4) {
  background-color: #7a62d8;
}

/* line 218, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(4)::before {
  content: "#7a62d8";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 225, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(5) {
  background-color: #755cd7;
}

/* line 227, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(5)::before {
  content: "#755cd7";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 234, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(6) {
  background-color: #7056d5;
}

/* line 236, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(6)::before {
  content: "#7056d5";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 243, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(7) {
  background-color: #6a50d4;
}

/* line 245, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(7)::before {
  content: "#6a50d4";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 252, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(8) {
  background-color: #654ad2;
}

/* line 254, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.primary-color ul li:nth-child(8)::before {
  content: "#654ad2";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 264, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li {
  padding: 10px 15px;
}

/* line 266, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(1) {
  background-color: #69b9ec;
}

/* line 268, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(1)::before {
  content: "#69b9ec";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 275, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(2) {
  background-color: #64b6eb;
}

/* line 277, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(2)::before {
  content: "#64b6eb";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 284, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(3) {
  background-color: #60b4ea;
}

/* line 286, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(3)::before {
  content: "#60b4ea";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 293, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(4) {
  background-color: #5bb2ea;
}

/* line 295, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(4)::before {
  content: "#5bb2ea";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 302, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(5) {
  background-color: #57b0e9;
}

/* line 304, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(5)::before {
  content: "#57b0e9";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 311, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(6) {
  background-color: #52aee9;
}

/* line 313, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(6)::before {
  content: "#52aee9";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 320, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(7) {
  background-color: #4eace8;
}

/* line 322, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(7)::before {
  content: "#4eace8";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 329, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(8) {
  background-color: #49aae8;
}

/* line 331, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.secondary-color ul li:nth-child(8)::before {
  content: "#49aae8";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 340, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li {
  padding: 10px 15px;
}

/* line 342, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(1) {
  background-color: #d7ff97;
}

/* line 344, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(1)::before {
  content: "#d7ff97";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 351, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(2) {
  background-color: #d1ff88;
}

/* line 353, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(2)::before {
  content: "#d1ff88";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 360, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(3) {
  background-color: #ccff78;
}

/* line 362, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(3)::before {
  content: "#ccff78";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 369, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(4) {
  background-color: #c6ff69;
}

/* line 371, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(4)::before {
  content: "#c6ff69";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 378, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(5) {
  background-color: #c0ff5a;
}

/* line 380, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(5)::before {
  content: "#c0ff5a";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 387, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(6) {
  background-color: #b4ff3b;
}

/* line 389, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(6)::before {
  content: "#b4ff3b";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 396, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(7) {
  background-color: #aeff2c;
}

/* line 398, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(7)::before {
  content: "#aeff2c";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 405, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(8) {
  background-color: #a9ff1d;
}

/* line 407, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.success-color ul li:nth-child(8)::before {
  content: "#a9ff1d";
  display: block;
  color: #fff;
  text-align: center;
}

/* line 416, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.ui-sortable-handle {
  cursor: move;
}

/* line 419, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.chart_height_300px {
  height: 400px;
}

/* line 422, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.amcharts-export-menu.amcharts-export-menu-top-right.amExportButton {
  display: none;
}

/* -------------------------------------------------------------------------- */
/*                             update news                           */
/* -------------------------------------------------------------------------- */
/* line 430, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.single_update_news {
  border-bottom: 1px solid #f3f4f3;
  padding: 15px 0 17px 0;
}

/* line 433, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.single_update_news:first-child {
  border-top: 1px solid #f3f4f3;
}

/* line 436, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.single_update_news h5 {
  margin-bottom: 0;
}

/* line 439, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.single_update_news p {
  margin-bottom: 0;
}

/* line 445, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.monthly_plan_wraper .single_plan {
  margin-bottom: 15px;
}

/* line 447, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.monthly_plan_wraper .single_plan:last-child {
  margin-bottom: 0;
}

/* line 451, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.monthly_plan_wraper .single_plan .plan_left .thumb {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
  padding: 12px;
  background: #f5f5ff;
  line-height: 1;
}

/* line 459, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.monthly_plan_wraper .single_plan .plan_left .thumb img {
  width: 100%;
}

/* line 463, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.monthly_plan_wraper .single_plan .plan_left h5 {
  font-size: 16px;
  font-weight: 700;
  color: #474d58;
  margin-bottom: 0;
}

/* line 469, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.monthly_plan_wraper .single_plan .plan_left span {
  font-size: 12px;
  color: #b5b5c3 !important;
  font-weight: 700;
  line-height: 12px;
}

/* line 476, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.monthly_plan_wraper .single_plan .brouser_btn {
  background: #f3f6f9;
  font-size: 12px;
  font-weight: 700;
  color: #757575;
  border-radius: 5px;
  padding: 8px 9px;
  line-height: 12px;
}

/* line 487, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.monthly_plan_wraper .total_blance {
  background: #f8faff;
  border-radius: 5px;
  padding: 18px 30px;
}

/* line 495, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.Sales_Details_plan .row .col-lg-6:nth-child(1) .thumb {
  background: #eee5ff;
}

/* line 498, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.Sales_Details_plan .row .col-lg-6:nth-child(2) .thumb {
  background: #ffe2e5;
}

/* line 501, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.Sales_Details_plan .row .col-lg-6:nth-child(3) .thumb {
  background: #c9f7f5;
}

/* line 504, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.Sales_Details_plan .row .col-lg-6:nth-child(4) .thumb {
  background: #d7f9ef;
}

/* line 508, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.Sales_Details_plan .single_plan {
  margin-bottom: 30px;
}

/* line 511, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.Sales_Details_plan .single_plan .plan_left .thumb {
  width: 45px;
  height: 45px;
  border-radius: 0.42rem;
  margin-right: 10px;
  padding: 12px;
  background: #f5f5ff;
  line-height: 1;
}

/* line 519, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.Sales_Details_plan .single_plan .plan_left .thumb img {
  width: 100%;
}

/* line 523, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.Sales_Details_plan .single_plan .plan_left h5 {
  font-size: 16px;
  font-weight: 700;
  color: #474d58;
  margin-bottom: 0;
}

/* line 529, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.Sales_Details_plan .single_plan .plan_left span {
  font-size: 12px;
  color: #b5b5c3 !important;
  font-weight: 700;
  line-height: 12px;
}

/* line 536, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.Sales_Details_plan .single_plan .brouser_btn {
  background: #f3f6f9;
  font-size: 12px;
  font-weight: 700;
  color: #757575;
  border-radius: 5px;
  padding: 8px 9px;
  line-height: 12px;
}

/* line 547, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.Sales_Details_plan .total_blance {
  background: #f8faff;
  border-radius: 5px;
  padding: 18px 30px;
}

/* line 554, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.sales_renew_btns a {
  color: #303e67;
}

/* line 556, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.sales_renew_btns a.active {
  color: #1c2d41;
  background-color: #f1f5fa;
}

/* line 563, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.common_tab_btn a {
  color: #7e8299;
  font-size: 12px;
  font-weight: 600;
  border-radius: 8px;
  padding: 5px 13px;
}

/* line 569, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.common_tab_btn a.active {
  color: #fff;
  background-color: #884ffb;
}

/* line 576, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.common_tab_btn2 a {
  color: #7e8299;
  font-size: 12px;
  font-weight: 600;
  border-radius: 8px;
  padding: 5px 13px;
}

/* line 582, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.common_tab_btn2 a.active {
  color: #fff;
  background-color: #f64e60;
}

/* line 588, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.white_card_body.renew_report_card {
  padding: 39px 50px;
}

/* line 591, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.dashboard_w_map {
  height: 270px;
}

/* line 594, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.dashboard_w_map .jvectormap-zoomin,
.dashboard_w_map .jvectormap-zoomout {
  width: 20px;
  height: 20px;
}

/* line 598, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.dashboard_w_map .jvectormap-zoomin {
  top: 10px;
}

/* line 601, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.dashboard_w_map .jvectormap-zoomin,
.dashboard_w_map .jvectormap-zoomout,
.dashboard_w_map .jvectormap-goback {
  position: absolute;
  left: auto;
  right: 0;
  display: inline-block;
  border-radius: 50%;
  background: #f8f8fc;
  padding: 5px;
  color: #435177;
  cursor: pointer;
  line-height: 20px;
  text-align: center;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}

/* line 618, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.single_progressbar h6 {
  font-size: 14px;
  font-weight: 400;
  color: #474d58;
}

/* line 624, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.single_progressbar .barfiller span {
  color: #8890b5 !important;
  font-size: 11px;
}

/* line 635, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.card_container {
  padding: 20px;
}

/* line 644, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.devices_btn {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

/* line 648, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.devices_btn a:not(:last-child) {
  margin-right: 15px;
}

/* line 652, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.devices_btn a {
  margin-bottom: 10px;
}

/* line 657, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card {
  background-color: #eee5ff;
  background-image: url(../img/shape_2.svg);
  background-size: cover;
  background-position: top right;
  background-repeat: no-repeat;
}

/* line 663, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card .media_card_body {
  padding: 25px;
  position: relative;
  z-index: 1;
}

/* line 667, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card .media_card_body::before {
  position: absolute;
  content: "";
  left: 0;
  top: 0;
  background: #ffffff;
  width: 100%;
  bottom: 0;
  top: 70px;
  z-index: -1;
  border-radius: 15px;
}

/* line 679, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card .media_card_body .media_card_list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 25px;
}

/* line 683, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card .media_card_body .media_card_list .single_media_card {
  background: #fff4de;
  border-radius: 15px;
  height: 136px;
  display: flex;
  align-items: self-start;
  flex-direction: column;
  justify-content: end;
  padding: 25px;
}

/* line 692, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card .media_card_body .media_card_list .single_media_card span {
  color: #ffc881;
  font-size: 12px;
  font-weight: 700;
}

/* line 697, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card .media_card_body .media_card_list .single_media_card h3 {
  font-size: 28px;
  font-weight: 900;
  margin-bottom: 0;
  color: #ffad44;
}

/* line 703, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card .media_card_body .media_card_list .single_media_card:nth-child(2) {
  background: #e2fff6;
}

/* line 705, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card .media_card_body .media_card_list .single_media_card:nth-child(2) span {
  color: #69e2bd;
}

/* line 708, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card .media_card_body .media_card_list .single_media_card:nth-child(2) h3 {
  color: #25c997;
}

/* line 712, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card .media_card_body .media_card_list .single_media_card:nth-child(3) {
  background: #ffe2e5;
}

/* line 714, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card .media_card_body .media_card_list .single_media_card:nth-child(3) span {
  color: #ff8895;
}

/* line 717, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card .media_card_body .media_card_list .single_media_card:nth-child(3) h3 {
  color: #f64e60;
}

/* line 721, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card .media_card_body .media_card_list .single_media_card:nth-child(4) {
  background: #e1f0ff;
}

/* line 723, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card .media_card_body .media_card_list .single_media_card:nth-child(4) span {
  color: #76b5f5;
}

/* line 726, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.social_media_card .media_card_body .media_card_list .single_media_card:nth-child(4) h3 {
  color: #3699ff;
}

/* line 736, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.sales_card_wrapper {
  background-color: #f65365;
  position: relative;
  background-image: url(../img/plane_thumb.svg);
  background-repeat: no-repeat;
  background-size: cover;
  background-size: 100% 58%;
  background-position: top left;
}

/* line 744, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.sales_card_wrapper .sales_card_body {
  background: #fff;
  border-radius: 15px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  row-gap: 40px;
  padding: 30px 70px;
  position: relative;
  margin: 200px 15px 0px 15px;
  bottom: 15px;
}

/* line 755, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.sales_card_wrapper .sales_card_body .single_sales span {
  font-size: 12px;
  font-weight: 700;
  color: #757575;
  display: block;
  margin-bottom: 5px;
}

/* line 762, G:/admin_project/8 admin/management_html/scss/_byer_table.scss */
.sales_card_wrapper .sales_card_body .single_sales h3 {
  font-size: 25px;
  color: #474d58;
  font-weight: 900;
  margin-bottom: 0;
}

/* line 2, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.notification_wrapper .single_notification {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 30px;
}

/* line 8, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.notification_wrapper .single_notification .notification_info h4 {
  color: #2e4765;
  margin-bottom: 0;
}

/* line 12, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.notification_wrapper .single_notification .notification_info p {
  font-size: 13px;
  font-weight: 400;
  color: #7f8b9f;
}

/* line 21, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.color_bg {
  width: 27px;
  height: 28px;
  border-radius: 5px;
  background: #c9f7f5;
  flex: 27px 0 0;
}

/* line 27, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.color_bg.color_lite_pink {
  background: #ffe2e5;
}

/* line 30, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.color_bg.color_lite_violate {
  background: #e2e4ff;
}

/* line 33, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.color_bg.color_lite_green {
  background: #e2fff0;
}

/* line 36, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.color_bg.color_lite_yellow {
  background: #fdffe2;
}

/* line 40, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.mr_15 {
  margin-right: 15px;
  margin-top: 7px;
}

/* line 46, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.property_wrapper .single_property {
  display: flex;
  align-items: center;
  margin-bottom: 22px;
  flex-wrap: wrap;
}

@media (max-width: 575.98px) {

  /* line 46, G:/admin_project/8 admin/management_html/scss/_notification.scss */
  .property_wrapper .single_property {
    flex-wrap: wrap;
  }
}

/* line 54, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.property_wrapper .single_property .property_thumb {
  margin-right: 40px;
  position: relative;
}

/* line 57, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.property_wrapper .single_property .property_thumb .img_up {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  opacity: 0;
  cursor: pointer;
}

/* line 66, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.property_wrapper .single_property .property_thumb img {
  width: 190px;
  height: 173px;
  border-radius: 5px;
}

/* line 72, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.property_wrapper .single_property .property_content {
  margin: 10px 0;
}

/* line 75, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.property_wrapper .single_property .property_content a h4 {
  font-size: 29px;
  color: #2e4765;
  font-weight: 700;
  margin-bottom: 0;
}

@media (max-width: 575.98px) {

  /* line 75, G:/admin_project/8 admin/management_html/scss/_notification.scss */
  .property_wrapper .single_property .property_content a h4 {
    font-size: 20px;
  }
}

/* line 83, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.property_wrapper .single_property .property_content a h4.Up_name {
  color: #d1d1d1;
}

/* line 87, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.property_wrapper .single_property .property_content a.property_location {
  font-size: 14px;
  color: #2f90f7;
  background: #e7f3fe;
  display: inline-block;
  padding: 10px 14px;
  display: inline-flex;
  align-items: center;
  margin-top: 24px;
  margin-bottom: 10px;
  border-radius: 5px;
}

/* line 98, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.property_wrapper .single_property .property_content a.property_location img {
  margin-right: 10px;
}

/* line 103, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.property_wrapper .single_property .property_content p {
  font-size: 14px;
  color: #7f8b9f;
  font-weight: 400;
}

/* line 107, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.property_wrapper .single_property .property_content p span {
  margin-right: 30px;
}

/* line 109, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.property_wrapper .single_property .property_content p span:last-child {
  margin: 0;
}

/* line 117, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.weather_box {
  background: #0090ff;
}

/* line 119, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.weather_box .weather_header {
  display: flex;
  position: relative;
  justify-content: space-between;
  align-items: center;
}

/* line 124, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.weather_box .weather_header .thumb {
  top: 0;
  right: 0;
}

/* line 127, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.weather_box .weather_header .thumb img {
  width: 160px;
}

@media (max-width: 575.98px) {

  /* line 127, G:/admin_project/8 admin/management_html/scss/_notification.scss */
  .weather_box .weather_header .thumb img {
    width: 100%;
  }
}

/* line 134, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.weather_box .weather_header .weather_info {
  display: inline-block;
}

/* line 136, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.weather_box .weather_header .weather_info h4 {
  font-size: 22px;
  color: #fff;
  font-weight: 700;
  margin-right: 25px;
  display: inline-block;
  vertical-align: top;
}

/* line 144, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.weather_box .weather_header .weather_info p {
  font-size: 22px;
  display: inline-block;
  color: #ffffff;
  vertical-align: top;
  font-weight: 400;
}

/* line 154, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.weather_box .weather_content ul {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

@media (max-width: 575.98px) {

  /* line 154, G:/admin_project/8 admin/management_html/scss/_notification.scss */
  .weather_box .weather_content ul {
    margin-top: 10px;
  }
}

/* line 163, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.weather_box .weather_content ul li span.selcias {
  font-size: 71px;
  font-weight: 700;
  color: #ffdc7b;
  text-transform: uppercase;
}

@media (max-width: 991px) {

  /* line 163, G:/admin_project/8 admin/management_html/scss/_notification.scss */
  .weather_box .weather_content ul li span.selcias {
    font-size: 30px;
  }
}

/* line 172, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.weather_box .weather_content ul li.weather_percent {
  font-size: 37px;
  font-weight: 400;
  color: #fff;
  display: flex;
  align-items: center;
}

@media (max-width: 991px) {

  /* line 172, G:/admin_project/8 admin/management_html/scss/_notification.scss */
  .weather_box .weather_content ul li.weather_percent {
    font-size: 28px;
  }
}

/* line 181, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.weather_box .weather_content ul li.weather_percent .thumb {
  margin-right: 22px;
}

/* line 187, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.weather_box .weather_content p {
  font-size: 28px;
  color: #fff;
  font-weight: 400;
}

@media (max-width: 991px) {

  /* line 187, G:/admin_project/8 admin/management_html/scss/_notification.scss */
  .weather_box .weather_content p {
    font-size: 18px;
  }
}

/* line 194, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.weather_box .weather_content p .eql {
  display: inline-block;
}

/* line 198, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.weather_box .weather_content p span span {
  font-size: 21px;
}

/* line 206, G:/admin_project/8 admin/management_html/scss/_notification.scss */
.option_icon {
  cursor: pointer;
}

/* line 5, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker-inline .datepicker .datepicker--nav .datepicker--nav-action {
  display: none;
}

/* line 8, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker-inline .datepicker .datepicker--content .datepicker--days .datepicker--days-names {
  margin: 30px 0 0px;
  padding: 15px 0;
}

/* line 12, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker-inline .datepicker {
  width: auto;
  background: rgba(0, 0, 0, 0);
  -webkit-box-shadow: none;
  box-shadow: none;
  padding: 0;
}

/* line 18, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker-inline .datepicker .datepicker--content .datepicker--days .datepicker--days-names .datepicker--day-name {
  font-size: 14px;
  font-family: "Mulish", sans-serif;
  color: #2b2b2b;
  font-size: 14px;
}

/* line 24, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker-inline .datepicker .datepicker--content .datepicker--days .datepicker--cells .datepicker--cell-day.-other-month- {
  color: #2b2b2b;
}

/* line 27, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker-inline .datepicker .datepicker--content .datepicker--days .datepicker--cells .datepicker--cell {
  font-weight: 700;
  font-size: 18px;
}

/* line 31, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker-inline .datepicker .datepicker--content .datepicker--days .datepicker--cells .datepicker--cell-day {
  height: 51px;
  color: #2b2b2b;
}

/* line 36, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker-inline .datepicker .datepicker--content .datepicker--days .datepicker--cells .datepicker--cell-day:hover {
  background: #884ffb;
  border: 2px solid #884ffb;
  color: #fff;
}

/* line 42, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker-inline .datepicker .datepicker--nav-title {
  color: #ff3000;
  font-size: 18px;
  font-weight: 900;
  font-family: "Mulish", sans-serif;
}

/* line 49, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker-inline .datepicker .datepicker--nav-title {
  border-radius: 8px;
  padding: 0 8px;
}

/* line 53, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker-inline .datepicker .datepicker--nav {
  border-bottom: none;
  padding: 0;
  text-transform: capitalize;
  margin-top: -12px;
}

/* line 59, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker-inline .datepicker .datepicker--nav-title i {
  margin-left: 10px;
  font-weight: 400;
  font-size: 30px;
  color: #2b2b2b;
  font-family: "Mulish", sans-serif;
  color: #884ffb;
}

/* line 67, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker-inline .datepicker .datepicker--day-name {
  color: #000;
  font-weight: bold;
}

/* line 73, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker--days-names {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: 8px 0 3px;
}

/* line 81, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker--cells {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* line 88, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker--cell-day {
  width: 14.28571%;
  height: 34px;
}

/* line 92, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker--cell {
  border-radius: 15px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 32px;
  z-index: 1;
}

/* line 110, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker--nav-title,
.default-datepicker .datepicker--nav-action {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  cursor: pointer;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

/* line 123, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker--cell.-current- {
  color: #fff !important;
  border-radius: 15px;
  font-weight: bold;
  border: 2px solid #884ffb;
  background: #884ffb;
}

/* line 130, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker--cell.-focus- {
  background: #884ffb;
  border: 2px solid #884ffb;
  color: #fff;
}

/* line 137, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.datepicker--cell-day.-other-month-,
.datepicker--cell-year.-other-decade- {
  color: #dedede;
}

/* line 141, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.date_picker_wrapper {
  padding: 0;
  padding: 45px 50px;
  border-radius: 15px;
  background: #fff;
  background-image: url(../img/dateBg.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

/* line 152, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.datepicker--cell.-selected- {
  border: none;
  background: #884ffb;
  color: #fff;
}

/* line 157, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.datepicker--cell.-selected-.-current- {
  border: none;
  background: #884ffb;
  color: #fff;
}

/* line 163, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker-inline .datepicker .datepicker--content .datepicker--days .datepicker--cells .datepicker--cell.-selected- {
  background: #884ffb;
  color: #fff;
  border-radius: 15px;
  position: relative;
}

/* line 171, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.datepicker--cell.-focus- {
  background: #884ffb;
  color: #fff;
}

/* line 175, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.datepicker--cell.-current- {
  color: #000;
  border-radius: 5px;
  font-weight: bold;
  border: 2px solid #884ffb;
}

/* line 181, G:/admin_project/8 admin/management_html/scss/_datepicker.scss */
.default-datepicker .datepicker-inline .datepicker .datepicker--content .datepicker--days .datepicker--cells .datepicker--cell-day.-other-month- {
  color: #8890b5;
}

/* line 2, G:/admin_project/8 admin/management_html/scss/_error_page.scss */
.erroe_page_wrapper .errow_wrap {
  display: flex;
  padding: 40px 0;
  align-items: center;
  justify-content: center;
}

/* line 7, G:/admin_project/8 admin/management_html/scss/_error_page.scss */
.erroe_page_wrapper .errow_wrap img {
  width: 100px;
  margin: auto;
}

/* line 11, G:/admin_project/8 admin/management_html/scss/_error_page.scss */
.erroe_page_wrapper .errow_wrap .error_heading {
  margin-top: 60px;
}

/* line 14, G:/admin_project/8 admin/management_html/scss/_error_page.scss */
.erroe_page_wrapper .errow_wrap .error_heading h3.headline {
  font-size: 200px;
  font-weight: 800;
  line-height: 1;
}

@media (max-width: 991px) {

  /* line 14, G:/admin_project/8 admin/management_html/scss/_error_page.scss */
  .erroe_page_wrapper .errow_wrap .error_heading h3.headline {
    font-size: 120px;
  }
}

/* line 24, G:/admin_project/8 admin/management_html/scss/_error_page.scss */
.erroe_page_wrapper .errow_wrap p {
  font-size: 18px;
  letter-spacing: 1px;
  color: #000;
  font-weight: 400;
  margin-top: 30px;
  line-height: 35px;
  margin-bottom: 30px;
}

/* line 1, G:/admin_project/8 admin/management_html/scss/_accordian_box.scss */
.card_box {
  border-radius: 15px;
}

/* line 3, G:/admin_project/8 admin/management_html/scss/_accordian_box.scss */
.card_box .white_box_tittle {
  background-color: #fff;
  padding: 50px;
  border-bottom: 1px solid #f2f4ff;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  position: relative;
  overflow: hidden;
}

/* line 12, G:/admin_project/8 admin/management_html/scss/_accordian_box.scss */
.card_box .box_body {
  padding: 50px;
}

/* line 14, G:/admin_project/8 admin/management_html/scss/_accordian_box.scss */
.card_box .box_body .card {
  margin-bottom: 40px;
  border: 0px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
  border-radius: 40px;
  -webkit-box-shadow: 0px 0px 25px 0px rgba(126, 55, 216, 0.05);
  box-shadow: 0px 0px 25px 0px rgba(126, 55, 216, 0.05);
}

/* line 23, G:/admin_project/8 admin/management_html/scss/_accordian_box.scss */
.card_box .box_body .card:last-child {
  margin-bottom: 0;
}

/* line 26, G:/admin_project/8 admin/management_html/scss/_accordian_box.scss */
.card_box .box_body .card .card-header {
  background-color: #fff;
  padding: 50px;
  border-bottom: 1px solid #f2f4ff;
  border-top-left-radius: 40px;
  border-top-right-radius: 40px;
  position: relative;
  overflow: hidden;
  padding: 0.75rem 1.25rem;
}

/* line 35, G:/admin_project/8 admin/management_html/scss/_accordian_box.scss */
.card_box .box_body .card .card-header h5 {
  margin-bottom: 0;
  text-transform: capitalize;
  font-weight: 800;
  line-height: 1;
  margin-bottom: 2px;
}

/* line 41, G:/admin_project/8 admin/management_html/scss/_accordian_box.scss */
.card_box .box_body .card .card-header h5 button {
  font-weight: 600;
  color: #1b3155;
}

/* line 47, G:/admin_project/8 admin/management_html/scss/_accordian_box.scss */
.card_box .box_body .card .card-body {
  padding: 50px;
  background-color: rgba(0, 0, 0, 0);
}

/* line 55, G:/admin_project/8 admin/management_html/scss/_accordian_box.scss */
.default-according.arrow_style button::before {
  right: 25px;
  position: absolute;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  content: "\e648";
  font-family: "themify";
  font-size: 14px;
}

/* line 64, G:/admin_project/8 admin/management_html/scss/_accordian_box.scss */
.default-according.arrow_style button.collapsed::before {
  right: 25px;
  position: absolute;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  content: "\e64b";
  font-family: "themify";
  font-size: 14px;
}

/* line 75, G:/admin_project/8 admin/management_html/scss/_accordian_box.scss */
.typography small {
  padding-left: 10px;
  color: #2c323f;
}

/*! PhotoSwipe Default UI CSS by Dmitry Semenov | photoswipe.com | MIT license */
/* line 3, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__button {
  width: 44px;
  height: 44px;
  position: relative;
  background: 0;
  cursor: pointer;
  overflow: visible;
  -webkit-appearance: none;
  display: block;
  border: 0;
  padding: 0;
  margin: 0;
  float: right;
  opacity: 0.75;
  -webkit-transition: opacity 0.2s;
  transition: opacity 0.2s;
  -webkit-box-shadow: none;
  box-shadow: none;
}

/* line 23, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__button:focus,
.pswp__button:hover {
  opacity: 1;
}

/* line 28, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__button:active {
  outline: 0;
  opacity: 0.9;
}

/* line 33, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__button::-moz-focus-inner {
  padding: 0;
  border: 0;
}

/* line 38, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__ui--over-close .pswp__button--close {
  opacity: 1;
}

/* line 42, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__button,
.pswp__button--arrow--left:before,
.pswp__button--arrow--right:before {
  background: url(../img/default-skin/default-skin.png) 0 0 no-repeat;
  background-size: 264px 88px;
  width: 44px;
  height: 44px;
}

@media (-webkit-min-device-pixel-ratio: 1.1),
(-webkit-min-device-pixel-ratio: 1.09375),
(min-resolution: 105dpi),
(min-resolution: 1.1dppx) {

  /* line 55, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
  .pswp--svg .pswp__button,
  .pswp--svg .pswp__button--arrow--left:before,
  .pswp--svg .pswp__button--arrow--right:before {
    background-image: url(../img/default-skin/default-skin.svg);
  }

  /* line 60, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
  .pswp--svg .pswp__button--arrow--left,
  .pswp--svg .pswp__button--arrow--right {
    background: 0;
  }
}

/* line 66, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__button--close {
  background-position: 0 -44px;
}

/* line 70, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__button--share {
  background-position: -44px -44px;
}

/* line 74, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__button--fs {
  display: none;
}

/* line 78, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--supports-fs .pswp__button--fs {
  display: block;
}

/* line 82, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--fs .pswp__button--fs {
  background-position: -44px 0;
}

/* line 86, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__button--zoom {
  display: none;
  background-position: -88px 0;
}

/* line 91, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--zoom-allowed .pswp__button--zoom {
  display: block;
}

/* line 95, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--zoomed-in .pswp__button--zoom {
  background-position: -132px 0;
}

/* line 99, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--touch .pswp__button--arrow--left,
.pswp--touch .pswp__button--arrow--right {
  visibility: hidden;
}

/* line 104, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__button--arrow--left,
.pswp__button--arrow--right {
  background: 0;
  top: 50%;
  margin-top: -50px;
  width: 70px;
  height: 100px;
  position: absolute;
}

/* line 114, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__button--arrow--left {
  left: 0;
}

/* line 118, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__button--arrow--right {
  right: 0;
}

/* line 122, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__button--arrow--left:before,
.pswp__button--arrow--right:before {
  content: "";
  top: 35px;
  background-color: rgba(0, 0, 0, 0.3);
  height: 30px;
  width: 32px;
  position: absolute;
}

/* line 132, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__button--arrow--left:before {
  left: 6px;
  background-position: -138px -44px;
}

/* line 137, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__button--arrow--right:before {
  right: 6px;
  background-position: -94px -44px;
}

/* line 142, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__counter,
.pswp__share-modal {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 150, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__share-modal {
  display: block;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  padding: 10px;
  position: absolute;
  z-index: 1600;
  opacity: 0;
  -webkit-transition: opacity 0.25s ease-out;
  transition: opacity 0.25s ease-out;
  -webkit-backface-visibility: hidden;
  will-change: opacity;
}

/* line 167, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__share-modal--hidden {
  display: none;
}

/* line 171, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__share-tooltip {
  z-index: 1620;
  position: absolute;
  background: #fff;
  top: 56px;
  border-radius: 2px;
  display: block;
  width: auto;
  right: 44px;
  -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25);
  -webkit-transform: translateY(6px);
  transform: translateY(6px);
  -webkit-transition: -webkit-transform 0.25s;
  transition: -webkit-transform 0.25s;
  transition: transform 0.25s;
  transition: transform 0.25s, -webkit-transform 0.25s;
  -webkit-backface-visibility: hidden;
  will-change: transform;
}

/* line 192, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__share-tooltip a {
  display: block;
  padding: 8px 12px;
  color: #000;
  text-decoration: none;
  font-size: 14px;
  line-height: 18px;
}

/* line 201, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__share-tooltip a:hover {
  text-decoration: none;
  color: #000;
}

/* line 206, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__share-tooltip a:first-child {
  border-radius: 2px 2px 0 0;
}

/* line 210, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__share-tooltip a:last-child {
  border-radius: 0 0 2px 2px;
}

/* line 214, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__share-modal--fade-in {
  opacity: 1;
}

/* line 218, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__share-modal--fade-in .pswp__share-tooltip {
  -webkit-transform: translateY(0);
  transform: translateY(0);
}

/* line 223, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--touch .pswp__share-tooltip a {
  padding: 16px 12px;
}

/* line 227, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
a.pswp__share--facebook:before {
  content: "";
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  top: -12px;
  right: 15px;
  border: 6px solid transparent;
  border-bottom-color: #fff;
  -webkit-pointer-events: none;
  -moz-pointer-events: none;
  pointer-events: none;
}

/* line 242, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
a.pswp__share--facebook:hover {
  background: #3e5c9a;
  color: #fff;
}

/* line 247, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
a.pswp__share--facebook:hover:before {
  border-bottom-color: #3e5c9a;
}

/* line 251, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
a.pswp__share--twitter:hover {
  background: #55acee;
  color: #fff;
}

/* line 256, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
a.pswp__share--pinterest:hover {
  background: #ccc;
  color: #ce272d;
}

/* line 261, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
a.pswp__share--download:hover {
  background: #ddd;
}

/* line 265, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__counter {
  position: absolute;
  left: 0;
  top: 0;
  height: 44px;
  font-size: 13px;
  line-height: 44px;
  color: #fff;
  opacity: 0.75;
  padding: 0 10px;
}

/* line 277, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__caption {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  min-height: 44px;
}

/* line 285, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__caption small {
  font-size: 11px;
  color: #bbb;
}

/* line 290, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__caption__center {
  text-align: center;
  max-width: 420px;
  margin: 0 auto;
  font-size: 13px;
  padding: 10px;
  line-height: 20px;
  color: #ccc;
}

/* line 300, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__caption--empty {
  display: none;
}

/* line 304, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__caption--fake {
  visibility: hidden;
}

/* line 308, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__preloader {
  width: 44px;
  height: 44px;
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -22px;
  opacity: 0;
  -webkit-transition: opacity 0.25s ease-out;
  transition: opacity 0.25s ease-out;
  will-change: opacity;
  direction: ltr;
}

/* line 322, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__preloader__icn {
  width: 20px;
  height: 20px;
  margin: 12px;
}

/* line 328, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__preloader--active {
  opacity: 1;
}

/* line 332, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__preloader--active .pswp__preloader__icn {
  background: url(preloader.gif) 0 0 no-repeat;
}

/* line 336, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--css_animation .pswp__preloader--active {
  opacity: 1;
}

/* line 340, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--css_animation .pswp__preloader--active .pswp__preloader__icn {
  -webkit-animation: clockwise 500ms linear infinite;
  animation: clockwise 500ms linear infinite;
}

/* line 345, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--css_animation .pswp__preloader--active .pswp__preloader__donut {
  -webkit-animation: donut-rotate 1000ms cubic-bezier(0.4, 0, 0.22, 1) infinite;
  animation: donut-rotate 1000ms cubic-bezier(0.4, 0, 0.22, 1) infinite;
}

/* line 350, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--css_animation .pswp__preloader__icn {
  background: 0;
  opacity: 0.75;
  width: 14px;
  height: 14px;
  position: absolute;
  left: 15px;
  top: 15px;
  margin: 0;
}

/* line 361, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--css_animation .pswp__preloader__cut {
  position: relative;
  width: 7px;
  height: 14px;
  overflow: hidden;
}

/* line 368, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--css_animation .pswp__preloader__donut {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 14px;
  height: 14px;
  border: 2px solid #fff;
  border-radius: 50%;
  border-left-color: transparent;
  border-bottom-color: transparent;
  position: absolute;
  top: 0;
  left: 0;
  background: 0;
  margin: 0;
}

@media screen and (max-width: 1024px) {

  /* line 385, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
  .pswp__preloader {
    position: relative;
    left: auto;
    top: auto;
    margin: 0;
    float: right;
  }
}

@-webkit-keyframes clockwise {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes clockwise {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-webkit-keyframes donut-rotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  50% {
    -webkit-transform: rotate(-140deg);
    transform: rotate(-140deg);
  }

  100% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}

@keyframes donut-rotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  50% {
    -webkit-transform: rotate(-140deg);
    transform: rotate(-140deg);
  }

  100% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}

/* line 446, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__ui {
  -webkit-font-smoothing: auto;
  visibility: visible;
  opacity: 1;
  z-index: 1550;
}

/* line 453, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__top-bar {
  position: absolute;
  left: 0;
  top: 0;
  height: 44px;
  width: 100%;
}

/* line 461, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__caption,
.pswp__top-bar,
.pswp--has_mouse .pswp__button--arrow--left,
.pswp--has_mouse .pswp__button--arrow--right {
  -webkit-backface-visibility: hidden;
  will-change: opacity;
  -webkit-transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
  transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
}

/* line 471, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--has_mouse .pswp__button--arrow--left,
.pswp--has_mouse .pswp__button--arrow--right {
  visibility: visible;
}

/* line 476, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__top-bar,
.pswp__caption {
  background-color: rgba(0, 0, 0, 0.5);
}

/* line 481, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__ui--fit .pswp__top-bar,
.pswp__ui--fit .pswp__caption {
  background-color: rgba(0, 0, 0, 0.3);
}

/* line 486, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__ui--idle .pswp__top-bar {
  opacity: 0;
}

/* line 490, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__ui--idle .pswp__button--arrow--left,
.pswp__ui--idle .pswp__button--arrow--right {
  opacity: 0;
}

/* line 495, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__ui--hidden .pswp__top-bar,
.pswp__ui--hidden .pswp__caption,
.pswp__ui--hidden .pswp__button--arrow--left,
.pswp__ui--hidden .pswp__button--arrow--right {
  opacity: 0.001;
}

/* line 502, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__ui--one-slide .pswp__button--arrow--left,
.pswp__ui--one-slide .pswp__button--arrow--right,
.pswp__ui--one-slide .pswp__counter {
  display: none;
}

/* line 508, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__element--disabled {
  display: none !important;
}

/* line 512, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--minimal--dark .pswp__top-bar {
  background: 0;
}

/*! PhotoSwipe main CSS by Dmitry Semenov | photoswipe.com | MIT license */
/* line 518, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp {
  display: none;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  overflow: hidden;
  -ms-touch-action: none;
  touch-action: none;
  z-index: 1500;
  -webkit-text-size-adjust: 100%;
  -webkit-backface-visibility: hidden;
  outline: 0;
}

/* line 534, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp * {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/* line 539, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp img {
  max-width: none;
}

/* line 543, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--animate_opacity {
  opacity: 0.001;
  will-change: opacity;
  -webkit-transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
  transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
}

/* line 550, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--open {
  display: block;
}

/* line 554, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--zoom-allowed .pswp__img {
  cursor: -webkit-zoom-in;
  cursor: zoom-in;
}

/* line 559, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--zoomed-in .pswp__img {
  cursor: -webkit-grab;
  cursor: grab;
}

/* line 564, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--dragging .pswp__img {
  cursor: -webkit-grabbing;
  cursor: grabbing;
}

/* line 569, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #000;
  opacity: 0;
  -webkit-backface-visibility: hidden;
  will-change: opacity;
}

/* line 581, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__scroll-wrap {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* line 590, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__container,
.pswp__zoom-wrap {
  -ms-touch-action: none;
  touch-action: none;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

/* line 601, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__container,
.pswp__img {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

/* line 611, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__zoom-wrap {
  position: absolute;
  width: 100%;
  -webkit-transform-origin: left top;
  transform-origin: left top;
  -webkit-transition: -webkit-transform 333ms cubic-bezier(0.4, 0, 0.22, 1);
  transition: -webkit-transform 333ms cubic-bezier(0.4, 0, 0.22, 1);
  transition: transform 333ms cubic-bezier(0.4, 0, 0.22, 1);
  transition: transform 333ms cubic-bezier(0.4, 0, 0.22, 1), -webkit-transform 333ms cubic-bezier(0.4, 0, 0.22, 1);
}

/* line 622, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__bg {
  will-change: opacity;
  -webkit-transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
  transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
}

/* line 628, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--animated-in .pswp__bg,
.pswp--animated-in .pswp__zoom-wrap {
  -webkit-transition: none;
  transition: none;
}

/* line 634, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__container,
.pswp__zoom-wrap {
  -webkit-backface-visibility: hidden;
}

/* line 639, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__item {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow: hidden;
}

/* line 648, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__img {
  position: absolute;
  width: auto;
  height: auto;
  top: 0;
  left: 0;
}

/* line 656, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__img--placeholder {
  -webkit-backface-visibility: hidden;
}

/* line 660, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__img--placeholder--blank {
  background: #222;
}

/* line 664, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp--ie .pswp__img {
  width: 100% !important;
  height: auto !important;
  left: 0;
  top: 0;
}

/* line 671, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__error-msg {
  position: absolute;
  left: 0;
  top: 50%;
  width: 100%;
  text-align: center;
  font-size: 14px;
  line-height: 16px;
  margin-top: -8px;
  color: #ccc;
}

/* line 683, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.pswp__error-msg a {
  color: #ccc;
  text-decoration: underline;
}

/* line 688, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.my-gallery {
  padding-right: 0;
}

/* line 692, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.my-gallery img {
  width: 100%;
}

/* line 696, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.my-gallery figure {
  margin-bottom: 30px;
}

/* line 700, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.my-gallery figcaption {
  display: none;
}

/* line 704, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.my-gallery.gallery-with-description img {
  padding: 10px !important;
  border: 1px solid #ddd !important;
  border-bottom: none !important;
  border-radius: 15px 15px 0 0 !important;
}

/* line 711, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.my-gallery.gallery-with-description a>div {
  border-top: none !important;
  margin-bottom: 0;
  padding: 5px 10px 10px 10px;
}

/* line 717, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.my-gallery.gallery-with-description h4 {
  margin-top: 0px;
}

/* line 721, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.my-gallery.gallery figure.img-hover a>div {
  overflow: hidden;
  border-radius: 15px;
}

/* line 726, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.my-gallery.gallery figure.img-hover.hover-12 a>div {
  background: #7e37d8;
}

/* line 730, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.my-gallery.gallery figure.img-hover.hover-12 a>div:hover img {
  opacity: 0.7;
}

/* line 734, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
#aniimated-thumbnials figure:nth-child(12),
#aniimated-thumbnials figure:nth-child(11),
#aniimated-thumbnials figure:nth-child(10),
#aniimated-thumbnials figure:nth-child(9) {
  margin-bottom: 30px;
}

/* line 741, G:/admin_project/8 admin/management_html/scss/_photoswip.scss */
.photo_gallery img {
  border-radius: 10px;
}

/*# sourceMappingURL=photoswipe.css.map */
/* line 2, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.Menu_NOtification_Wrap {
  background: #ffffff;
  box-shadow: 0px 10px 15px rgba(6, 0, 8, 0.22);
  border-radius: 10px;
  position: absolute;
  right: 0;
  width: 350px;
  transform: translateY(30px) translateX(20px);
  opacity: 0;
  visibility: hidden;
  transition: 0.3s;
}

@media (max-width: 575.98px) {

  /* line 2, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
  .Menu_NOtification_Wrap {
    width: 300px;
    transform: translateY(30px) translateX(55px);
  }
}

/* line 17, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.Menu_NOtification_Wrap.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(20px) translateX(20px);
}

@media (max-width: 575.98px) {

  /* line 17, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
  .Menu_NOtification_Wrap.active {
    transform: translateY(16px) translateX(55px);
  }
}

/* line 25, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.Menu_NOtification_Wrap .notification_Header {
  padding: 15px 20px !important;
  border-radius: 10px 10px 0px 0px !important;
  background: #8950fc;
}

/* line 29, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.Menu_NOtification_Wrap .notification_Header h4 {
  font-size: 15px !important;
  font-weight: 500 !important;
  color: #fff;
  margin-bottom: 0 !important;
}

/* line 36, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.Menu_NOtification_Wrap .Notification_body {
  padding: 20px 20px !important;
  height: 300px !important;
  overflow: auto;
}

/* line 40, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.Menu_NOtification_Wrap .Notification_body .single_notify {
  margin-bottom: 10px !important;
}

/* line 42, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.Menu_NOtification_Wrap .Notification_body .single_notify:last-child {
  margin-bottom: 0;
}

/* line 45, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.Menu_NOtification_Wrap .Notification_body .single_notify .notify_thumb {
  /*margin-right: 15px;*/
}

/* line 47, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.Menu_NOtification_Wrap .Notification_body .single_notify .notify_thumb img {
  width: 40px !important;
  height: 40px !important;
  border-radius: 5px !important;
}

/* line 55, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.Menu_NOtification_Wrap .Notification_body .single_notify .notify_content a h5 {
  margin-bottom: 0 !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

/* line 61, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.Menu_NOtification_Wrap .Notification_body .single_notify .notify_content p {
  font-size: 13px !important;
  font-weight: 400 !important;
  margin-bottom: 0 !important;
  line-height: 16px !important;
  margin-left: 8px !important;
}

/* line 70, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.Menu_NOtification_Wrap .nofity_footer {
  padding: 13px 20px !important;
  background: #f5f7f9;
  border-radius: 0 0 10px 10px !important;
}

/* line 75, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.Menu_NOtification_Wrap .nofity_footer .submit_button a {
  font-size: 12px !important;
}

/* line 84, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX {
  width: 360px;
  background: #ffffff;
  border-radius: 5px;
  position: fixed;
  bottom: 40px;
  right: 30px;
  z-index: 99999;
  opacity: 0;
  visibility: hidden;
  transform: translateX(50%);
  transition: 0.5s;
  box-shadow: 0px 10px 15px rgba(6, 0, 8, 0.22);
}

/* line 98, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX.active {
  opacity: 1;
  visibility: visible;
  transform: translateX(0%);
}

@media (max-width: 575.98px) {

  /* line 84, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
  .CHAT_MESSAGE_POPUPBOX {
    width: 300px;
    right: 15px;
    bottom: 15px;
  }
}

/* line 108, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_HEADER {
  background: #8950fc;
  border-radius: 5px 5px 0px 0px;
  padding: 27px 20px;
  position: relative;
}

/* line 113, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_HEADER .MSEESAGE_CHATBOX_CLOSE {
  position: absolute;
  right: 20px;
  top: 25px;
  cursor: pointer;
}

/* line 119, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_HEADER h3 {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 12px;
}

/* line 125, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_HEADER ul {
  display: flex;
  align-items: center;
}

/* line 128, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_HEADER ul li {
  margin-right: 5.5px;
}

/* line 132, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_HEADER ul li a .member_thumb img {
  width: 27px;
  height: 27px;
  transition: 0.3s;
  border-radius: 50%;
}

/* line 138, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_HEADER ul li a .member_thumb .more_member_count {
  background: rgba(255, 255, 255, 0.2);
  width: 27px;
  height: 27px;
  border-radius: 50%;
  text-align: center;
  line-height: 27px;
}

/* line 145, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_HEADER ul li a .member_thumb .more_member_count span {
  font-size: 12px;
  font-weight: 400;
  color: #fff;
}

/* line 153, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_HEADER ul li a:hover img {
  transform: translateY(-5px);
}

/* line 161, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .chat_input_box {
  background: #f5f7fb;
  border-radius: 0px 0px 5px 5px;
  padding-right: 10px;
}

/* line 165, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .chat_input_box input {
  background: #f5f7fb;
  height: 50px;
  border: 0;
  padding-left: 20px;
  font-size: 13px;
}

/* line 171, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .chat_input_box input::placeholder {
  color: #707db7;
  font-size: 13px;
  font-weight: 400;
}

/* line 177, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .chat_input_box .input-group-append {
  border-radius: 0 0 10px 10px;
}

/* line 178, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .chat_input_box .input-group-append .btn {
  background: #f5f7fb;
  font-size: 20px;
  color: #a0a7c9;
  padding: 0;
  line-height: 40px;
  position: relative;
  height: 50px;
}

/* line 186, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .chat_input_box .input-group-append .btn input {
  position: absolute;
  height: 100%;
  width: 100%;
  right: 0;
  top: 0;
  left: 0;
  bottom: 0;
  opacity: 0;
  cursor: pointer;
}

/* line 196, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .chat_input_box .input-group-append .btn input::placeholder {
  color: #707db7;
  font-size: 13px;
  font-weight: 400;
}

/* line 202, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .chat_input_box .input-group-append .btn i {
  font-size: 20px;
}

/* line 205, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .chat_input_box .input-group-append .btn:last-child {
  padding-right: 11px;
  padding-left: 11px;
  border-radius: 0px 10px 10px 0px;
}

/* line 214, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_BODY {
  padding: 20px;
  height: 354px;
  overflow: auto;
}

/* line 218, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_BODY .mesaged_send_date {
  font-size: 13px;
  font-weight: 400;
  color: #415094;
  text-align: center;
  margin-bottom: 12px;
  line-height: 1;
}

/* line 226, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_BODY .CHATING_SENDER {
  display: flex;
  align-items: flex-end;
  margin-bottom: 20px;
  padding-right: 70px;
}

/* line 231, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_BODY .CHATING_SENDER.CHATING_RECEIVEr {
  justify-content: flex-end;
  padding-right: 0px;
  padding-left: 60px;
}

/* line 235, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_BODY .CHATING_SENDER.CHATING_RECEIVEr .SMS_thumb {
  margin-right: 0;
  margin-left: 9.55px;
}

/* line 239, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_BODY .CHATING_SENDER.CHATING_RECEIVEr .SEND_SMS_VIEW {
  background: #8950fc;
}

/* line 243, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_BODY .CHATING_SENDER .SMS_thumb {
  margin-right: 9.55px;
}

/* line 245, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_BODY .CHATING_SENDER .SMS_thumb img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

/* line 251, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_BODY .CHATING_SENDER .SEND_SMS_VIEW {
  background: #4567ee;
  border-radius: 5px;
  padding: 10px 15px;
}

/* line 255, G:/admin_project/8 admin/management_html/scss/_notification_menu.scss */
.CHAT_MESSAGE_POPUPBOX .CHAT_POPUP_BODY .CHATING_SENDER .SEND_SMS_VIEW p {
  font-size: 13px;
  color: #ffffff;
  font-weight: 400;
  line-height: 23px;
}

/* Spinner */
/* line 3, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.theme_loder_1 div::after {
  background: #2d1967 !important;
}

/* line 6, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.blue_loder div::after {
  background: #2f90f7 !important;
}

/* line 9, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.perpel_loder div::after {
  background: #833cdf !important;
}

/* line 12, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.pink_loder div::after {
  background: #fe80b2 !important;
}

/* line 15, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.yellow_loder div::after {
  background: #ffcb24 !important;
}

/* line 19, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.green_loder div::after {
  background: #2ff0f7 !important;
}

/* line 23, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.Ring_1 div {
  border-color: #2d1967 transparent transparent transparent !important;
}

/* line 26, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.Ring_2 div {
  border-color: #2f90f7 transparent transparent transparent !important;
}

/* line 29, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.Ring_3 div {
  border-color: #833cdf transparent transparent transparent !important;
}

/* line 32, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.Ring_4 div {
  border-color: #fe80b2 transparent transparent transparent !important;
}

/* line 35, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.Ring_5 div {
  border-color: #ffcb24 transparent transparent transparent !important;
}

/* line 39, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.Ring_6 div {
  border-color: #2ff0f7 transparent transparent transparent !important;
}

/* line 44, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.colord_bg_1 div {
  background-color: #2d1967 !important;
}

/* line 47, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.colord_bg_2 div {
  background-color: #2f90f7 !important;
}

/* line 50, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.colord_bg_3 div {
  background-color: #833cdf !important;
}

/* line 53, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.colord_bg_4 div {
  background-color: #fe80b2 !important;
}

/* line 56, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.colord_bg_5 div {
  background-color: #ffcb24 !important;
}

/* line 59, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.colord_bg_6 div {
  background-color: #2ff0f7 !important;
}

/* line 68, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--spinner {
  display: inline-block;
  position: relative;
  color: official;
  height: 60px;
  width: 60px;
}

/* line 76, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--spinner div {
  animation: loader--spinner 1.2s linear infinite;
  transform-origin: 30px 30px;
}

/* line 81, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--spinner div:after {
  display: block;
  position: absolute;
  top: 3px;
  left: 27px;
  border-radius: 20%;
  content: " ";
  height: 10px;
  width: 5px;
}

/* line 92, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--spinner div:nth-child(1) {
  animation-delay: -1.1s;
  transform: rotate(0deg);
}

/* line 97, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--spinner div:nth-child(2) {
  animation-delay: -1s;
  transform: rotate(30deg);
}

/* line 102, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--spinner div:nth-child(3) {
  animation-delay: -0.9s;
  transform: rotate(60deg);
}

/* line 107, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--spinner div:nth-child(4) {
  animation-delay: -0.8s;
  transform: rotate(90deg);
}

/* line 112, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--spinner div:nth-child(5) {
  animation-delay: -0.7s;
  transform: rotate(120deg);
}

/* line 117, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--spinner div:nth-child(6) {
  animation-delay: -0.6s;
  transform: rotate(150deg);
}

/* line 122, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--spinner div:nth-child(7) {
  animation-delay: -0.5s;
  transform: rotate(180deg);
}

/* line 127, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--spinner div:nth-child(8) {
  animation-delay: -0.4s;
  transform: rotate(210deg);
}

/* line 132, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--spinner div:nth-child(9) {
  animation-delay: -0.3s;
  transform: rotate(240deg);
}

/* line 137, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--spinner div:nth-child(10) {
  animation-delay: -0.2s;
  transform: rotate(270deg);
}

/* line 142, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--spinner div:nth-child(11) {
  animation-delay: -0.1s;
  transform: rotate(300deg);
}

/* line 147, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--spinner div:nth-child(12) {
  animation-delay: 0s;
  transform: rotate(330deg);
}

@keyframes loader--spinner {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

/* Ring */
/* line 164, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--ring {
  display: inline-block;
  position: relative;
  height: 64px;
  width: 64px;
}

/* line 171, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--ring div {
  display: block;
  position: absolute;
  animation: loader--ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  border: 6px solid #fff;
  border-radius: 50%;
  box-sizing: border-box;
  margin: 6px;
  height: 51px;
  width: 51px;
}

/* line 183, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--ring div:nth-child(1) {
  animation-delay: -0.45s;
}

/* line 187, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--ring div:nth-child(2) {
  animation-delay: -0.3s;
}

/* line 191, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--ring div:nth-child(3) {
  animation-delay: -0.15s;
}

@keyframes loader--ring {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Roller */
/* line 208, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller {
  display: inline-block;
  position: relative;
  height: 64px;
  width: 64px;
}

/* line 215, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div {
  animation: loader--roller 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  transform-origin: 32px 32px;
}

/* line 220, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:after {
  display: block;
  position: absolute;
  border-radius: 50%;
  content: " ";
  margin: -3px 0 0 -3px;
  height: 6px;
  width: 6px;
}

/* line 230, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(1) {
  animation-delay: -0.036s;
}

/* line 234, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(1):after {
  top: 50px;
  left: 50px;
}

/* line 239, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(2) {
  animation-delay: -0.072s;
}

/* line 243, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(2):after {
  top: 54px;
  left: 45px;
}

/* line 248, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(3) {
  animation-delay: -0.108s;
}

/* line 252, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(3):after {
  top: 57px;
  left: 39px;
}

/* line 257, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(4) {
  animation-delay: -0.144s;
}

/* line 261, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(4):after {
  top: 58px;
  left: 32px;
}

/* line 266, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(5) {
  animation-delay: -0.18s;
}

/* line 270, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(5):after {
  top: 57px;
  left: 25px;
}

/* line 275, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(6) {
  animation-delay: -0.216s;
}

/* line 279, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(6):after {
  top: 54px;
  left: 19px;
}

/* line 284, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(7) {
  animation-delay: -0.252s;
}

/* line 288, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(7):after {
  top: 50px;
  left: 14px;
}

/* line 293, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(8) {
  animation-delay: -0.288s;
}

/* line 297, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--roller div:nth-child(8):after {
  top: 45px;
  left: 10px;
}

@keyframes loader--roller {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Spinner default */
/* line 315, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--default {
  display: inline-block;
  position: relative;
  height: 64px;
  width: 64px;
}

/* line 322, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--default div {
  position: absolute;
  animation: loader--default 1.2s linear infinite;
  border-radius: 50%;
  height: 5px;
  width: 5px;
}

/* line 330, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--default div:nth-child(1) {
  animation-delay: 0s;
  top: 29px;
  left: 53px;
}

/* line 336, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--default div:nth-child(2) {
  animation-delay: -0.1s;
  top: 18px;
  left: 50px;
}

/* line 342, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--default div:nth-child(3) {
  animation-delay: -0.2s;
  top: 9px;
  left: 41px;
}

/* line 348, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--default div:nth-child(4) {
  animation-delay: -0.3s;
  top: 6px;
  left: 29px;
}

/* line 354, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--default div:nth-child(5) {
  animation-delay: -0.4s;
  top: 9px;
  left: 18px;
}

/* line 360, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--default div:nth-child(6) {
  animation-delay: -0.5s;
  top: 18px;
  left: 9px;
}

/* line 366, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--default div:nth-child(7) {
  animation-delay: -0.6s;
  top: 29px;
  left: 6px;
}

/* line 372, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--default div:nth-child(8) {
  animation-delay: -0.7s;
  top: 41px;
  left: 9px;
}

/* line 378, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--default div:nth-child(9) {
  animation-delay: -0.8s;
  top: 50px;
  left: 18px;
}

/* line 384, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--default div:nth-child(10) {
  animation-delay: -0.9s;
  top: 53px;
  left: 29px;
}

/* line 390, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--default div:nth-child(11) {
  animation-delay: -1s;
  top: 50px;
  left: 41px;
}

/* line 396, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--default div:nth-child(12) {
  animation-delay: -1.1s;
  top: 41px;
  left: 50px;
}

@keyframes loader--default {

  0%,
  20%,
  80%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.5);
  }
}

/* Spinner elipses */
/* line 418, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--ellipsis {
  display: inline-block;
  position: relative;
  height: 64px;
  width: 64px;
}

/* line 425, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--ellipsis div {
  position: absolute;
  top: 27px;
  animation-timing-function: cubic-bezier(0, 1, 1, 0);
  border-radius: 50%;
  height: 11px;
  width: 11px;
}

/* line 434, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--ellipsis div:nth-child(1) {
  left: 6px;
  animation: loader--ellipsis1 0.6s infinite;
}

/* line 439, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--ellipsis div:nth-child(2) {
  left: 6px;
  animation: loader--ellipsis2 0.6s infinite;
}

/* line 444, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--ellipsis div:nth-child(3) {
  left: 26px;
  animation: loader--ellipsis2 0.6s infinite;
}

/* line 449, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--ellipsis div:nth-child(4) {
  left: 45px;
  animation: loader--ellipsis3 0.6s infinite;
}

@keyframes loader--ellipsis1 {
  0% {
    transform: scale(0);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes loader--ellipsis3 {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(0);
  }
}

@keyframes loader--ellipsis2 {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(19px, 0);
  }
}

/* Spinner grid */
/* line 487, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--grid {
  display: inline-block;
  position: relative;
  height: 64px;
  width: 64px;
}

/* line 494, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--grid div {
  position: absolute;
  animation: loader--grid 1.2s linear infinite;
  border-radius: 50%;
  height: 13px;
  width: 13px;
}

/* line 502, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--grid div:nth-child(1) {
  top: 6px;
  left: 6px;
  animation-delay: 0s;
}

/* line 508, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--grid div:nth-child(2) {
  top: 6px;
  left: 26px;
  animation-delay: -0.4s;
}

/* line 514, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--grid div:nth-child(3) {
  top: 6px;
  left: 45px;
  animation-delay: -0.8s;
}

/* line 520, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--grid div:nth-child(4) {
  top: 26px;
  left: 6px;
  animation-delay: -0.4s;
}

/* line 526, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--grid div:nth-child(5) {
  top: 26px;
  left: 26px;
  animation-delay: -0.8s;
}

/* line 532, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--grid div:nth-child(6) {
  top: 26px;
  left: 45px;
  animation-delay: -1.2s;
}

/* line 538, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--grid div:nth-child(7) {
  top: 45px;
  left: 6px;
  animation-delay: -0.8s;
}

/* line 544, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--grid div:nth-child(8) {
  top: 45px;
  left: 26px;
  animation-delay: -1.2s;
}

/* line 550, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--grid div:nth-child(9) {
  top: 45px;
  left: 45px;
  animation-delay: -1.6s;
}

@keyframes loader--grid {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

/* Spinner ripple */
/* line 570, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--ripple {
  display: inline-block;
  position: relative;
  height: 64px;
  width: 64px;
}

/* line 577, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--ripple div {
  position: absolute;
  animation: loader--ripple 1s cubic-bezier(0, 0.2, 0.8, 1) infinite;
  border: 4px solid;
  border-radius: 50%;
  opacity: 1;
}

/* line 585, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--ripple div:nth-child(2) {
  animation-delay: -0.5s;
}

@keyframes loader--ripple {
  0% {
    top: 28px;
    left: 28px;
    opacity: 1;
    height: 0;
    width: 0;
  }

  100% {
    top: -1px;
    left: -1px;
    opacity: 0;
    height: 58px;
    width: 58px;
  }
}

/* Spinner dual ring */
/* line 610, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--dual-ring {
  display: inline-block;
  height: 64px;
  width: 64px;
}

/* line 616, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--dual-ring:after {
  display: block;
  animation: loader--dual-ring 1.2s linear infinite;
  border: 5px solid #fff;
  border-radius: 50%;
  content: " ";
  margin: 1px;
  height: 46px;
  width: 46px;
}

@keyframes loader--dual-ring {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Spinner facebook */
/* line 640, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--facebook {
  display: inline-block;
  position: relative;
  height: 64px;
  width: 64px;
}

/* line 647, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--facebook div {
  display: inline-block;
  position: absolute;
  left: 6px;
  animation: loader--facebook 1.2s cubic-bezier(0, 0.5, 0.5, 1) infinite;
  width: 13px;
}

/* line 655, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--facebook div:nth-child(1) {
  left: 6px;
  animation-delay: -0.24s;
}

/* line 660, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--facebook div:nth-child(2) {
  left: 26px;
  animation-delay: -0.12s;
}

/* line 665, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.loader--facebook div:nth-child(3) {
  left: 45px;
  animation-delay: 0;
}

@keyframes loader--facebook {
  0% {
    top: 6px;
    height: 51px;
  }

  50%,
  100% {
    top: 19px;
    height: 26px;
  }
}

/**
 * Spinners colours
 * ---------------------------------------------------------
 */
/* Blue loaders in white background */
/* line 692, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.section--white {
  color: #00539f;
  background: #fff;
}

/* line 697, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.section--white .loader--spinner div:after,
.section--white .loader--roller div:after,
.section--white .loader--default div,
.section--white .loader--ellipsis div,
.section--white .loader--grid div,
.section--white .loader--facebook div {
  background: #00539f;
}

/* line 706, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.section--white .loader--ripple div {
  border-color: #00539f;
}

/* line 710, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.section--white .loader--ring div {
  border-color: #00539f transparent transparent transparent;
}

/* line 714, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.section--white .loader--dual-ring:after {
  border-color: #00539f transparent #00539f transparent;
}

/* White loaders in blue background */
/* line 721, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.section--blue {
  color: #fff;
  background: #00539f;
}

/* line 726, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.section--blue .loader--spinner div:after,
.section--blue .loader--roller div:after,
.section--blue .loader--default div,
.section--blue .loader--ellipsis div,
.section--blue .loader--grid div,
.section--blue .loader--facebook div {
  background: #fff;
}

/* line 735, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.section--blue .loader--ripple div {
  border-color: #fff;
}

/* line 739, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.section--blue .loader--ring div {
  border-color: #fff transparent transparent transparent;
}

/* line 743, G:/admin_project/8 admin/management_html/scss/_loadsers.scss */
.section--blue .loader--dual-ring:after {
  border-color: #fff transparent #fff transparent;
}

/* line 1, G:/admin_project/8 admin/management_html/scss/_typographi.scss */
.btn-outline-light {
  color: #ddd !important;
  border-color: #ddd !important;
}

/* line 7, G:/admin_project/8 admin/management_html/scss/_typographi.scss */
.icon-lists div {
  cursor: pointer;
  line-height: 60px;
  white-space: nowrap;
  border-radius: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

/* line 18, G:/admin_project/8 admin/management_html/scss/_typographi.scss */
.icon-lists div i {
  margin: 0 15px 0 10px;
  font-size: 24px;
}

/* line 2, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.thumb_34 {
  width: 34px;
  height: 34px;
}

/* line 6, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.thumb_62 {
  width: 62px;
  height: 62px;
}

/* line 10, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.thumb_54 {
  width: 54px;
  height: 54px;
}

/* line 15, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.social_media {
  width: 54px;
  height: 54px;
  border-radius: 8px;
  background: #7799ff;
  font-size: 30px;
  text-align: center;
  color: #fff;
  line-height: 54px;
}

/* line 24, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.social_media.insta_bg {
  background: #ff72c2;
}

/* line 27, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.social_media.twitter_bg {
  background: #56d5ff;
}

/* line 30, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.social_media.youtube_bg {
  background: #ff7171;
}

/* line 34, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.radius_50 {
  border-radius: 50%;
}

/* line 38, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.color_text_1 {
  color: #bda5f8 !important;
}

/* line 41, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.color_text_2 {
  color: #ffb98a !important;
}

/* line 44, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.color_text_3 {
  color: #a48cbc !important;
}

/* line 47, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.color_text_4 {
  color: #97c0ff !important;
}

/* line 50, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.color_text_5 {
  color: #474d58 !important;
}

/* line 54, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.color_text_6 {
  color: #545454 !important;
}

/* line 58, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.color_text_7 {
  color: #4489ff !important;
}

/* line 63, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_1 {
  font-size: 11px;
  font-weight: 700;
  background: #fff0eb;
  display: inline-block;
  border-radius: 2px;
  padding: 5px 17px;
  color: #ff7f57;
}

/* line 71, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_1:hover {
  color: #ff7f57;
}

/* line 75, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_2 {
  font-size: 11px;
  font-weight: 700;
  background: #fdebff;
  display: inline-block;
  border-radius: 2px;
  padding: 5px 17px;
  color: #c85ed4;
}

/* line 83, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_2:hover {
  color: #c85ed4;
}

/* line 87, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_3 {
  font-size: 11px;
  font-weight: 700;
  background: #e6f8ff;
  display: inline-block;
  border-radius: 2px;
  padding: 5px 17px;
  color: #0893c8;
}

/* line 95, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_3:hover {
  color: #0893c8;
}

/* line 99, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_4 {
  font-size: 11px;
  font-weight: 700;
  background: #e8ffeb;
  display: inline-block;
  border-radius: 2px;
  padding: 5px 17px;
  color: #50d863;
}

/* line 107, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_4:hover {
  color: #50d863;
}

/* line 111, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_5 {
  font-size: 14px;
  font-weight: 700;
  background: #e8ffeb;
  display: inline-block;
  border-radius: 4px;
  padding: 2px 10px;
  background: #d1eee7;
  color: #7bca9e;
}

/* line 120, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_5:hover {
  color: #7bca9e;
}

/* line 124, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_6 {
  font-size: 10px;
  font-weight: 700;
  background: #e8ffeb;
  display: inline-block;
  border-radius: 2px;
  padding: 6px 17px;
  background: #f16060;
  color: #fff;
  transition: 0.3s;
}

/* line 134, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_6:hover {
  color: #fff;
  box-shadow: 0 4px 7px rgba(255, 0, 0, 0.31);
}

/* line 139, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_7 {
  font-size: 10px;
  font-weight: 700;
  background: #e8ffeb;
  display: inline-block;
  border-radius: 2px;
  padding: 6px 17px;
  background: #567aed;
  color: #fff;
  transition: 0.3s;
}

/* line 149, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_7:hover {
  color: #fff;
  box-shadow: 0 4px 7px rgba(86, 122, 237, 0.31);
}

/* line 154, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_8 {
  font-size: 10px;
  font-weight: 700;
  background: #e8ffeb;
  display: inline-block;
  border-radius: 2px;
  padding: 6px 17px;
  background: #884ffb;
  color: #fff;
  transition: 0.3s;
}

/* line 164, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_8:hover {
  color: #fff;
  box-shadow: 0 4px 7px rgba(136, 79, 251, 0.43);
}

/* line 169, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_semi {
  font-size: 11px;
  font-weight: 700;
  background: #567aed;
  display: inline-block;
  border-radius: 2px;
  padding: 11px 70px;
  font-size: 14px;
  color: #fff;
  border-radius: 5px;
}

/* line 180, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_btn_semi:hover {
  color: #fff;
  box-shadow: 0 3px 11px #508ff4;
}

/* line 185, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_complete {
  font-size: 12px;
  font-weight: 700;
  background: #e2ffe2;
  display: inline-block;
  border-radius: 8px;
  padding: 5px 17px;
  color: #64e355;
}

/* line 193, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_complete:hover {
  background: #e2ffe2;
  color: #64e355;
}

/* line 198, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_active {
  font-size: 12px;
  font-weight: 700;
  background: #e2ffe2;
  display: inline-block;
  border-radius: 8px;
  padding: 5px 17px;
  color: #64e355;
}

/* line 206, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_active:hover {
  background: #e2ffe2;
  color: #64e355;
}

/* line 211, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_active2 {
  font-size: 12px;
  font-weight: 700;
  background: #e2f2ff;
  display: inline-block;
  border-radius: 8px;
  padding: 5px 17px;
  color: #59a0df;
}

/* line 219, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_active2:hover {
  background: #e2f2ff;
  color: #59a0df;
}

/* line 224, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_active3 {
  font-size: 12px;
  font-weight: 700;
  background: #ffeae6;
  display: inline-block;
  border-radius: 8px;
  padding: 5px 17px;
  color: #ff816d;
}

/* line 232, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_active3:hover {
  background: #ffeae6;
  color: #ff816d;
}

/* line 237, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_active4 {
  font-size: 12px;
  font-weight: 700;
  background: #fff8d5;
  display: inline-block;
  border-radius: 8px;
  padding: 5px 17px;
  color: #d2bf62;
}

/* line 245, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.badge_active4:hover {
  background: #fff8d5;
  color: #d2bf62;
}

/* line 250, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.mark_complete {
  font-size: 12px;
  font-weight: 700;
  background: #ffeff1;
  display: inline-block;
  border-radius: 8px;
  padding: 5px 17px;
  color: #fe8794;
}

/* line 258, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.mark_complete:hover {
  background: #ffeff1;
  color: #fe8794;
}

/* line 263, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.text_color_1 {
  font-size: 11px;
  font-weight: 400;
  color: #ff6d6d !important;
}

/* line 268, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.text_color_2 {
  font-size: 11px;
  font-weight: 400;
  color: #62d45e !important;
}

/* line 273, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.text_color_3 {
  font-size: 11px;
  font-weight: 400;
  color: #6fa5ec !important;
}

/* line 278, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.text_color_4 {
  color: #e64b55 !important;
}

/* line 281, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.text_color_5 {
  color: #4be69d !important;
}

/* line 284, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.text_color_6 {
  color: #706f9a !important;
}

/* line 287, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.text_color_7 {
  color: #59748a !important;
}

/* line 290, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.text_color_8 {
  color: #a48cbc !important;
}

/* line 293, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.text_color_9 {
  color: #a4cbff !important;
}

/* line 296, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.text_color_10 {
  color: #af81dc !important;
}

/* line 299, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.text_color_11 {
  color: #96d89c !important;
}

/* line 302, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.text_color_11 {
  color: #67349d !important;
}

/* line 307, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.switch_toggle {
  display: inline-block;
  height: 22px;
  position: relative;
  width: 40px;
  margin-bottom: 0;
}

/* line 314, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.switch_toggle input {
  opacity: 0;
}

/* line 317, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.switch_toggle .slider:before {
  bottom: 0px;
  content: "";
  height: 13px;
  left: 0px;
  left: 2px;
  position: absolute;
  transition: 0.3s;
  width: 13px;
  top: 1.4px;
  border: 3px solid #8950fc;
}

/* line 334, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.switch_toggle input:checked+.slider {
  background: transparent;
}

/* line 338, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.switch_toggle input:checked+.slider:before {
  transform: translateX(17px);
}

/* line 344, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.switch_toggle .slider.round {
  border-radius: 34px;
}

/* line 348, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.switch_toggle .slider.round:before {
  border-radius: 50%;
}

/* line 351, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.switch_toggle .switch_toggle input {
  display: none;
}

/* line 354, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.switch_toggle .slider {
  bottom: 0;
  cursor: pointer;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  transition: 0.3s;
  border: 3px solid #8950fc;
  background: transparent;
}

/* line 368, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.custom_bootstrap_nav a {
  color: #303e67;
}

/* line 373, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.nav.nav-tabs .nav-item.show:focus,
.nav.nav-tabs .nav-item.show.active,
.nav.nav-tabs .nav-link:focus,
.nav.nav-tabs .nav-link.active {
  color: #1761fd;
  background-color: #fff;
  border-color: transparent transparent #1761fd;
}

/* line 382, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.nav-pills .nav-item.show .nav-link,
.nav-pills .nav-link.active {
  background: #1761fd;
  color: #fff;
}

/* line 386, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.nav-pills .nav-item.show .nav-link,
.nav-pills .nav-link.active {
  background: #1761fd;
  color: #fff;
}

/* line 390, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
.nav.nav-pills {
  background-color: #f1f5fa;
  margin-top: 22px;
}

/* line 394, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
table.lms_table_active3.dataTable thead .sorting::before,
table.lms_table_active3.dataTable thead .sorting::after,
table.lms_table_active3.dataTable thead .sorting_asc::before,
table.lms_table_active3.dataTable thead .sorting_asc::after,
table.lms_table_active3.dataTable thead .sorting_desc::before,
table.lms_table_active3.dataTable thead .sorting_desc::after,
table.lms_table_active3.dataTable thead .sorting_asc_disabled::before,
table.lms_table_active3.dataTable thead .sorting_asc_disabled::after,
table.lms_table_active3.dataTable thead .sorting_desc_disabled::before,
table.lms_table_active3.dataTable thead .sorting_desc_disabled::after {
  position: absolute;
  bottom: 0.9em;
  display: block;
  opacity: 0.3;
}

/* line 409, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
table.lms_table_active3.dataTable thead .sorting::after,
table.lms_table_active3.dataTable thead .sorting_asc::after,
table.lms_table_active3.dataTable thead .sorting_desc::after,
table.lms_table_active3.dataTable thead .sorting_asc_disabled::after,
table.lms_table_active3.dataTable thead .sorting_desc_disabled::after {
  right: 1em;
  content: "\2193";
}

/* line 417, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
table.lms_table_active3.dataTable thead .sorting::before,
table.lms_table_active3.dataTable thead .sorting_asc::before,
table.lms_table_active3.dataTable thead .sorting_desc::before,
table.lms_table_active3.dataTable thead .sorting_asc_disabled::before,
table.lms_table_active3.dataTable thead .sorting_desc_disabled::before {
  right: 1.5em;
  content: "\2191";
}

/* line 425, G:/admin_project/8 admin/management_html/scss/_predefine.scss */
table.lms_table_active3.dataTable thead .sorting_asc::before,
table.lms_table_active3.dataTable thead .sorting_desc::after {
  opacity: 1;
}

/* line 1, G:/admin_project/8 admin/management_html/scss/_board.scss */
.board_wrapper {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  white-space: nowrap;
  overflow-x: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

/* line 10, G:/admin_project/8 admin/management_html/scss/_board.scss */
.board_wrapper .single_board {
  width: auto;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 auto;
  flex: 1 0 auto;
  max-height: 100%;
  padding-right: 12px;
  padding-bottom: 12px;
  outline: none !important;
  position: relative;
}

/* line 25, G:/admin_project/8 admin/management_html/scss/_board.scss */
.board_wrapper .single_board .main_board_card {
  max-height: 100%;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  margin-bottom: 12px;
  border-radius: 4px;
  padding: 20px;
  background: #f1f5fa;
}

/* line 45, G:/admin_project/8 admin/management_html/scss/_board.scss */
.board_wrapper .single_board .main_board_card .board_card_list .card {
  -webkit-box-shadow: 0px 2px 4px rgba(31, 30, 47, 0.03);
  box-shadow: 0px 2px 4px rgba(31, 30, 47, 0.03);
  margin-bottom: 24px;
  background-color: #fff;
  border: 1px solid #eceff5;
}

/* line 51, G:/admin_project/8 admin/management_html/scss/_board.scss */
.board_wrapper .single_board .main_board_card .board_card_list .card .card-body {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  min-height: 1px;
  padding: 1.25rem;
}

/* line 4, G:/admin_project/8 admin/management_html/scss/_product.scss */
.font-16 {
  font-size: 16px !important;
}

/* line 7, G:/admin_project/8 admin/management_html/scss/_product.scss */
.text-warning {
  color: #ffb822 !important;
}

/* line 10, G:/admin_project/8 admin/management_html/scss/_product.scss */
.col-auto {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}

/* line 18, G:/admin_project/8 admin/management_html/scss/_product.scss */
.total-payment .payment-title {
  color: #303e67;
  font-weight: 500;
}

/* line 22, G:/admin_project/8 admin/management_html/scss/_product.scss */
.total-payment {
  border: 1px solid #f1f5fa;
  background-color: #fff;
  border-radius: 3px;
}

/* line 27, G:/admin_project/8 admin/management_html/scss/_product.scss */
.total-payment .table tbody td,
.total-payment table tbody td,
.shopping-cart .table tbody td,
.shopping-cart table tbody td {
  padding: 20px 10px;
  border-top: 0 !important;
  border-bottom: 1px solid #f1f5fa;
}

/* line 32, G:/admin_project/8 admin/management_html/scss/_product.scss */
.shopping-cart .table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #eaf0f7;
}

/* line 36, G:/admin_project/8 admin/management_html/scss/_product.scss */
.text-muted {
  color: #a4abc5 !important;
}

/* line 39, G:/admin_project/8 admin/management_html/scss/_product.scss */
.ribbon1 {
  position: absolute;
  top: -6.1px;
  right: 10px;
}

/* line 44, G:/admin_project/8 admin/management_html/scss/_product.scss */
.ribbon1 span {
  display: block;
  padding: 6px 4px 0px;
  border-top-right-radius: 6px;
  width: 60px;
  font-size: 11px;
  font-weight: 500;
}

/* line 52, G:/admin_project/8 admin/management_html/scss/_product.scss */
.ribbon1 span.rib1-primary::before,
.ribbon1 span.rib1-primary {
  background: #884ffb;
}

/* line 55, G:/admin_project/8 admin/management_html/scss/_product.scss */
.ribbon1.rib1-primary::after {
  border-top-color: #884ffb;
}

/* line 58, G:/admin_project/8 admin/management_html/scss/_product.scss */
.ribbon1::after {
  position: absolute;
  content: "";
  width: 0;
  height: 0;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  border-top: 10px solid;
  border-top-color: currentcolor;
}

/* line 68, G:/admin_project/8 admin/management_html/scss/_product.scss */
.ribbon1 span.rib1-primary::before,
.ribbon1 span.rib1-primary {
  background: #884ffb;
}

/* line 71, G:/admin_project/8 admin/management_html/scss/_product.scss */
.ribbon1 span::before {
  height: 6px;
  width: 4px;
  left: -4px;
  top: 0;
}

/* line 77, G:/admin_project/8 admin/management_html/scss/_product.scss */
.ribbon1 span::before,
.ribbon1 span::after {
  position: absolute;
  content: "";
}

/* line 81, G:/admin_project/8 admin/management_html/scss/_product.scss */
.ribbon1 span.rib1-primary::after {
  background: #884ffb;
}

/* line 84, G:/admin_project/8 admin/management_html/scss/_product.scss */
.ribbon1 span::after {
  height: 6px;
  width: 6px;
  left: -6px;
  top: 0;
  border-radius: 8px 8px 0 0;
}

/* line 91, G:/admin_project/8 admin/management_html/scss/_product.scss */
.ribbon1 span::before,
.ribbon1 span::after {
  position: absolute;
  content: "";
}

/* line 98, G:/admin_project/8 admin/management_html/scss/_product.scss */
.single-pro-detail .pro-price span {
  font-size: 14px;
  color: #8997bd;
}

/* line 102, G:/admin_project/8 admin/management_html/scss/_product.scss */
.single-pro-detail .quantity input {
  width: 14%;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: calc(24px + 12px);
}

/* line 109, G:/admin_project/8 admin/management_html/scss/_product.scss */
.single-pro-detail .custom-border {
  width: 60px;
  height: 2px;
  background-color: #1d2c48;
}

/* line 114, G:/admin_project/8 admin/management_html/scss/_product.scss */
.single-pro-detail .product-review li {
  margin: 0;
}

/* line 116, G:/admin_project/8 admin/management_html/scss/_product.scss */
.single-pro-detail .product-review li i {
  font-size: 16px;
}

/* line 120, G:/admin_project/8 admin/management_html/scss/_product.scss */
.single-pro-detail .pro-price {
  color: #303e67;
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 0;
}

/* line 126, G:/admin_project/8 admin/management_html/scss/_product.scss */
.single-pro-detail .pro-features {
  margin-bottom: 20px;
}

/* line 128, G:/admin_project/8 admin/management_html/scss/_product.scss */
.single-pro-detail .pro-features li {
  line-height: 26px;
  color: #a4abc5;
}

/* line 131, G:/admin_project/8 admin/management_html/scss/_product.scss */
.single-pro-detail .pro-features li:before {
  content: "\f00c" !important;
  font-family: "Font Awesome 5 Free" !important;
  font-weight: 600;
  font-size: 12px;
  color: #03d87f !important;
  display: inline-block;
  margin-right: 8px;
}

/* line 2, G:/admin_project/8 admin/management_html/scss/_todo.scss */
.todo_wrapper .single_todo {
  margin-bottom: 25px;
}

/* line 6, G:/admin_project/8 admin/management_html/scss/_todo.scss */
.todo_wrapper .single_todo .lodo_left .todo_head h5 {
  color: #474d58;
  line-height: 24px;
}

/* line 10, G:/admin_project/8 admin/management_html/scss/_todo.scss */
.todo_wrapper .single_todo .lodo_left .todo_head p {
  line-height: 16px;
}

/* line 20, G:/admin_project/8 admin/management_html/scss/_todo.scss */
.bar_line {
  height: 49px;
  width: 4px;
  background: #64e355;
  border-radius: 5px;
}

/* line 25, G:/admin_project/8 admin/management_html/scss/_todo.scss */
.bar_line.red_line {
  background: #fe8794;
}

/* line 30, G:/admin_project/8 admin/management_html/scss/_todo.scss */
.world_list_wraper {
  background: transparent;
  border-radius: 15px;
  padding-top: 25px;
  padding-right: 15px;
  padding-left: 15px;
}

/* line 38, G:/admin_project/8 admin/management_html/scss/_todo.scss */
.project_complete {
  background: #e2fff6;
  background-image: url(../img/pro_complete.png);
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  border-radius: 20px;
  padding: 30px 50px;
}

/* line 46, G:/admin_project/8 admin/management_html/scss/_todo.scss */
.project_complete .single_pro {
  margin-bottom: 25px;
}

/* line 48, G:/admin_project/8 admin/management_html/scss/_todo.scss */
.project_complete .single_pro .probox {
  width: 14px;
  height: 13px;
  border-radius: 3px;
  background: rgba(244, 135, 107, 0.34);
  margin-right: 10px;
  position: relative;
  top: 5px;
  flex: 14px 0 0;
}

/* line 57, G:/admin_project/8 admin/management_html/scss/_todo.scss */
.project_complete .single_pro .probox.blue_box {
  background: rgba(47, 98, 165, 0.34);
}

/* line 62, G:/admin_project/8 admin/management_html/scss/_todo.scss */
.project_complete .single_pro .box_content h4 {
  font-size: 25px;
  font-weight: 900;
  color: #f4876b;
  margin-bottom: 0;
}

/* line 67, G:/admin_project/8 admin/management_html/scss/_todo.scss */
.project_complete .single_pro .box_content h4.bluish_text {
  color: #2f62a5;
}

/* line 71, G:/admin_project/8 admin/management_html/scss/_todo.scss */
.project_complete .single_pro .box_content span {
  font-size: 12px;
  color: #c5bab7;
  font-weight: 600;
}

/* line 75, G:/admin_project/8 admin/management_html/scss/_todo.scss */
.project_complete .single_pro .box_content span.grayis_text {
  color: #a6b1bf;
}

/* line 2, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.mini_sidebar {
  width: 136px;
  z-index: 1;
  overflow: visible !important;
  min-width: 136px;
  max-width: 136px;
  position: absolute;
  z-index: 9999999;
  height: auto;
  top: 0;
  bottom: 0;
  left: 0;
}

/* line 14, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar .menu-text {
  font-size: 12px;
  color: #b1bdcb;
  font-weight: 600;
  margin: 0;
  padding: 12px 0 12px 24px;
  text-transform: uppercase;
  text-align: center;
  padding-left: 0;
}

/* line 23, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar .menu-text span {
  display: none;
}

/* line 26, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar .menu-text i {
  display: block;
}

/* line 30, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar .mini_logo {
  display: block;
  width: 136px;
}

/* line 35, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar .logo {
  width: 136px !important;
  padding: 10px 0;
  margin: 0;
  justify-content: center !important;
}

/* line 41, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar .large_logo {
  display: none;
}

/* line 44, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar .small_logo {
  display: block;
}

/* line 48, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar .wp-has-submenu::before {
  display: none;
}

/* line 52, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar .sidebar-header {
  padding-left: 11px;
}

/* line 55, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar .update_sidebar img {
  max-width: 40px !important;
}

/* line 59, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar #adminmenu ul li ul {
  overflow: visible;
  position: absolute;
  z-index: 999;
  left: 100% !important;
  top: 100%;
}

/* line 66, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar #adminmenu>li.wp-has-submenu {
  position: relative;
  width: 79px;
  text-align: center;
}

/* line 70, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar #adminmenu>li ul {
  margin-left: 0;
  padding-left: 0;
}

/* line 74, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar #adminmenu>li ul li a {
  white-space: nowrap;
  padding-left: 18px;
  color: #144339;
  width: 194px;
  padding-left: 20px !important;
}

/* line 81, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar #adminmenu>li>a {
  /*padding-left: 0px;*/
  display: grid;
  grid-template-columns: 44px auto !important;
}

/* line 88, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li>a.wp-has-submenu::after {
  display: none;
}

/* line 91, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li>a .nav_icon_small {
  width: 136px;
}

/* line 95, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li>a .wp-menu-name {
  display: none;
}

/* line 98, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li>a:hover,
.sidebar.mini_sidebar #adminmenu>li>a:active {
  width: 330px;
}

/* line 102, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li>a:hover .nav_icon_small,
.sidebar.mini_sidebar #adminmenu>li>a:active .nav_icon_small {
  display: inline-block;
}

/* line 105, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li>a:hover .wp-menu-name,
.sidebar.mini_sidebar #adminmenu>li>a:active .wp-menu-name {
  display: inline-block;
}

/* line 108, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li>a:hover.wp-has-submenu::after,
.sidebar.mini_sidebar #adminmenu>li>a:active.wp-has-submenu::after {
  display: inline-block;
}

/* line 113, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li:hover>a,
.sidebar.mini_sidebar li.mm-active>a {
  width: 330px !important;
}

/* line 117, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li:hover>a:hover,
.sidebar.mini_sidebar li:hover>a.active,
.sidebar.mini_sidebar li.mm-active>a:hover,
.sidebar.mini_sidebar li.mm-active>a.active {
  width: 330px;
}

/* line 121, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li:hover>a:hover .nav_icon_small,
.sidebar.mini_sidebar li:hover>a.active .nav_icon_small,
.sidebar.mini_sidebar li.mm-active>a:hover .nav_icon_small,
.sidebar.mini_sidebar li.mm-active>a.active .nav_icon_small {
  display: inline-block;
}

/* line 124, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li:hover>a:hover .wp-menu-name,
.sidebar.mini_sidebar li:hover>a.active .wp-menu-name,
.sidebar.mini_sidebar li.mm-active>a:hover .wp-menu-name,
.sidebar.mini_sidebar li.mm-active>a.active .wp-menu-name {
  display: inline-block;
}

/* line 127, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li:hover>a:hover.wp-has-submenu::after,
.sidebar.mini_sidebar li:hover>a.active.wp-has-submenu::after,
.sidebar.mini_sidebar li.mm-active>a:hover.wp-has-submenu::after,
.sidebar.mini_sidebar li.mm-active>a.active.wp-has-submenu::after {
  display: inline-block;
}

/* line 136, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li.mm-active:hover {
  overflow: visible;
}

/* line 139, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li.mm-active>a {
  background: #fff;
}

/* line 142, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li.mm-active>a .nav_icon_small {
  display: inline-block;
}

/* line 145, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li.mm-active>a .wp-menu-name {
  display: inline-block;
}

/* line 148, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li.mm-active>a.wp-has-submenu::after {
  display: inline-block;
}

/* line 152, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar li.mm-active ul {
  left: 100%;
  left: 136px;
  position: absolute;
  top: 100%;
  width: 194px;
  z-index: 1000;
  margin-left: 0;
  padding-left: 0;
}

/* line 171, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
#main-content.mini_main_content {
  margin-left: 70px;
  width: calc(100% - 70px);
}

@media (max-width: 1370px) {

  /* line 171, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
  #main-content.mini_main_content {
    width: calc(100% - 70px);
  }
}

@media (min-width: 1200px) {

  /* line 171, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
  #main-content.mini_main_content {
    padding: 30px 30px;
  }
}

@media (max-width: 991px) {

  /* line 171, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
  #main-content.mini_main_content {
    width: 100%;
    margin-top: 0;
  }
}

/* line 189, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar ul li ul {
  background: #144339;
  opacity: 0.9;
  position: absolute;
  left: 100%;
}

/* line 196, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.collaspe_icon.open_miniSide {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

@media (max-width: 991px) {

  /* line 196, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
  .collaspe_icon.open_miniSide {
    display: none;
  }
}

/* line 204, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.collaspe_icon.open_miniSide i {
  color: #144339;
  cursor: pointer;
  font-size: 18px;
  margin-right: 15px;
}

/* line 218, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar #adminmenu>li.metis_submenu_up_collaspe ul {
  top: auto;
  bottom: 100%;
}

/* line 226, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar #adminmenu li ul li.metis_submenu {
  position: relative;
}

/* line 228, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar #adminmenu li ul li.metis_submenu ul {
  top: 100%;
  left: 0 !important;
  position: relative;
  background: rgba(0, 0, 0, 0.05);
}

/* line 236, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.dark_sidebar.mini_sidebar #adminmenu>li>a:hover,
.sidebar.dark_sidebar.mini_sidebar #adminmenu>li>a:active {
  background: #1e1e2d;
}

/* line 239, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.dark_sidebar.mini_sidebar #adminmenu>li.mm-active>a {
  background: #1e1e2d;
  color: #f65365 !important;
}

/* line 243, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.mini_sidebar.dark_sidebar #adminmenu>li ul {
  background: #1e1e2d;
  opacity: 1;
}

/* line 247, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.dark_sidebar.mini_sidebar #adminmenu>li ul li a {
  color: #fff;
  width: 194px;
}

/* line 251, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.dark_sidebar.mini_sidebar #adminmenu>li ul li a {
  color: #fff;
  width: 194px;
}

/* line 255, G:/admin_project/8 admin/management_html/scss/_mini_menu.scss */
.sidebar.dark_sidebar.mini_sidebar #adminmenu>li.mm-active ul {
  width: 194px;
}

/* line 1, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
.custom-card {
  overflow: hidden;
  padding: 30px;
}

@media (max-width: 575.98px) {

  /* line 1, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
  .custom-card {
    padding: 0;
  }
}

/* line 7, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
.custom-card.card {
  margin-bottom: 30px;
  border: none;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
  border-radius: 15px;
  -webkit-box-shadow: 0 0 37px rgba(8, 21, 66, 0.05);
  box-shadow: 0 0 37px rgba(8, 21, 66, 0.05);
  border: none !important;
}

/* line 17, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
.custom-card.card .card-header {
  background-color: #fff;
  padding: 0px;
  border-bottom: 1px solid #ecf3fa;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  position: relative;
}

/* line 24, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
.custom-card.card .card-header img {
  border-radius: 50%;
  margin-top: -100px;
  -webkit-transform: scale(1.5);
  transform: scale(1.5);
}

/* line 32, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
.custom-card .card-profile {
  text-align: center;
}

/* line 34, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
.custom-card .card-profile img {
  height: 150px;
  padding: 7px;
  background-color: #fff;
  z-index: 1;
  position: relative;
}

/* line 42, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
.custom-card .card-social {
  text-align: center;
}

/* line 44, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
.custom-card .card-social li {
  display: inline-block;
  padding: 15px 0;
}

/* line 47, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
.custom-card .card-social li a {
  padding: 13px;
  color: #bcc6de;
  font-size: 16px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

/* line 56, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
.custom-card .profile-details h6 {
  margin-bottom: 30px;
  margin-top: 10px;
  color: rgba(43, 43, 43, 0.7);
  font-size: 14px;
}

/* line 62, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
.custom-card .card-footer {
  padding: 0;
  background-color: #fff;
  border-top: 1px solid #ecf3fa;
  padding: 0px;
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
}

@media (max-width: 575.98px) {

  /* line 62, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
  .custom-card .card-footer {
    padding: 0 15px 15px 15px;
  }
}

/* line 72, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
.custom-card .card-footer>div h6 {
  font-size: 14px;
  color: rgba(43, 43, 43, 0.7);
}

@media (max-width: 575.98px) {

  /* line 72, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
  .custom-card .card-footer>div h6 {
    font-size: 13px;
  }
}

/* line 79, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
.custom-card .card-footer>div h3 {
  margin-bottom: 0;
  font-size: 24px;
}

@media (max-width: 575.98px) {

  /* line 79, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
  .custom-card .card-footer>div h3 {
    font-size: 20px;
  }
}

/* line 86, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
.custom-card .card-footer>div+div {
  border-left: 1px solid #efefef;
}

/* line 89, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
.custom-card .card-footer>div {
  padding: 15px;
  text-align: center;
}

@media (max-width: 575.98px) {

  /* line 89, G:/admin_project/8 admin/management_html/scss/_profilebox_new.scss */
  .custom-card .card-footer>div {
    padding: 15px 13px;
  }
}

/* line 1, G:/admin_project/8 admin/management_html/scss/_crm.scss */
.user_crm_wrapper {
  background: transparent;
}

/* line 6, G:/admin_project/8 admin/management_html/scss/_crm.scss */
.user_crm_wrapper .single_crm {
  background: #fff;
  border-radius: 15px;
  margin-bottom: 15px;
  padding: 5px;
}

/* line 11, G:/admin_project/8 admin/management_html/scss/_crm.scss */
/*.user_crm_wrapper .single_crm .crm_head {
  background: #FF7EA5;
  padding: 8px 20px;
  border-radius: 13px;
}*/

/* line 15, G:/admin_project/8 admin/management_html/scss/_crm.scss */
.user_crm_wrapper .single_crm .crm_head i {
  color: #fff;
}

/* line 19, G:/admin_project/8 admin/management_html/scss/_crm.scss */
.user_crm_wrapper .single_crm .crm_body {
  padding: 15px 23px;
}

/* line 21, G:/admin_project/8 admin/management_html/scss/_crm.scss */
.user_crm_wrapper .single_crm .crm_body h4 {
  font-size: 22px;
  font-weight: 700;
  color: #424242;
}

/* line 26, G:/admin_project/8 admin/management_html/scss/_crm.scss */
.user_crm_wrapper .single_crm .crm_body p {
  font-size: 14px;
  color: #9b9aba;
  font-weight: 400;
}

/* line 36, G:/admin_project/8 admin/management_html/scss/_crm.scss */
.user_crm_wrapper .crm_reports_bnner {
  background-image: url(../img/crm/bg.svg);
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  border-radius: 15px;
  padding: 40px 30px;
}

/* line 43, G:/admin_project/8 admin/management_html/scss/_crm.scss */
.user_crm_wrapper .crm_reports_bnner h4 {
  font-size: 16px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 0;
}

/* line 49, G:/admin_project/8 admin/management_html/scss/_crm.scss */
.user_crm_wrapper .crm_reports_bnner p {
  color: #c8b4de;
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  margin: 10px 0;
}

/* line 56, G:/admin_project/8 admin/management_html/scss/_crm.scss */
.user_crm_wrapper .crm_reports_bnner a {
  color: #efc343;
  font-size: 10px;
  font-weight: 300;
}

/* line 64, G:/admin_project/8 admin/management_html/scss/_crm.scss */
.crm_bg_1 {
  background: #20deff !important;
}

/* line 67, G:/admin_project/8 admin/management_html/scss/_crm.scss */
.crm_bg_2 {
  background: #c388f6 !important;
}

/* line 70, G:/admin_project/8 admin/management_html/scss/_crm.scss */
.crm_bg_3 {
  background: #f5f5ff !important;
}

/* line 72, G:/admin_project/8 admin/management_html/scss/_crm.scss */
.crm_bg_3 i {
  color: #58377e !important;
}

/* line 76, G:/admin_project/8 admin/management_html/scss/_crm.scss */
.max-width-220 {
  max-width: 220px;
}

/*# sourceMappingURL=style.css.map */
.f-r {
  float: right;
}

.single_crm:hover {
  background: #ff5c00;
  color: #fff;
  cursor: pointer;
}

.user_crm_wrapper .single_crm:hover .crm_body p {
  color: #fff;
}

.user_crm_wrapper .single_crm:hover .crm_body h4 {
  color: #fff;
}

.df {
  display: flex !important;
}

.status_btn_active_no {
  display: inline-block;
  padding: 2px 11px;
  font-size: 11px;
  font-weight: 700;
  color: #fff !important;
  background: #ff0000;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  border-radius: 30px;
  text-transform: capitalize;
  white-space: nowrap;
  min-width: 25px;
  text-align: center;
  margin-right: 10px;
}

.status_btn_active_yes {
  display: inline-block;
  padding: 2px 11px;
  font-size: 11px;
  font-weight: 700;
  color: #fff !important;
  background: #5bc870;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  border-radius: 30px;
  text-transform: capitalize;
  white-space: nowrap;
  min-width: 25px;
  text-align: center;
  margin-right: 10px;
}

.status_btn {
  display: inline-block;
  padding: 2px 11px;
  font-size: 11px;
  font-weight: 700;
  color: #3f4042 !important;
  background: #fff;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  border-radius: 30px;
  text-transform: capitalize;
  white-space: nowrap;
  min-width: 25px;
  text-align: center;
  margin-right: 10px;
  border: 1px solid #b4b4b4;
}

.status_btn_2_active {
  color: #fff;
  border: 1px solid #ff7700;
  border-radius: 45px;
  padding: 2px 6px 4px 6px;
  background: #ff7700;
  margin-right: 6px;
}

.status_btn_2_active img {
  width: 13px;
}

.status_btn_2_inactive img {
  width: 13px;
}

.status_btn_2_inactive {
  color: #3f4042;
  border: 1px solid #3f4042;
  border-radius: 45px;
  padding: 2px 6px 4px 6px;
  margin-right: 6px;
  background: #fff;
}

.status_btn_serach img {
  width: 28px;
}

.status_btn_serach_cancle img {
  width: 28px;
}

.status_btn_serach_cancle {
  color: #fff;
  border: 1px solid #ff1c1c;
  border-radius: 14px;
  padding: 4px 8px;
  margin-right: 6px;
  background: #ff1c1c;
}

.btn_submit_cust {
  background: #ee793c !important;
}

.status_btn_export {
  display: inline-block;
  padding: 2px 11px;
  font-size: 11px;
  font-weight: 700;
  color: #000 !important;
  background: #ffff;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  border-radius: 30px;
  text-transform: capitalize;
  white-space: nowrap;
  min-width: 25px;
  text-align: center;
  /*margin-right: 5px;*/
  margin-left: 10px;
}

.user_name {
  line-height: 24px;
  font-size: 15px;
  margin-bottom: 0px;
  color: #2261aa;
  font-family: "Mulish", sans-serif;
  font-weight: 600;
}

.total_item {
  float: right;
  color: #ccc;
}

.floating {
  margin-bottom: 4px;
  background-color: var(--field__background);
  transition: background-color 0.2s ease;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.floating label {
  font-weight: 600;
  margin-bottom: 10px;
}

.floating:hover,
.floating:focus-within {
  background-color: var(--field__background--hover);
}

.floating__input {
  height: 46px;
  font-size: 16px;
  /* border-bottom: 0.1rem solid var(--input__border); */
  transition: border-color 0.2s ease;
  caret-color: var(--color__accent);
}

.floating:hover .floating__input {
  border-color: var(--input__border--hover);
}

.floating__input::placeholder {
  color: rgba(0, 0, 0, 0);
}

.floating__label {
  display: block;
  position: relative;
  max-height: 0;
  pointer-events: none;
}

.floating__label::before {
  color: var(--label__color);
  content: attr(data-content);
  display: inline-block;
  filter: blur(0);
  color: #ccc;
  backface-visibility: hidden;
  transform-origin: left top;
  transition: transform 0.2s ease;
  left: 1rem;
  font-weight: 100;
  position: relative;
}

.floating__label::after {
  bottom: 1rem;
  content: "";
  height: 0.1rem;
  position: absolute;
  transition: transform 180ms cubic-bezier(0.4, 0, 0.2, 1), opacity 180ms cubic-bezier(0.4, 0, 0.2, 1), background-color 0.3s ease;
  opacity: 0;
  left: 0;
  top: 100%;
  margin-top: -0.1rem;
  transform: scale3d(0, 1, 1);
  width: 100%;
  background-color: var(--color__accent);
}

.floating__input:focus+.floating__label::after {
  transform: scale3d(1, 1, 1);
  opacity: 1;
}

.floating__input:placeholder-shown+.floating__label::before {
  transform: translate3d(0, -2.5rem, 0) scale3d(1, 1, 1);
}

.floating__label::before,
.floating__input:focus+.floating__label::before {
  transform: translate3d(0, -47.6px, 0) scale3d(0.82, 0.82, 1);
  font-size: 14px;
}

.floating__input:focus+.floating__label::before {
  color: var(--color__accent);
}

.label_text {
  color: #3f4042;
  font-size: 12px;
}

.active_btn {
  background: #5bc870;
  padding: 4px 18px;
  color: #fff;
  border-radius: 12px;
}

.dd_text {
  width: 100%;
  font-size: 12px;
  padding: 16px;
  color: #707070;
}

.QA_section {
  padding: 0px 1px;
}

.verification_switch {
  font-size: 14px;
  margin-left: 19px;
  width: 70%;
  margin-top: 12px;
}

.verification_custom_control {
  float: left;
  width: 14%;
}

.card_box_main .card_box .box_body {
  padding: 30px 22px;
}

.card_box_main .card_box {
  background: transparent linear-gradient(91deg, #145aa6 0%, #0089c2 100%) 0% 0% no-repeat padding-box !important;
}

.profile-pic {
  max-width: 200px;
  max-height: 200px;
  display: block;
}

.file-upload {
  display: none;
}

.circle {
  border-radius: 1000px !important;
  overflow: hidden;
  width: 128px;
  height: 128px;
  border: 8px solid rgb(231 231 231 / 70%);
  position: relative;
  top: 11px;
}

.p-image {
  position: absolute;
  top: 113px;
  color: #666666;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  left: 107px;
}

.p-image:hover {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.upload-button {
  font-size: 1.2em;
}

.upload-button:hover {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  color: #999;
}

/* .input-group-addon{
  position: relative;
    left: -25px;
    top: 8px;
} */
.mm-active {
  background: transparent linear-gradient(270deg, #1658a5 0%, #0089c2 100%) 0% 0% no-repeat padding-box !important;
  box-shadow: 0px 0px 10px #0000000d !important;
  border-radius: 10px !important;
}

.mm-active span {
  color: #fff !important;
}

#adminmenumain #adminmenu>li ul li a {
  color: #fff !important;
  background: unset;
}

#adminmenumain #adminmenu>li a:hover {
  color: #fff !important;
}

.menu_label {
  margin: 12px 15px -14px 38px !important;
  color: #0186c0;
}

#adminmenumain #adminmenu>li a:hover .darke {
  display: none;
}

#adminmenumain #adminmenu>li a .darke {
  display: block;
}

#adminmenumain #adminmenu>li a:hover .light {
  display: block !important;
}

#adminmenumain #adminmenu>li a .light {
  display: none;
}

#adminmenumain #adminmenu>li a.active .light {
  display: block;
}

#adminmenumain #adminmenu>li a.active .darke {
  display: none;
}

.wp-menu-name label {
  color: #0186c0;
  font-weight: 500;
  padding: 0px 0px 0px 9px;
}

.m-b-12 {
  margin-bottom: 13px;
}

.adv_main {
  height: 200px;
  width: 300px;
  background: #fff;
  position: absolute !important;
  top: 0px;
  left: 0px;
  width: 500px !important;
  box-shadow: 5px 5px 5px #00000040 !important;
}

.search_tip {
  margin-top: 5px !important;
  padding: 7px 22px !important;
  /*border-top: 1px solid #ccc;*/
}

.search_tip span {
  font-size: 10px;
}

.adv_btn {
  border-radius: 10px !important;
  color: #ffff !important;
  font-size: 12px !important;
  padding: 2px 11px !important;
  background: transparent linear-gradient(270deg, #1658a5 0%, #0089c2 100%) 0% 0% no-repeat padding-box !important;
  box-shadow: 0px 0px 10px #0000000d !important;
  display: inline-block !important;
}

#adv_filter {
  color: #86898e;
  font-size: 11px;
  height: 36px;
  width: 93%;
  padding-left: 12px;
  border: 0;
  padding-right: 6px;
  background: #ffffff;
  border-radius: 6px;
  border: 0;
  font-weight: 400;
  padding: 12px;
  left: 30px;
  position: relative;
}

#search_adv_block .search_field img {
  width: 30px;
  padding: 5px;
  position: absolute;
  top: 4px;
  left: 5px;
}

.serach_field-area .search_inner input {
  padding: 3px 12px 3px 39px !important;
}

.display_block {
  display: block !important;
}

.display_none {
  display: none !important;
}

.filter_cal {
  font-size: 11px;
  padding: 8px;
  border-bottom: 1px solid !important;
  border: unset;
  border-radius: unset;
}

.select2-container {
  width: 100% !important;
}

.btn_cancle {
  border: 1px solid #606060 !important;
  color: #ffffff !important;
  padding: 1px 15px !important;
  border-radius: 10px !important;
  box-shadow: 0px 0px 5px #0000001a !important;
  font-weight: 500 !important;
  background: transparent linear-gradient(270deg, #1658a5 0%, #0089c2 100%) 0% 0% no-repeat padding-box !important;
  margin-right: 5px !important;
}

#btn_search_submit {
  border: 1px solid #ff5c00 !important;
  color: #fff !important;
  padding: 1px 15px !important;
  border-radius: 10px !important;
  box-shadow: 0px 0px 5px #0000001a !important;
  font-weight: 500 !important;
  background: #ff5c00 !important;
  width: auto !important;
  height: 32px !important;
}

.m-t-10 {
  margin-top: 10px;
}

.m-r-10 {
  margin-right: 10px;
}

/*manage invoice style*/

#table_2 tr {
  background: #fff;
}

.custom_searchbox {
  width: 100%;
  border-radius: 12px;
  padding: 12px 48px;
  margin: 48px 0px 12px 0px;
  border: 2px solid #ccc;
  background: url(search.svg);
  background-repeat: no-repeat;
  background-position: 12px;
}

.batch_act_btn {
  border: 2px solid;
  padding: 5px 21px;
  margin-left: 34px;
  border-radius: 7px;
}

.custom_searchbox:after {}

input:focus {
  outline: unset;
}

#table .table>thead>tr>th {
  font-weight: 100;
}

.head_text h3 {
  font-size: 19px;
  padding: 0px;
  margin: 8px;
}

.head_text {
  background: linear-gradient(90deg, #1758a8 0%, #0887be 100%);
  padding: 4px 2px;
  color: #fff;
}

.border-radious-ds {
  box-shadow: 19px 5px 13px 2px #337ab79c;
}

.bg-blue-radi {
  background: linear-gradient(90deg, #1758a8 0%, #0887be 100%);
  padding: 16px 2px;
  color: #fff;
  border-top-right-radius: 12px;
  border-top-left-radius: 12px;
}

.transprant_btn:hover {
  /* color: #fff !important;
      background: #ef793c !important;
      border: 2px solid #ef793c;*/
}

.transprant_btn {
  background: #fff !important;
  color: #ef793c !important;
  border: 2px solid #ef793c;
  width: 100%;
}

#copy_row:hover {
  text-decoration: unset;
}

.btn-default.focus,
.btn-default:focus {
  border-color: unset !important;
  color: #fff;
  outline: unset;
}

.custom_dd {
  color: #fff !important;
  background: #0000 !important;
  border: 1px solid #ffff;
  border-radius: 17px;
  padding: 3px 26px;
  float: right;
  margin-right: 19px;
}

#copy_row {
  border-radius: 5px;
  font-size: 13px;
  padding: 3px 13px;
  width: 100%;
  text-align: left;
  border: 2px solid #0089c2;
}

#adminmenumain .logo img {
  height: 51px;
  margin-left: 0px;
  width: 100%;
}

.form-group {
  margin-bottom: 6px;
}

.m-b-18 {
  margin-bottom: 18px;
}

a:hover {
  text-decoration: unset;
}

.top_fields label {
  width: 71%;
  margin-top: 8px;
  text-align: right;
  margin-right: 8px;
}

.m-t-38 {
  margin-top: 38px;
}

#table .table-striped>tbody>tr:nth-of-type(even) {
  background-color: #eaeaea;
}

#table .table-striped>tbody>tr:nth-of-type(odd) {
  background-color: #fbfbfb;
}

.custom_btn:hover {
  background: -webkit-gradient(linear, left top, right top, from(#ee0979), to(#ff6a00));
  background: linear-gradient(90deg, #ff6a00 0%, #f1661e 100%);
}

.custom_btn {
  background: -webkit-gradient(linear, left top, right top, from(#ee0979), to(#ff6a00));
  background: linear-gradient(90deg, #ef793c 0%, #ff6a00 100%);
  color: #fff;
  padding: 8px 19px;
  font-family: Montserrat-Regular;
  width: 100%;
  border: unset;
  border-radius: 10px;
}

/* .input-group-addon{
  position: relative;
  left: -37px;
  z-index: 999;
  background: unset;
  border: unset;
  top: 3px;
} */
.datepicker.dropdown-menu {
  left: 1105.6px !important;
}

#table thead {
  background: linear-gradient(90deg, #1758a8 0%, #0887be 100%);
  color: #fff;
}

#table .table-bordered>tbody>tr>td,
#table .table-bordered>tbody>tr>th,
#table .table-bordered>tfoot>tr>td,
#table .table-bordered>tfoot>tr>th,
#table .table-bordered>thead>tr>td,
#table .table-bordered>thead>tr>th {
  border: 2px solid #fff;
}

#table .table>tbody>tr>td,
#table .table>tbody>tr>th,
#table .table>tfoot>tr>td,
#table .table>tfoot>tr>th,
#table .table>thead>tr>td,
#table .table>thead>tr>th {
  padding: 10px 18px;
  line-height: 2.428571;
}

.form-control {
  border: 1px solid #7e8993 !important;
}

.custom_field {
  width: 56px;
  margin-left: 6px;
  padding: 5px;
}

.mar-right {
  margin-right: 12px;
}

.mar-right-p {
  margin-right: 14px;
}

.custom_btn:hover {
  color: #fff !important;
}

.invoice_text {
  color: #0089c2;
  font-size: 43px;
  font-weight: 600;
  float: right;
  margin-right: -9px;
}

.f-r {
  float: right;
}

.txt-r {
  text-align: right;
}

.f-r {
  float: right;
}

.padding-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.padding-left-0 {
  padding-left: 0px;
}

.p-r-12 {
  padding-right: 19px;
  width: 78px;
}

.i_icon {
  color: #b7b7b7;
  padding: 10px 5px;
}

.m-r-25 {
  margin-right: 25px;
}

.my_bus_info {
  font-weight: 600;
  position: relative;
  top: -22px;
}

.login p.footer-style {
  /*
   Commeted for Occams 360 connect changes
   width: auto !important;
    left: 167px;
    text-align: left !important;*/
}

.woocommerce-reports-wide .widefat td,
.woocommerce-reports-wrap .widefat td {
  vertical-align: middle !important;
}

#echeck_report_table_wrap .widefat td p {
  margin: 0px 0 0 !important;
}

/*media query*/
@media only screen and (max-width: 600px) {
  .amount_box p {
    font-size: 14px !important;
    font-weight: 700 !important;
  }

  .amount_box {
    text-align: center !important;
    border-radius: 10px !important;
    /*border: 2.5px solid #701313 !important;*/
    padding: 8px 1px 0px 2px !important;
    margin: -23px 0px 15px -4px !important;
    z-index: 99999 !important;
    font-size: 10px !important;
  }

  .m-r-25 {
    margin-right: unset;
  }

  .i_icon {
    color: #b7b7b7;
    padding: 10px 5px;
    float: right;
    position: relative;
    right: -23px;
    top: -32px;
  }

  .m0b-m {
    margin-bottom: -27px;
  }

  .mar-right {
    margin-right: unset;
  }

  .my_bus_info {
    position: relative;
    top: 0px;
  }

  .logo {
    /* background: #ccc; */
    margin-top: -5%;
    margin-bottom: 16px;
  }

  .custom_btn {
    color: #fff;
    padding: 5px 14px;
    width: 100%;
  }

  #table .table>tbody>tr>td,
  #table .table>tbody>tr>th,
  #table .table>tfoot>tr>td,
  #table .table>tfoot>tr>th,
  #table .table>thead>tr>td,
  #table .table>thead>tr>th {
    padding: 5px 10px;
  }

  div#adminmenuwrap .logo img {
    height: 34px;
    margin-left: 0px;
  }

  .invoice_text {
    color: #0089c2;
    font-size: 33px;
    font-weight: 600;
    float: right;
    margin-right: 18px;
  }

  .f-r {
    float: unset;
  }

  .mar-right-p {
    margin-right: 2px;
  }

  .top_fields label {
    width: unset;
    margin-top: 8px;
    text-align: unset;
    margin-right: 8px;
  }

  .d-flex {
    /*display: unset;*/
  }

  .head_text {
    padding: 4px 0px;
    color: #fff;
  }

  .f-r {
    float: unset;
  }

  .m-m-s {
    margin: 1px 9px;
  }

  .padding-0 {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }

  .padding-left-0 {
    padding-left: 15px;
  }
}

.amount_box {
  text-align: center;
  width: 162px;
  border-radius: 10px;
  border: 2.5px solid #ccc;
  padding: 8px 1px 0px 2px;
  margin: -15px 0px 15px 94px;
}

.amount_box p {
  font-size: 20px;
  font-weight: 700;
}

.screen-reader-shortcut,
#adminmenuback {
  display: none;
}

#adminmenu,
#adminmenu .wp-submenu,
#adminmenuback,
#adminmenuwrap {
  background: #ffffff;
  box-shadow: 0 12px 30px rgb(80 143 244 / 10%);
  width: auto;
}

#adminmenu li.menu-top:hover,
#adminmenu li.opensub>a.menu-top,
#adminmenu li>a.menu-top:focus {
  position: relative;
  background-color: transparent;
  color: #ffffff;
}

#adminmenu li.menu-top {
  border: none;
  min-height: 15px;
  position: relative;
}

#adminmenu div.wp-menu-name {
  padding: 0px !important;
  margin-left: -10px;
}

#adminmenu li.wp-has-submenu.wp-not-current-submenu:hover:after,
#adminmenu li.wp-has-submenu.wp-not-current-submenu:focus-within:after {
  display: none;
}

#adminmenu div.wp-menu-image:before {
  color: #144339;
}

#adminmenu li:hover div.wp-menu-image:before,
#adminmenu li a:focus div.wp-menu-image:before,
#adminmenu li.opensub div.wp-menu-image:before {
  color: #ffffff;
}

#adminmenu li.wp-menu-separator {
  display: none;
}

.sticky-menu #adminmenuwrap {
  position: relative;
}

#collapse-menu {
  display: none;
  visibility: hidden;
}

ul#adminmenu a.wp-has-current-submenu:after,
ul#adminmenu>li.current>a.current:after {
  margin-top: 0px;
  border: none !important;
}

#adminmenu .current div.wp-menu-image:before,
#adminmenu .wp-has-current-submenu div.wp-menu-image:before,
#adminmenu a.current:hover div.wp-menu-image:before,
#adminmenu a.wp-has-current-submenu:hover div.wp-menu-image:before,
#adminmenu li.wp-has-current-submenu a:focus div.wp-menu-image:before,
#adminmenu li.wp-has-current-submenu.opensub div.wp-menu-image:before,
#adminmenu li.wp-has-current-submenu:hover div.wp-menu-image:before {
  color: #144339;
}

#adminmenu .current div.wp-menu-image:before {
  color: #fff !important;
}

#adminmenumain #adminmenu>li a:hover div.wp-menu-image:before {
  color: #ffffff;
}

#adminmenumain.mini_sidebar #adminmenu a {
  width: 30%;
}

#adminmenumain.mini_sidebar #adminmenu li {
  width: 330px !important;
}

/****************************************
*  Wocommerce Plugin Style Overrides
****************************************/
.woocommerce-layout__header {
  position: relative !important;
  width: 96% !important;
  top: 15px !important;
  left: 0 !important;
  margin: 0 auto !important;
}

#titlediv #title {
  height: 2em;
}

.woocommerce-page .wp-has-current-submenu:after {
  display: none !important;
}

div#wpbody {
  margin-top: 0px !important;
}

.table_tab a {
  color: #fff;
}

/****************************************
*  Subscription Report Plugin Style Overrides
****************************************/
#subscription_report_table .tablenav.top {
  display: none;
}

#subscription_report_table .alternate,
#subscription_report_table .striped>tbody> :nth-child(odd),
#subscription_report_table ul.striped> :nth-child(odd) {
  background-color: #ffffff;
}

#subscription_report_table .widefat thead td,
#subscription_report_table .widefat thead th {
  padding: 5px 8px !important;
  font-size: 12px;
  line-height: 1 !important;
  font-weight: 600;
  color: #474d58;
  /* white-space: nowrap; */
  text-transform: capitalize;
  font-family: "Mulish", sans-serif;
  border-bottom: 1px solid #e5ecff;
  border-top: 1px solid #e5ecff;
}

#subscription_report_table .widefat thead td,
#subscription_report_table .widefat thead th:last-child {
  border-right: 1px solid #e5ecff;
}

#subscription_report_table .widefat thead td,
#subscription_report_table .widefat thead th:first-child {
  border-left: 1px solid #e5ecff;
}

th#order_title a {
  position: relative;
  padding-left: 0;
}

.subscription_report_tbl_inner {
  padding: 18px 30px 25px 30px;
}

#subscription_report_table .sorting-indicator {
  position: absolute;
  right: -3px;
  top: 10px;
  margin-top: 0px;
  margin-left: 0px;
  display: none;
}

#subscription_report_table table.wp-list-table.widefat.fixed.striped.table-view-list.subscriptions {
  border: 0px;
}

#subscription_report_table .widefat td,
#subscription_report_table .widefat th {
  color: #888;
  font-size: 12px;
}

#subscription_report_table .widefat th {
  padding: 8px 2px !important;
}

#subscription_report_table th.sortable a,
#subscription_report_table th.sorted a {
  padding: 5px 0px;
}

span.table_tab a {
  padding: 0 !important;
  background: transparent !important;
  color: #fff !important;
}

.search_form_wrapper {
  margin: 10px;
  display: none;
}

#subscription_report_table {
  margin-top: 10px;
}

.search_form_wrapper p {
  margin-bottom: 5px;
}

input#search_amount {
  margin-right: 0px;
}

select#search_merchant {
  margin-right: 0;
  width: 45%;
  border-radius: 6px;
  margin-top: 0;
  padding: 1px 5px;
}

input#inputDate {
  border: 1px solid !important;
  width: 83% !important;
  padding: 2px 10px !important;
  padding: 2px 5px;
  margin-top: 5px;
}

.echeck_report input#datestart,
.echeck_report input#dateend {
  border: 1px solid !important;
  width: 83% !important;
  padding: 2px 10px !important;
  margin-top: 5px;
}

/************** Subscription Add Form  ****************/
div#susbc_api_form {
  background: #ffffff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}

#echeck_subscription_api_btn {
  padding: 10px;
  color: #fff;
  font-weight: 600;
  border: 1px solid rgb(59 118 239 / 30%);
  box-shadow: 0px 5px 10px rgb(59 118 239 / 30%);
  transition: all 0.3s ease-in-out;
}

#echeck_subscription_api_btn:active {
  background: linear-gradient(90deg, #ef793c 0%, #ff6a00 100%);
}

#echeck_subscription_api_btn:hover {
  /* background: #ffff;
    color: #000; */
  box-shadow: 0px 5px 10px rgb(59 118 239 / 30%);
  transition: all 0.3s ease-in-out;
}

#add_subs_butom #errorMsg {
  text-align: center;
}

button.button.save_order.button-primary {
  padding-bottom: 2px !important;
}

#recurring_notes .note_content p {
  color: #000000;
}

table.wp-list-table.widefat.fixed.striped.subscriptions {
  border: 0px;
}

.go-back-btn {
  margin-bottom: 10px;
}

#back-top a i {
  line-height: 40px;
}

#update-nag,
.update-nag {
  margin-bottom: 10px;
}

.notification_row {
  background: transparent linear-gradient(270deg, #1658a5 0%, #0089c2 100%) 0% 0% no-repeat padding-box !important;
  padding: 10px;
  color: #fff;
  width: 75%;
  border-radius: 10px;
  font-weight: bold;
  margin: 10px;
}

.notification_popup_row {
  margin-left: 5px;
  border-bottom: 1px solid;
}

#userheader {
  display: none;
}

/***** Echeck Report Page ******/
nav#wc-echeck-report-tabs2 {
  display: none;
}

#echeck_report_table_wrap .tablenav.top {
  display: none;
}

#poststuff .white_card {
  margin-bottom: 0px !important;
}

span.table_tab a:hover {
  text-decoration: none;
}

/*td.order_status.column-order_status.has-row-actions.column-primary {
    text-align: center;
}*/
a.user_action_echeckbtn {
  border-radius: 30px !important;
  margin-right: 2px !important;
  text-align: center;
  display: inline-block !important;
  font-size: 11px !important;
  font-weight: 700;
}

th#checkno {
  padding: 0;
}

th#amount {
  padding: 0px;
}

th#amount a {
  padding: 0px;
}

.tablenav .tablenav-pages .button,
.tablenav .tablenav-pages .tablenav-pages-navspan {
  color: #fff !important;
  border: 1px solid #ff3b00;
  background-color: #ff3b00 !important;
  box-shadow: 0px 5px 10px rgb(136 79 251 / 4%) !important;
}

th.manage-column.column-checkno.sortable.asc {
  padding: 0;
}

th.manage-column.column-amount.sortable.asc a {
  padding: 0;
}

th.manage-column.column-amount.sortable.asc {
  padding: 0;
}

/**** Eq Customre Profile ******/
#eq_customer_profile_listing .alert.alert-success.alert_status {
  background: #5bc870;
  padding: 4px 18px;
  color: #fff;
  border-radius: 12px;
}

label.Amount.echeck_searching {
  margin-top: 5px;
}

.contact_fields .row {
  margin-bottom: 15px;
}

.contact_fields #emailsubmit {
  font-size: 1.6rem;
}

.QA_section.contact_fields label {
  font-weight: normal;
}

ul li a span.update-plugins {
  width: auto;
  margin-left: 50px !important;
}

.wp-submenu {
  margin-top: 0px !important;
}

.sidebar_close_icon {
  display: none;
}

/*#toplevel_page_woocommerce .wp-first-item{display: none;}*/
.wp-core-ui .button,
.wp-core-ui .button-secondary {
  color: #ffffff;
  background: #057fbc;
}

#postmetatabs .ui-widget-header {
  background: transparent linear-gradient(89deg, #1658a5 0%, #0089c2 100%) 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 10px #0000000d;
  padding-bottom: 6px !important;
}

div#wpadminbar li#wp-admin-bar-wp-logo {
  display: none;
}

div#wpadminbar li#wp-admin-bar-updates {
  display: none;
}

form#woocommerce_amount p.search-box2 {
  display: none;
}

form#woocommerce_amount p.search-box {
  display: none;
}

p#searchbox_echeckreport {
  display: none;
}

.verification_switch_applicable,
#instant_bank_verification_mandatory,
#instant_bank_verification_optional,
#instant_bank_verification_none,
#autoverif,
#bankverif,
#smsverif,
#emailverif,
#ach,
#vendor1,
#vendor2,
#vendor3 {
  margin: -3px 0 0 !important;
}

.pl_chk_ach {
  margin-left: -10px;
}

#generate_echeck_form {
  margin-left: 10px;
}

#subscription_report_table td.order_title.column-order_title a {
  font-size: 12px;
}

#subscription_report_table td.order_title.column-order_title a strong {
  color: #337ab7;
  font-size: 12px;
}

#checknos {
  font-weight: 700;
}

th#order_title a {
  font-size: 12px;
  font-weight: 600;
  color: #474d58;
}

.box_header h4 {
  margin-top: 0px;
}

td.user_actions.column-user_actions p {
  display: inline-block !important;
}

td.user_actions.column-user_actions p a {
  line-height: 1.53846;
  min-height: 24px;
  height: 24px !important;
  margin-bottom: 3px;
}

a.user_action_echeckbtn.resumebtn.button.tips.resume {
  height: 24px !important;
}

a.user_action_echeckbtn.button.disabled {
  height: 24px !important;
}

#order_data .order_data_column .form-field strong {
  font-weight: 700;
}

#order_data p {
  line-height: 1.5;
  margin-bottom: 10px;
}

@media only screen and (max-width: 1479px) {
  a.user_action_echeckbtn {
    margin-right: 2px !important;
  }

  .stopbtn {
    width: 50px !important;
  }

  .refundbtn,
  .refund_btn {
    width: 59px !important;
  }
}

@media screen and (max-width: 782px) {
  li#wp-admin-bar-togglesidebaricon {
    display: block !important;
  }

  li#wp-admin-bar-menu-toggle {
    display: none !important;
  }

  #wpadminbar .quicklinks .ab-top-secondary>li {
    display: block !important;
  }

  .auto-fold #adminmenuwrap {
    display: block !important;
  }

  #adminmenuwrap {
    display: block !important;
  }

  #wpadminbar #wp-admin-bar-my-account>a {
    overflow: visible !important;
  }

  #wp-admin-bar-my-account a span {
    display: inline;
  }

  #adminmenu {
    width: auto !important;
  }

  .auto-fold #adminmenu {
    position: relative !important;
    width: auto !important;
  }

  #wpadminbar #wp-admin-bar-my-account>a {
    text-indent: 0% !important;
    width: auto !important;
  }

  #wpbody {
    padding: 0px 0px !important;
  }

  .auto-fold #wpcontent {
    padding-left: 0px;
  }

  #wpwrap #adminmenumain #adminmenu>li>a {
    padding: 10px 10px 10px 15px;
  }

  #adminmenumain #adminmenu>li a.wp-has-submenu::after {
    right: 25px;
  }

  .auto-fold #adminmenu li.menu-top {
    width: auto !important;
  }

  .auto-fold #adminmenu a.menu-top {
    height: auto;
  }

  ul#wp-admin-bar-top-secondary {
    margin-top: -16px;
  }

  #wpbody-content {
    margin-top: 0 !important;
  }

  .sidebar_close_icon {
    display: block;
  }
}

@media screen and (max-width: 600px) {
  #wpbody {
    padding-top: 19px !important;
  }

  #wpbody-content {
    margin-top: 0px !important;
  }
}

@media screen and (max-width: 380px) {
  #wpadminbar {
    padding: 20px 15px !important;
  }

  li#wp-admin-bar-adminbarnotifcicon {
    width: 40px;
  }

  #wpadminbar .quicklinks a {
    padding: 0px 0px !important;
  }

  #wpadminbar {
    top: 5px;
  }
}

/**************code added on 24-march-2022 for modal fix on return and charges report****************/

.modal-header {
  display: block !important;
}

.modal-header .close {
  padding: 0px !important;
  margin: 0px !important;
}

.modal-title {
  float: left;
}

#wp-admin-bar-esign {
  display: none !important;
}

.send_doc {
  background: #ff5c00 !important;
  background: #ff5c00 0% 0% no-repeat padding-box !important;
  box-shadow: 0px 0px 5px #0000001a;
  border-radius: 10px !important;
  color: #fff;
  padding: 6px 20px !important;
  font-weight: 600 !important;
  display: inline-block !important;
}

.send_doc:hover {
  color: #fff !important;
}

.calanly_link {
  color: #fff;
  padding: 6px 40px !important;
  font-weight: 600 !important;
  background: #1758a8 0% 0% no-repeat padding-box !important;
  box-shadow: 0px 0px 5px #0000001a;
  border-radius: 10px !important;
  margin-left: 15px !important;
  display: inline-block !important;
}

.calanly_link:hover {
  color: #fff !important;
}

.calanly_link span {
  margin-right: 10px !important;
}

.pro_icon i {
  color: #fff;
  font-size: 22px;
  padding: 0px 9px 0px 10px;
}

#toplevel_page_company_ownership_search {
  display: none;
}

#adminmenu .nav_title label {
  display: none;
}

.widefat tfoot tr td,
.widefat tfoot tr th,
.widefat thead tr td,
.widefat thead tr th {
  font-size: 10px !important;
  font-weight: bold !important;
  text-align: left !important;
}

.widefat tbody tr td {
  font-size: 10px !important;
  text-align: left !important;
}

.widefat tfoot tr td a,
.widefat tfoot tr th a,
.widefat thead tr td a,
.widefat thead tr th a {
  font-size: 10px !important;
  font-weight: bold !important;
  text-align: left !important;
}

.widefat tbody tr td select {
  font-size: 10px !important;
}

#audit_logs_table_length,
#activity_status_table_length,
#activity_campaign_table_length,
#activity_source_table_length {
  margin-bottom: 15px;
}

#audit_logs_table_filter,
#activity_status_table_filter,
#activity_campaign_table_filter,
#activity_source_table_filter {
  margin-bottom: 15px;
}

#audit_logs_table,
#activity_status_table,
#activity_campaign_table,
#activity_source_table {
  border: 1px solid #e3e3e3;
}

#audit_logs_table tr td,
#audit_logs_table tr th,
#activity_status_table tr td,
#activity_status_table tr th,
#activity_campaign_table tr td,
#activity_campaign_table tr th,
#activity_source_table tr td,
#activity_source_table tr th {
  border: 1px solid #e3e3e3;
  font-size: 10px;
  padding: 8px 10px;
}

#audit_logs_table .odd,
#activity_status_table .odd,
#activity_campaign_table .odd,
#activity_source_table .odd {
  background-color: #f6f7f7;
}

#audit_logs_table_info,
#activity_status_table_info,
#activity_campaign_table_info,
#activity_source_table_info {
  margin-top: 15px;
  margin-bottom: 25px;
}

#audit_logs_table_paginate,
#activity_status_table_paginate,
#activity_campaign_table_paginate,
#activity_source_table_paginate {
  margin-top: 15px;
  margin-bottom: 25px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover,
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active {
  background: transparent !important;
  border: 0 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  color: #fff !important;
  background: #ff5c00 !important;
  border: 0 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: #ff5c00 !important;
  border: 0 !important;
}

#wpfooter {
  position: inherit !important;
  background: #dce9f4;
}

#wpadminbar * {
  width: unset;
  position: relative;
  margin: 0px;
}

.send_doc span {
  margin-right: 10px !important;
}

#wpadminbar {
  padding: 15px 30px;
  position: relative;
  margin-bottom: 20px;
  background: transparent;
}

#wpadminbar .ab-top-menu>li.hover>.ab-item,
#wpadminbar.nojq .quicklinks .ab-top-menu>li>.ab-item:focus,
#wpadminbar:not(.mobile) .ab-top-menu>li:hover>.ab-item,
#wpadminbar:not(.mobile) .ab-top-menu>li>.ab-item:focus {
  background: #2c3338;
  color: #fafafa;
  background: transparent linear-gradient(270deg, #1658a5 0%, #0089c2 100%) 0% 0% no-repeat padding-box;
}

#wpadminbar .quicklinks .menupop ul.ab-sub-secondary,
#wpadminbar .quicklinks .menupop ul.ab-sub-secondary .ab-submenu {
  background: transparent linear-gradient(270deg, #1658a5 0%, #0089c2 100%) 0% 0% no-repeat padding-box;
}

#wpadminbar .menupop .ab-sub-wrapper {
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.5s !important;
  transition: 0.5s !important;
}

#wpadminbar li.hover>.ab-sub-wrapper,
#wpadminbar.nojs li:hover>.ab-sub-wrapper {
  display: block;
  opacity: 1;
  visibility: visible;
  -webkit-transition: 0.5s !important;
  transition: 0.5s !important;
  top: 30px;
}

.profile_info .profile_info_iner {
  position: absolute;
  right: 0;
  background-color: #fff !important;
  text-align: left;
  width: 215px !important;
  padding: 0;
  opacity: 0;
  visibility: hidden;
  top: 100px;
  -webkit-transition: 0.5s !important;
  transition: 0.5s !important;
  -webkit-border-radius: 15px !important;
  -moz-border-radius: 15px !important;
  border-radius: 15px !important;
}

.profile_info:hover .profile_info_iner {
  opacity: 1;
  visibility: visible;
  top: 41px;
  -webkit-transition: 0.5s !important;
  transition: 0.5s !important;
}

#wpadminbar li .ab-item.ab-empty-item {
  display: none !important;
}

ul#wp-admin-bar-user-actions li a {
  color: #0186c0 !important;
}

#wpadminbar #wp-admin-bar-user-info .avatar {
  display: none;
}

#wpadminbar #wp-admin-bar-my-account.with-avatar #wp-admin-bar-user-actions>li {
  margin-left: 16px;
}

#wpadminbar #wp-admin-bar-user-info {
  margin-bottom: 6px;
}

#wpadminbar #wp-admin-bar-user-info .display-name,
#wpadminbar #wp-admin-bar-user-info .username {
  display: block;
  font-size: 14px;
  font-weight: 400;
  text-transform: capitalize;
}

#wpbody-content {
  margin-top: 15px;
}

#wpadminbar #wp-admin-bar-my-account.with-avatar>.ab-empty-item img,
#wpadminbar #wp-admin-bar-my-account.with-avatar>a img {
  display: none;
}

#wpfooter {
  padding-left: 285px !important;
  margin-left: 0px;
}

html.wp-toolbar {
  padding-top: 0px;
}

.check_email {
  /* margin-left: 10px; */
  border: 1px solid #ff5f00;
  padding: 0px 5px;
  color: #ff5f00 !important;
  border-radius: 5px;
  font-size: 13px;
  /* margin-top: -10px; */
}

#user-creation-form .floating,
#userUpdationform .floating {
  margin-bottom: 20px;
}

.select_role select {
  min-height: 46px !important;
  max-width: 100%;
}

.custom_width#echeck_report_table_wrap th,
.custom_width#echeck_report_table_wrap td {
  font-size: 12px !important;
}

#um-search-submit {
  background: #ff5c00 0% 0% no-repeat padding-box !important;
  box-shadow: 0px 0px 5px #0000001a;
  border-radius: 5px !important;
  color: #fff;
  padding: 6px 20px !important;
  font-weight: 600 !important;
}

.search-box #user_email,
.search-box #search_data {
  height: 40px;
  width: 260px;
}

#waiter,
#spining,
#spinners {
  position: fixed;
  top: 50%;
  left: 50%;
  margin-top: -50px;
  margin-left: -50px;
}

#waiter img,
#spining img,
#spinners img {
  height: auto;
  max-height: 110px;
}

.username-not-available,
.email-not-available {
  color: #0eb30e;
}

.username-available,
.email-available {
  color: red;
}

.phone-main-div {
  width: 100%;
}

.phone-main-div .custom_country_code {
  width: 10%;
  float: left;
  height: 46px;
  border-right: none !important;
  border-bottom-right-radius: 0px;
  border-top-right-radius: 0px;
  padding: 0px 0px 0px 5px;
}

.phone-main-div .custom_phone_number {
  width: 90%;
  float: right;
  border-left: none !important;
  border-bottom-left-radius: 0px;
  border-top-left-radius: 0px;
  height: 46px;
}

.nav-pills>li.active>a {
  background-color: unset;
}

.nav.nav-pills {
  background-color: unset;
  margin-top: 22px;
}

.nav-link {
  padding: 7px 25px !important;
}

.nav>li>a:focus,
.nav>li>a:hover {
  text-decoration: none;
  background-color: unset !important;
}

.nav.nav-tabs .nav-item.show:focus,
.nav.nav-tabs .nav-item.show.active,
.nav.nav-tabs .nav-link:focus,
.nav.nav-tabs .nav-link.active {
  color: #1761fd;
  background-color: #fff;
  border-color: transparent transparent #1761fd;
}

.nav-pills .nav-link {
  color: #fff !important;
}

.nav-pills .nav-item.show .nav-link,
.nav-pills .nav-link.active {
  background: unset !important;
  color: #fff !important;
  border: 1px solid !important;
  padding: 3px 13px !important;
  border-radius: 50px !important;
}

.nav-link:focus,
.nav-link:hover,
.nav-item.active .nav-link {
  color: #fff !important;
  border: 1px solid;
  padding: 3px 13px;
  border-radius: 50px;
}

/* Overlay */
#umModal.overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1111;
  /* Set a high z-index value */
}

/* Modal Content */
#umModal .umContent {
  background: #fff;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  color: #333;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1222;
  /* Set a higher z-index value than the overlay */
}

#umModal .umContent p {
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: 600;
  color: #e65100;
}

#confirm-yes,
#confirm-no {
  border: none;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  margin: 0 10px;
  border-radius: 5px;
}

#confirm-yes {
  background-color: #4caf50;
  color: #fff;
}

#confirm-no {
  background-color: #f44336;
  color: #fff;
}

/* Optional: You can add animation for a smoother transition */
#umModal {
  transition: all 0.3s ease;
}

.checkbox-link-container {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.checkbox-link-container input[type="checkbox"] {
  margin-right: 10px;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  margin-bottom: 10px;
}

.checkbox-link-container label {
  color: #333;
  cursor: pointer;
  display: flex;
  align-items: center;
}

/**
 * All of the CSS for your admin-specific functionality should be
 * included in this file.
 */
.activeUser {
  color: #ff5c00;
  margin-bottom: 10px;
  font-weight: 500;
  font-size: 16px;
  margin-left: 15px;
}

.nxtBtn {
  padding: 5px 10px !important;
  color: #ff5f00 !important;
  font-size: 16px !important;
  font-weight: 600;
  background: #ffffff 0% 0% no-repeat padding-box !important;
  box-shadow: 0px 3px 6px #00000029;
  border: 2px solid #ff5f00;
  border-radius: 11px !important;
  margin-top: 30px !important;
  border-color: #ff5f00 !important;
}

.sendDoc {
  background: #ff5c00 0% 0% no-repeat padding-box !important;
  box-shadow: 0px 0px 5px #0000001a;
  border-radius: 10px !important;
  color: #fff;
  padding: 9px 20px !important;
  font-weight: 600 !important;
  display: inline-block !important;
  border: 0;
  margin-top: 15px;
}

.ved,
.wing,
.errer,
.succes {
  font-size: 18px;
  font-weight: 400;
  padding: 15px 10px;
}

.ved,
.succes {
  color: #0eb30e;
}

.wing,
.errer {
  color: red;
}

/* Popup Styles */
.user-popup {
  display: none;
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
}

.close-popup {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 20px;
  cursor: pointer;
  color: red;
  font-weight: 600;
}

/* Gaurav Css */
/* Start Custom Css Bharat Goyal 29/05/2023 */
/* Start Custom Css Bharat Goyal 29/05/2023 */
@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css");

#echeck_report_table_wrap th,
#echeck_report_table_wrap td {
  font-size: 12px;
  vertical-align: middle !important;
}

#echeck_report_table_wrap th {
  font-weight: bold;
  vertical-align: top !important;
}

#echeck_report_table_wrap td input {
  margin-top: 6px;
}

/* End Custom Css Bharat Goyal 29/05/2023 */

/*#echeck_report_table_wrap th,#echeck_report_table_wrap td{font-size: 12px;}
 #echeck_report_table_wrap th{font-weight: bold;}*/

#woocommerce_amount b {
  font-weight: normal;
}

.holdbtn {
  display: inline-block !important;
  padding: 3px 11px !important;
  font-size: 11px !important;
  font-weight: 700 !important;
  color: #fff !important;
  background: #ffc400 !important;
  -webkit-border-radius: 30px !important;
  -moz-border-radius: 30px !important;
  border-radius: 30px !important;
  text-transform: capitalize !important;
  white-space: nowrap !important;
  width: 60px !important;
  text-align: center !important;
  margin-right: 10px !important;
  border-color: unset !important;
  border-style: none !important;
}

.stopbtn {
  display: inline-block !important;
  padding: 3px 11px !important;
  font-size: 11px !important;
  font-weight: 700 !important;
  color: #fff !important;
  background: #ff0000 !important;
  -webkit-border-radius: 30px !important;
  -moz-border-radius: 30px !important;
  border-radius: 30px !important;
  text-transform: capitalize !important;
  white-space: nowrap !important;
  width: 62px !important;
  text-align: center !important;
  margin-right: 10px !important;
  border-color: unset !important;
  border-style: none !important;
}

.refundbtn,
.refund_btn {
  display: inline-block !important;
  padding: 5px 11px !important;
  font-size: 11px !important;
  font-weight: 700 !important;
  color: #fff !important;
  background: #b4b4b4 !important;
  -webkit-border-radius: 30px !important;
  -moz-border-radius: 30px !important;
  border-radius: 30px !important;
  text-transform: capitalize !important;
  white-space: nowrap !important;
  width: 65px !important;
  text-align: center !important;
  margin-right: 10px !important;
  border-color: unset !important;
  border-style: none !important;
}

.resumebtn {
  display: inline-block !important;
  padding: 2px 9px !important;
  font-size: 11px !important;
  font-weight: 700 !important;
  color: #fff !important;
  background: #016087 !important;
  -webkit-border-radius: 30px !important;
  -moz-border-radius: 30px !important;
  border-radius: 30px !important;
  text-transform: capitalize !important;
  white-space: nowrap !important;
  width: 60px !important;
  text-align: center !important;
  margin-right: 10px !important;
  border-color: unset !important;
  border-style: none !important;
}

a.user_action_echeckbtn.button.tips.passbtn {
  height: 24px !important;
}

a.button.tips.passbtn {
  border-radius: 30px !important;
  margin-right: 1px !important;
  text-align: center;
  display: inline-block !important;
  font-size: 11px !important;
  color: #fff;
  height: 24px !important;
  border: 0px !important;
  width: 65px !important;
}

a.user_action_echeckbtn.view-check-btn.button {
  width: 62px;
}

input.decline-status-model-btn.button {
  border-radius: 30px !important;
  margin-right: 2px !important;
  text-align: center;
  display: inline-block !important;
  font-size: 11px !important;
  width: 65px !important;
  padding: 0px 2px 1px !important;
  height: 27.5px !important;
  line-height: 1 !important;
  min-height: 20px !important;
  font-weight: bold;
}

input.reprocessbtns.button {
  border-radius: 30px !important;
  margin-right: 2px !important;
  text-align: center;
  display: inline-block !important;
  font-size: 11px !important;
  width: 65px !important;
  padding: 0px 2px 1px !important;
  height: 26px !important;
  line-height: 1 !important;
  min-height: 20px !important;
  font-weight: bold;
  margin-top: 3px;
}

input.reprocess-btn.button {
  border-radius: 30px !important;
  margin-right: 1px !important;
  text-align: center;
  display: inline-block !important;
  font-size: 11px !important;
  font-weight: 700;
}

input.process-btn.button {
  border-radius: 30px !important;
  margin-right: 1px !important;
  text-align: center;
  display: inline-block !important;
  font-size: 11px !important;
  font-weight: 700;
}

.vm1btn,
.passcbtn,
.failcbtn {
  padding: 2px 10px !important;
  color: #fff;
}

td.user_actions.column-user_actions p a {
  color: #fff;
  padding: 2px 10px !important;
}

.toplevel_page_equipay_subscriptions #merchant_name {
  width: 125px;
}

.toplevel_page_equipay_subscriptions #order_title {
  width: 86px;
}

.toplevel_page_equipay_subscriptions #customer {
  width: 110px;
}

.toplevel_page_equipay_subscriptions #next_payment_date {
  width: 115px;
}

.toplevel_page_equipay_subscriptions #last_payment_date {
  width: 100px;
}

.toplevel_page_equipay_subscriptions #start_date {
  width: 90px;
}

.toplevel_page_equipay_subscriptions #end_date {
  width: 100px;
}

.toplevel_page_equipay_subscriptions #recurring_total {
  width: 100px;
}

.toplevel_page_equipay_subscriptions #status {
  width: 75px;
}

.toplevel_page_equipay_subscriptions #orders {
  width: 62px;
}

.notify_content h5 {
  margin-top: -12px !important;
}

.add_funds {
  padding: 0px !important;
  margin-right: -29px !important;
}

.add_fund_main {
  margin-left: 19px !important;
  background: #fff !important;
  border-radius: 6px !important;
  height: 36px !important;
}

.icon_fund {
  padding: 5px 12px !important;
  background: transparent linear-gradient(92deg, #1658a5 0%, #1659a5 40%, #0089c2 100%) 0% 0% no-repeat padding-box !important;
  font-size: 18px !important;
  color: #fff !important;
  border-top-left-radius: 6px !important;
  border-bottom-left-radius: 6px !important;
  display: inline-block;
  line-height: 41px !important;
}

.icon_fund img {
  width: 16px !important;
  margin-bottom: 18px !important;
}

.add_fund_main label {
  text-align: center !important;
  padding: 0px 15px !important;
  font-size: 17px !important;
  line-height: 34px !important;
}

.btn_funds {
  font-size: 9px !important;
  background: #ff5c00 !important;
  height: 25px !important;
  margin: 5px 10px 25px 0px !important;
  padding: 0px 12px !important;
  color: #fff !important;
  border-radius: 7px !important;
}

#add_funds_input {
  border: none !important;
  margin-left: -15px;
  font-size: 17px;
  width: 70px;
  margin-top: 0px;
}

.header_iner .serach_field-area {
  margin-right: auto;
  margin-left: 40px;
  width: 268px;
}

.serach_field-area {
  width: 268px;
  position: relative;
  background: #ffffff;
  border-radius: 6px;
}

.serach_field-area .search_inner {
  width: 100%;
}

.serach_field-area .search_inner input::placeholder {
  font-size: 11px;
  font-weight: 400;
  color: #86898e;
  font-family: "Mulish", sans-serif;
}

.serach_field-area .search_inner input {
  color: #86898e;
  font-size: 11px;
  height: 36px;
  width: 100%;
  padding-left: 12px;
  border: 0;
  padding-right: 6px;
  background: #ffffff;
  border-radius: 6px;
  border: 0;
  font-weight: 400;
  padding: 12px;
}

.serach_field-area .search_inner button {
  position: absolute;
  top: 2px;
  right: 2px;
  height: 100%;
  background: transparent;
  border: 0;
  width: 32px;
  height: 32px;
  background: #f45b0f;
  line-height: 32px;
  border-radius: 4px;
}

.serach_field-area .search_inner button img {
  position: relative;
  top: -2px;
}

.echeck_not_popup {
  position: absolute !important;
  left: 205px;
  display: none;
}

.echeck_not_popup_box {
  z-index: 10;
  top: -28px;
  right: 70px;
  width: 425px !important;
  padding: 10px !important;
  border-radius: 10px !important;
}

input.reprocess-btn.button {
  line-height: 1.53846;
  min-height: 24px !important;
  margin-bottom: 3px !important;
}

.echeck_display .modal-header .close,
.order_stop-model .modal-header .close,
.order_refund-model .modal-header .close,
.order_resume-model .modal-header .close,
.order_stop-model .modal-header .close,
.order_hold-model .modal-header .close {
  padding-top: 0rem !important;
}

.echeck_display .modal-content {
  width: 880px !important;
}

#banking_details .fade:not(.show) {
  opacity: 1 !important;
}

#add_funds_input {
  line-height: 34px !important;
}

@media screen and (min-width: 481px) {
  .echeck_not_popup_box {
    position: absolute !important;
  }
}

@media screen and (max-width: 480px) {
  .echeck_not_popup {
    position: absolute !important;
    left: -340px !important;
    width: 305px !important;
    display: none;
  }

  .echeck_not_popup_box {
    z-index: 10;
    top: -28px;
    right: 70px;
    width: 100% !important;
    padding: 10px;
    border-radius: 10px;
  }

  .sidebar_icon {
    margin-top: 15px !important;
  }

  .ab-top-menu {
    width: 10%;
    display: inline-block;
  }

  #wp-admin-bar-addfundadminbar {
    margin-left: -5px !important;
    margin-top: 50px !important;
  }

  .ab-top-secondary.ab-top-menu {
    width: 10px !important;
  }

  #wp-admin-bar-my-account {
    margin-top: -20px !important;
    margin-right: 40px;
  }

  #wp-admin-bar-adminbarnotifcicon {
    margin-top: -15px !important;
  }

  #add_funds_input {
    min-height: auto !important;
  }

  ul#wp-admin-bar-top-secondary {
    width: 90% !important;
    margin-top: 12px;
  }
}

#wp-admin-bar-addfundadminbar {
  display: none;
}

.charges-and-payment-report_page_cm-micropayment-platform-transactions #wp-admin-bar-addfundadminbar {
  display: block !important;
}

.toplevel_page_cm_micropayments #wp-admin-bar-addfundadminbar {
  display: block !important;
}

.charges-and-payment-report_page_cm-micropayment-platform-wallet #wp-admin-bar-addfundadminbar {
  display: block !important;
}

@media screen and (min-width: 1201px) {
  #wp-admin-bar-searchboxadminbar {
    margin: 0px 20px 0px 40px !important;
  }

  .serach_field-area .search_inner input {
    width: 80% !important;
  }

  .echecks_page_wc-reports #order_status {
    width: 55px;
  }

  .echecks_page_wc-reports #checkno {
    width: 85px;
  }

  .echecks_page_wc-reports #checkdate {
    width: 80px;
  }

  .echecks_page_wc-reports #amount {
    width: 70px !important;
    padding: 2px !important;
  }

  .echecks_page_wc-reports #post_date {
    width: 80px;
  }

  .echecks_page_wc-reports #billing_address {
    width: 90px;
  }

  .echecks_page_wc-reports #accinfo {
    width: 14% !important;
  }

  .echecks_page_wc-reports #risk_type {
    width: 85px;
  }

  .echecks_page_wc-reports #agent_id {
    width: 14.2% !important;
  }

  .echecks_page_wc-reports #user_actions {
    width: 200px;
  }

  .echecks_page_wc-reports #checkno a {
    padding: 2px !important;
  }

  .echecks_page_wc-reports .checkno,
  .amount {
    padding-left: 2px !important;
  }

  .echecks_page_wc-reports #phone_number {
    width: 70px;
  }

  td.user_actions.column-user_actions {
    padding: 2px 2px !important;
  }

  #wpadminbar .header_notification_warp {
    margin: 0px !important;
  }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
  #wp-admin-bar-searchboxadminbar {
    display: block !important;
  }

  .sidebar_icon {
    margin-top: 15px !important;
  }

  #add_funds_input {
    min-height: auto !important;
  }

  .ab-top-secondary.ab-top-menu {
    margin-top: 0px !important;
  }

  .profile_info .ab-item {
    margin-top: -5px !important;
  }

  #wp-admin-bar-searchboxadminbar {
    margin: 0px !important;
  }

  .header_notification_warp {
    margin: 0px !important;
  }

  #show_adv_search {
    min-height: auto !important;
  }

  #wpadminbar .serach_field-area {
    margin-right: auto !important;
    width: 188px !important;
  }

  .toplevel_page_invoices p.search-box {
    width: auto !important;
    height: auto !important;
    margin-bottom: 0px !important;
  }

  .toplevel_page_invoices #search_id-search-input {
    width: auto !important;
    float: inherit !important;
    padding: 0px !important;
    min-height: 0px !important;
  }

  .toplevel_page_invoices #search-submit {
    margin-bottom: 0px !important;
    min-height: auto !important;
    line-height: 28px !important;
  }

  .invoice_date_filter .date_from,
  .invoice_date_filter .date_to {
    padding: 0px !important;
    min-height: auto !important;
  }

  .invoice_date_submit {
    line-height: 27px !important;
    min-height: auto !important;
  }
}

@media screen and (min-width: 992px) and (max-width: 1199px) {
  #wp-admin-bar-searchboxadminbar {
    margin-left: 35px !important;
  }

  .serach_field-area {
    width: 245px !important;
  }

  .header_notification_warp {
    margin: 0px !important;
  }

  #wpadminbar .serach_field-area {
    margin-right: 0px !important;
  }

  .serach_field-area .search_inner input {
    width: 72% !important;
  }
}

.toplevel_page_vt .floating__label::before,
.floating__input:focus+.floating__label::before {
  transform: translate3d(0, -2.5rem, 0) scale3d(1, 1, 1);
}

.floating__label::before,
.floating__input:focus+.floating__label::before {
  transform: translate3d(0, -47.6px, 0) scale3d(0.82, 0.82, 1);
  font-size: 14px;
}

/* Custom Css Bharat Goyal 29/05/2023 */

.delete_notes {
  background: red;
  border: 0;
  padding: 7px 12px;
  color: #fff;
  border-radius: 10px;
}

.status_btn_export {
  background: #ff5c00 0% 0% no-repeat padding-box !important;
  color: #fff !important;
  padding: 10px !important;
  border-radius: 10px !important;
  opacity: 1 !important;
  font-size: 15px;
  font-weight: 600;
  display: initial;
}

.status_btn_export:before {
  font-family: FontAwesome;
  display: inline-block;
  padding-right: 6px;
  vertical-align: middle;
  content: "\f1c3";
}

/*code added by vinay for debit note button icon */

.btn_add_debit_note {
  background: #ff5c00 0% 0% no-repeat padding-box !important;
  color: #fff !important;
  padding: 10px !important;
  border-radius: 10px !important;
  opacity: 1 !important;
  font-size: 15px;
  font-weight: 600;
  display: initial;
}

.btn_add_debit_note:before {
  font-family: FontAwesome;
  display: inline-block;
  padding-right: 6px;
  vertical-align: middle;
  content: "\f24a";
}

.new_report_header {
  justify-content: space-between;
  padding: 0;
}

.title_img h4 {
  display: inline;
}

.wp-admin-bar-wp-mail-smtp-menu {
  display: none !important;
}

html {
  font-size: inherit !important;
}

/* #wpadminbar #wp-admin-bar-top-secondary>#wp-admin-bar-my-account>a::after {
  position: absolute !important;
  transform: rotate(0deg) translate(0, -50%) !important;
  transform-origin: top;
  top: 59%;
  transition: all 0.3s ease-out !important;
  content: "\e64b" !important;
  font-family: "themify";
  font-weight: 600;
  border-right-color: transparent !important;
  font-size: 10px;
  right: -7px;
} */

.ps__thumb-y {
  background-color: #057cba4d !important;
}

#adminmenu #toplevel_page_cm_micropayments a.wp-not-current-submenu .wp-menu-image:before {
  content: "" !important;
  font-weight: bold;
  background-repeat: no-repeat;
  background-image: url("../img/icon/Subscriptions.svg");
}

#adminmenu #toplevel_page_cm_micropayments:hover a.wp-not-current-submenu .wp-menu-image:before {
  content: "" !important;
  font-weight: bold;
  background-repeat: no-repeat;
  background-image: url("../img/icon/White/SubscriptionsWhite.svg");
}

#adminmenu #toplevel_page_cm_micropayments a.wp-has-current-submenu .wp-menu-image:before {
  content: "" !important;
  font-weight: bold;
  background-repeat: no-repeat;
  background-image: url("../img/icon/White/SubscriptionsWhite.svg");
}

#adminmenu li.mm-active a.toplevel_page_cm_micropayments .wp-menu-image:before {
  content: "" !important;
  font-weight: bold;
  background-repeat: no-repeat;
  background-image: url("../img/icon/White/SubscriptionsWhite.svg");
}

.wp-has-current-submenu {
  background: transparent linear-gradient(270deg, #1658a5 0%, #0089c2 100%) 0% 0% no-repeat padding-box;
  border-radius: 10px;
}

.subscriptions_page_susbcription-api-form .meta_fields_wrap #address_2 {
  margin-bottom: 10px;
}

.user_crm_wrapper .single_crm .crm_body {
  padding: 10px 10px;
}

.echecks_page_wc-reports .refund_btn {
  -webkit-appearance: none !important;
}

#wpcontent .btn {
  font-size: inherit;
}

.cm-micropayment-platform-wallet .btn {
  -webkit-appearance: none;
}


#wpbody {
  padding: 0px 30px !important;
}

.wrap {
  margin: 0px !important;
}

#wp-admin-bar-my-account a:hover {
  background: none !important;
  color: #828bb2 !important;
}

#wp-admin-bar-my-account a,
#wp-admin-bar-my-account a span {
  line-height: 24px;
  color: #828bb2;
  font-size: 15px;
  margin-bottom: 0px;
  color: #2261aa !important;
  font-family: "Montserrat", sans-serif !important;
  font-weight: 600;
}

#wpadminbar .ab-top-menu>li.hover>.ab-item,
#wpadminbar.nojq .quicklinks .ab-top-menu>li>.ab-item:focus,
#wpadminbar:not(.mobile) .ab-top-menu>li:hover>.ab-item,
#wpadminbar:not(.mobile) .ab-top-menu>li>.ab-item:focus {
  background: none !important;
}

#wp-admin-bar-site-name {
  display: none !important;
}

#wp-admin-bar-comments {
  display: none !important;
}

#wp-admin-bar-new-content {
  display: none !important;
}

#wp-admin-bar-wpforms-menu {
  display: none !important;
}

#wp-admin-bar-my-account a span {
  padding-top: 5px;
  display: inline-block;
}

#adminmenu .nav_title label {
  color: #0186c0;
  font-weight: 500;
  padding: 0px 0px 0px 9px;
}

#wpwrap #adminmenumain .logo {
  margin: 0px;
  width: auto;
  padding: 33px 59px;
  margin-bottom: 20px;
}

#wpwrap #adminmenumain #adminmenu>li>a {
  font-size: 15px;
  font-weight: 400;
  color: #144339;
  padding: 0px 25px 0px 0px;
  background: transparent;
  transition: 0.3s;
  position: relative;
  z-index: 0;
  display: grid;
  grid-template-columns: 34px auto;
  grid-gap: 15px;
  align-items: center;
  position: relative;
  color: #8890b5;
  font-family: "Mulish", sans-serif;
  padding: 14px 10px 14px 26px;
  border-radius: 0 30px 30px 0;
  border-radius: 15px !important;
  line-height: 1.5;
}

#wpwrap #adminmenumain #adminmenu>li>a:hover {
  color: #fff !important;
  background: transparent linear-gradient(270deg, #1658a5 0%, #0089c2 100%) 0% 0% no-repeat padding-box;
  border-radius: 15px;
  box-shadow: 4px 4px 15px 0px #0089c2b0;
}

#wpwrap #adminmenumain #adminmenu>li {
  margin: 16px;
}

#wpwrap #adminmenumain #adminmenu>li a:hover {
  border-radius: 15px !important;
}

#wpwrap #adminmenumain #adminmenu>li a.active {
  color: #ffff;
  background: transparent linear-gradient(270deg, #1658a5 0%, #0089c2 100%) 0% 0% no-repeat padding-box;
  border-radius: 15px;
  box-shadow: 4px 4px 15px 0px #0089c2b0;
}

#wpwrap #adminmenumain #adminmenu>li>a.active {
  color: #ffff;
  background: transparent linear-gradient(270deg, #1658a5 0%, #0089c2 100%) 0% 0% no-repeat padding-box;
  border-radius: 15px;
  box-shadow: 4px 4px 15px 0px #0089c2b0;
}

#wpwrap #adminmenumain #adminmenu div.wp-menu-name {
  margin-left: inherit;
}

#wpwrap #adminmenumain #adminmenu>li a.active,
#wpwrap #adminmenumain #adminmenu>li a.current {
  color: #00fff8 !important;
  background: transparent linear-gradient(270deg, #1658a5 0%, #0089c2 100%) 0% 0% no-repeat padding-box;
  border-radius: 15px;
  box-shadow: 4px 4px 15px 0px #0089c2b0;
}

#adminmenuwrap ul#adminmenu a.wp-has-current-submenu:after,
#adminmenuwrap ul#adminmenu>li.current>a.current:after {
  height: auto !important;
  width: auto !important;
}

#wpwrap #adminmenumain #adminmenu>li.mm-active>a {
  background: transparent;
  color: #fff;
  box-shadow: 0px 0px 0px #0000000d !important;
}

#wpwrap #adminmenumain #adminmenu>li.wp-has-submenu {
  box-shadow: 0px 0px 0px #0000000d !important;
}

#wpwrap #adminmenumain #adminmenu>li.mm-active>a div.wp-menu-image:before {
  color: #fff;
}

#adminmenumain #adminmenu div.wp-menu-image:before {
  color: #0186c0;
  padding: 5px 0;
}

#adminmenumain #adminmenu .wp-has-current-submenu div.wp-menu-image:before {
  color: #fff;
}

#adminmenumain #adminmenu li.wp-has-current-submenu a.wp-has-current-submenu {
  color: #fff !important;
}

#toplevel_page_admin-page-wc-reports {
  display: none;
}

#adminmenumain.mini_sidebar #adminmenu a.current {
  width: 70%;
}

#wpwrap .mini_sidebar #adminmenu li.current div.wp-menu-name {
  color: #fff;
  display: block;
}

.toplevel_page_Settings #eCheck_custom_profile_settings img.profile-pic {
  width: 100%;
}

.toplevel_page_Settings #eCheck_custom_profile_settings #password button {
  background: linear-gradient(90deg, #1758a8 0%, #0887be 100%) !important;
}

.toplevel_page_cm_micropayments #cm_micropayments-page #cm-micropayments-reports-filter #submit {
  background: linear-gradient(90deg, #1758a8 0%, #0887be 100%) !important;
}

.toplevel_page_cm_micropayments #cm_micropayments-page .data_container {
  width: 100% !important;
}

#adminmenu .wp-submenu>li a {
  white-space: break-spaces !important;
  display: block !important;
}

#adminmenumain .mini_sidebar #adminmenu .wp-submenu>li a {
  display: block !important;
  white-space: nowrap !important;
}

#wp-admin-bar-searchboxadminbar {
  display: none;
}

.echecks_page_wc-reports #wp-admin-bar-searchboxadminbar {
  display: block !important;
}

@media screen and (max-width: 782px) {
  #wpbody {
    padding: 0px 15px !important;
  }
}

@media screen and (max-width: 380px) {
  #wpadminbar .header_notification_warp {
    margin-right: 5px !important;
    margin-left: 5px !important;
  }
}

@media screen and (min-width: 768px) {
  #wpadminbar #wp-admin-bar-togglesidebaricon {
    display: none;
  }
}

#adminmenu a:focus,
#adminmenu a:hover,
.folded #adminmenu .wp-submenu-head:hover {
  box-shadow: inset 0px 0 0 0 currentColor !important;
  transition: box-shadow 0.1s linear !important;
}

.login-page {
  background: url('https://portal.occamsadvisory.com/portal/echeckimg/banner_main.png');
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

.login-logo {
  width: 190px;
}

.login-left h1 {
  margin-bottom: 0;
  font-size: 34px;
  font-weight: bold;
  color: #fff
}

.login-left h2 {
  font-weight: 100;
  font-size: 45px;
  margin-bottom: 0;
  color: #fff
}

.login-left h3 {
  font-weight: 800;
  font-size: 47px;
  margin-bottom: 0;
  color: #fff
}

.login-left p {
  color: #fff;
  font-size: 18px;
  margin-bottom: 0;
  margin-top: 10px;
}

.login-head {
  font-size: 38px;
  letter-spacing: 0px;
  color: #00549A;
  opacity: 1;
  text-align: center;
  margin-bottom: 3%;
  font-size: 38px;
  padding-bottom: 21px;
  font-weight: bold;
}

.login-form {
  max-width: 420px;
  margin: 0 auto;
  height: auto;
  border: 2px solid #fff;
  border-radius: 30px;
  padding: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, .04);
}

.login-page .form-control {
  color: #333;
  border-radius: 2px;
  line-height: 27px;
  border: 0 !important;
  border-bottom: 2px solid #ccc !important;
  font-weight: 700;
  font-size: 15px;
  padding-left: 0px;
}

.login-page .form-control::placeholder {
  color: #00549A;
}

.login-page .form-control:focus {
  box-shadow: none;
}

.login-btn {
  font-size: 16px;
  background: #FF5C00;
  color: #fff;
  border-radius: 28px;
  text-transform: capitalize;
  box-shadow: 5px 5px 10px 0px rgb(178 178 178 / 40%);
  padding: 7px 47px;
  width: auto;
  border: 0;
  text-align: center;
  margin: 40px auto 0;
  display: block;
  height: 45px;
}

.login-page .forgetmenot label {
  color: #676767;
  font-size: 14px;
  line-height: 1.5;
  vertical-align: baseline;
}

.login-page .forgetmenot input {
  border-radius: 2px;
}

.forgot-pwd a {
  color: #00549A;
    font-weight: 600;
    font-size: 14px;
}

.form-select {
  border: 1px solid #7e8993 !important;
}

