import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Swal from 'sweetalert2';
import PageContainer from '../common/PageContainer.jsx';
import TemplatePreviewModal from './TemplatePreviewModal.jsx';
import './InvoiceSettings.css';
import { ENDPOINTS, MERCHANT_ID } from "./settings.js";

const InvoiceSettings = () => {
  // General Settings State
  const [interestRate, setInterestRate] = useState('');
  const [invoiceType, setInvoiceType] = useState('');
  
  // Invoice Reminder Settings State
  const [invoice_reminders, setInvoiceReminders] = useState([
    {
      id: 1,
      days: '', // Set to empty string to show "Select" option
      template_id: '',
      sms_template_id: '',
      repeat_days: null,
      days_type: 'calendar'
    },
    {
      id: 2,
      days: '', // Set to empty string to show "Select" option
      template_id: '',
      sms_template_id: '',
      repeat_days: null,
      days_type: 'calendar'
    },
    {
      id: 3,
      days: '', // Set to empty string to show "Select" option
      template_id: '',
      sms_template_id: '',
      repeat_days: null,
      days_type: 'calendar'
    }
  ]);

  // Template Options
  const [emailTemplates, setEmailTemplates] = useState([]);
  const [emailTemplateOptions, setEmailTemplateOptions] = useState([]);

  const smsTemplates = [
    { value: '', label: 'Select reminder sms template' },
    { value: '9', label: 'Initial Friendly SMS Reminder' },
    { value: '10', label: 'Second Direct SMS Reminder' },
    { value: '11', label: 'Firm SMS Reminder' },
    { value: '12', label: 'Final Follow-Up SMS Reminder' }
  ];

  const overdueDaysOptions = [
    '1', '2', '3', '4', '5', '6', '7', '8', '9', '10',
    '11', '12', '13', '14', '15', '16', '17', '18', '19', '20',
    '21', '22', '23', '24', '25', '26', '27', '28', '29', '30',
    '31'
  ];

  const repeatDaysOptions = [
    { value: '', label: 'Select repeat days' },
    { value: '1', label: 'Repeat every 1 day' },
    { value: '2', label: 'Repeat every 2 days' },
    { value: '3', label: 'Repeat every 3 days' },
    { value: '4', label: 'Repeat every 4 days' },
    { value: '5', label: 'Repeat every 5 days' },
    { value: '6', label: 'Repeat every 6 days' },
    { value: '7', label: 'Repeat every 7 days' },
    { value: '8', label: 'Repeat every 8 days' },
    { value: '9', label: 'Repeat every 9 days' },
    { value: '10', label: 'Repeat every 10 days' },
    { value: '11', label: 'Repeat every 11 days' },
    { value: '12', label: 'Repeat every 12 days' },
    { value: '13', label: 'Repeat every 13 days' },
    { value: '14', label: 'Repeat every 14 days' },
    { value: '15', label: 'Repeat every 15 days' },
  ];

  // Loading states
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [templateData, setTemplateData] = useState(null);
  const [templateLoading, setTemplateLoading] = useState(false);
  
  // Validation errors state
  const [validationErrors, setValidationErrors] = useState([]);
  
  // Track which fields have been touched/interacted with
  const [touchedFields, setTouchedFields] = useState(new Set());
  
  // Track if form has been submitted (to show validation errors)
  const [formSubmitted, setFormSubmitted] = useState(false);

  // Fetch settings on component mount
  useEffect(() => {
    fetchInvoiceSettings();
    fetchEmailTemplates();
  }, []);

  const fetchInvoiceSettings = async () => {
    setLoading(true);
    try {
      const response = await axios.get(ENDPOINTS.GET_INVOICE_SETTINGS);
      if (response.data.success) {
        const data = response.data.data[0];
        setInterestRate(data.interest_rate || '25.00');
        setInvoiceType(data.invoice_type || 'IR/BR Invoices');
        
        // Parse the invoice_reminder_setting JSON string
        let parsedReminders = [];
        if (data.invoice_reminder_setting) {
          try {
            parsedReminders = JSON.parse(data.invoice_reminder_setting);
                         // Add default days_type to each reminder if not present
             parsedReminders = parsedReminders.map((reminder, index) => ({
               ...reminder,
               sms_template_id: reminder.sms_template_id || '',
               days_type: reminder.days_type || 'calendar',
               days: reminder.days ? parseInt(reminder.days) : '' // Convert days to integer if present, otherwise keep empty
             }));
            // console.log('Original reminder structure:', JSON.parse(data.invoice_reminder_setting));
            // console.log('Modified reminders:', parsedReminders);
          } catch (error) {
            console.error('Error parsing invoice_reminder_setting:', error);
            parsedReminders = [];
          }
        }
        setInvoiceReminders(parsedReminders);
        // console.log('Parsed invoice reminders with SMS templates:', parsedReminders);
        // console.log(data.invoice_reminder_setting);
      }
    } catch (error) {
      console.error('Error fetching invoice settings:', error);
      Swal.fire('Error', 'Failed to load invoice settings', 'error');
    } finally {
      setLoading(false);
    }
  };

  const fetchEmailTemplates = async () => {
    try {
      const response = await axios.get(ENDPOINTS.GET_EMAIL_TEMPLATES);
      if (response.data.success) {
        const templates = response.data.data;
        setEmailTemplates(templates);
        
        // Create options for dropdown
        const options = templates.map(template => ({
          value: template.templateid,
          label: template.name
        }));
        // console.log('Email template options:', options);
        setEmailTemplateOptions(options);
      }
    } catch (error) {
      console.error('Error fetching email templates:', error);
      Swal.fire('Error', 'Failed to load email templates', 'error');
    }
  };

  const saveInvoiceSettings = async () => {
    // Mark form as submitted to show validation errors
    setFormSubmitted(true);
    
    // Clear previous validation errors
    setValidationErrors([]);
    
    // Comprehensive validation
    const validationErrors = [];
    
    // Validate general settings
    if (!interestRate || interestRate === '') {
      validationErrors.push('Interest Rate is required');
    }
    
    if (!invoiceType || invoiceType === '') {
      validationErrors.push('Invoice Type is required');
    }
    
    // Check for duplicate day entries
    const usedDays = new Set();
    const duplicateErrors = [];
    
    invoice_reminders.forEach((reminder, index) => {
      // Check if overdue days is selected
      if (!reminder.days || reminder.days === '') {
        validationErrors.push(`Row ${index + 1}: Overdue Days is required`);
      }
      
      // Check if email template is selected
      if (!reminder.template_id || reminder.template_id === '') {
        validationErrors.push(`Row ${index + 1}: Email Template is required`);
      }
      
      // Check if SMS template is selected
      if (!reminder.sms_template_id || reminder.sms_template_id === '') {
        validationErrors.push(`Row ${index + 1}: SMS Template is required`);
      }
      
      // Check if repeat_days is selected when days === 31
      if (reminder.days === 31 && (!reminder.repeat_days || reminder.repeat_days === '')) {
        validationErrors.push(`Row ${index + 1}: Repeat Days is required when Overdue Days is set to 31`);
      }
      
      // Check for duplicate days
      if (reminder.days && reminder.days !== '') {
        if (usedDays.has(reminder.days)) {
          duplicateErrors.push(`Row ${index + 1}: Day ${reminder.days} is already used by another reminder`);
        } else {
          usedDays.add(reminder.days);
        }
      }
    });
    
    // Check if there are any reminders at all
    if (invoice_reminders.length === 0) {
      validationErrors.push('At least one invoice reminder is required');
    }
    
    // Show all validation errors if any exist
    if (validationErrors.length > 0 || duplicateErrors.length > 0) {
      const allErrors = [...validationErrors, ...duplicateErrors];
      setValidationErrors(allErrors);
      return;
    }
    
    setSaving(true);
    try {
      // Remove 'id' field from each invoice reminder before submitting
      const cleanedInvoiceReminders = invoice_reminders.map(({ id, ...reminder }) => reminder);
      
      // Convert payload to FormData
      const formData = new FormData();
      formData.append('merchant_id', '3');
      formData.append('interest_rate', parseFloat(interestRate));
      formData.append('invoice_type', invoiceType);
      formData.append('type', 'merchant_setting');
      formData.append('invoice_reminders', JSON.stringify(cleanedInvoiceReminders));
      
      const response = await axios.post(ENDPOINTS.SAVE_INVOICE_SETTINGS, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      if (response.data.data.status === 200) {
        const message = response.data.message || 'Settings saved successfully';
        const capitalizedMessage = message.charAt(0).toUpperCase() + message.slice(1);
        Swal.fire('Success', capitalizedMessage, 'success');
      } else {
        throw new Error(response.data.message || 'Failed to save settings');
      }
    } catch (error) {
      // console.error('Error saving invoice settings:', error);
      // console.error('Error response:', error.response?.data);
      // console.error('Error status:', error.response?.status);
      Swal.fire('Error', `Failed to save invoice settings: Failed to save invoice settings}`, 'error');
    } finally {
      setSaving(false);
    }
  };

  const addInvoiceReminder = () => {
    // Check if there are too many reminders (optional limit)
    /*if (invoice_reminders.length >= 10) {
      setValidationErrors(['You can only add up to 10 invoice reminders.']);
      return;
    } */

    // Clear validation errors when adding new reminder
    if (validationErrors.length > 0) {
      setValidationErrors([]);
    }
    
    // Reset form submitted state for new reminders
    setFormSubmitted(false);

    const newInvoiceReminder = {
      id: Date.now(),
      days: '', // Set to empty string to show "Select" option
      template_id: '',
      sms_template_id: '',
      repeat_days: null,
      days_type: 'calendar'
    };
    setInvoiceReminders([...invoice_reminders, newInvoiceReminder]);
    
    // Don't mark new reminder fields as touched - let user interact first
  };

  const removeInvoiceReminder = (index) => {
    Swal.fire({
      title: 'Are you sure?',
      text: 'This invoice reminder will be permanently deleted.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        setInvoiceReminders(invoice_reminders.filter((invoice_reminder, i) => i !== index));
        // Clear validation errors when removing reminder
        if (validationErrors.length > 0) {
          setValidationErrors([]);
        }
        // Show success message after deletion
        Swal.fire({
          icon: "success",
          title: "Deleted",
          text: 'Invoice reminder has been successfully deleted.',
          timer: 2000,
          showConfirmButton: false
        });
      }
    });
  };

  const updateInvoiceReminder = (index, field, value) => {
    // If updating the days field, check for duplicates
    if (field === 'days') {
      const dayValue = value === '' ? '' : parseInt(value);
      
      // Check if this day is already used by another reminder
      if (isDayAlreadyUsed(dayValue, index)) {
        return; // Don't update if duplicate
      }
    }

    // Mark this field as touched
    const fieldKey = `${index}-${field}`;
    setTouchedFields(prev => new Set([...prev, fieldKey]));

    // Clear validation errors when user starts typing/selecting
    if (validationErrors.length > 0) {
      setValidationErrors([]);
    }

    setInvoiceReminders(invoice_reminders.map((invoice_reminder, i) => 
      i === index ? { ...invoice_reminder, [field]: value } : invoice_reminder
    ));
  };

  // Helper function to check if a day is already used by other reminders
  const isDayAlreadyUsed = (dayValue, currentIndex) => {
    if (!dayValue || dayValue === '') return false;
    return invoice_reminders.some((reminder, index) => 
      index !== currentIndex && reminder.days === dayValue
    );
  };

  // Helper function to get available days for a specific reminder
  const getAvailableDays = (currentIndex) => {
    const usedDays = new Set();
    invoice_reminders.forEach((reminder, index) => {
      if (index !== currentIndex && reminder.days && reminder.days !== '') {
        usedDays.add(reminder.days);
      }
    });
    
    return overdueDaysOptions.filter(day => !usedDays.has(day));
  };

  // Helper function to check if a reminder is valid
  const isReminderValid = (reminder) => {
    if (reminder.days === 31) {
      return reminder.repeat_days && reminder.repeat_days !== '';
    }
    return true;
  };

  const previewInvoiceReminder = async (invoice_reminder) => {
    // Validate that email template is selected
    if (!invoice_reminder.template_id || invoice_reminder.template_id === '') {
      Swal.fire('Validation Error', 'Please select an Email Template before previewing', 'warning');
      return;
    }

    setTemplateLoading(true);
    try {
      // Call the preview API
      const formData = new FormData();
      formData.append('template_id', invoice_reminder.template_id);
      formData.append('sms_template_id', invoice_reminder.sms_template_id);
      
      const response = await axios.post(ENDPOINTS.PREVIEW_EMAIL_TEMPLATE, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data.success) {
        // Set template data and open modal
        setTemplateData(response.data.data);
        setIsModalOpen(true);
      } else {
        Swal.fire('Error', 'Failed to load email template preview', 'error');
      }
    } catch (error) {
      console.error('Error loading email template preview:', error);
      Swal.fire('Error', 'Failed to load email template preview', 'error');
    } finally {
      setTemplateLoading(false);
    }
  };

  if (loading) {
    return (
      <PageContainer title="Invoice Settings">
        <div className="text-center p-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading invoice settings...</p>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer title="Invoice Settings">
      {templateLoading && (
      <div className="overlay-loading d-flex flex-column justify-content-center align-items-center">
        <svg class="loader" viewBox="0 0 200 100">
          <defs>
          <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#007bff" />
          <stop offset="100%" stop-color="#ff6600" />
          </linearGradient>
          </defs>
          <path className="infinity-shape"
                d="M30,50
                  C30,20 70,20 100,50
                  C130,80 170,80 170,50
                  C170,20 130,20 100,50
                  C70,80 30,80 30,50"
              />
        </svg>
        <p className="mt-3 mb-0 text-muted">Processing data...</p>
      </div>
      )}
        <div className="invoice-settings-container"> 
         {/* General Invoice Settings - No Title */}
         <div className="settings-section">
          <div className="row">
                         <div className="col-md-4">
               <div className="form-group">
                 <label htmlFor="interestRate" className="form-label">Interest Rate(%)</label>
                                                                       <input
                     type="number"
                     className={`form-control form-control-sm ${
                       (formSubmitted && (!interestRate || interestRate === '')) ? 'is-invalid' : ''
                     }`}
                     id="interestRate"
                     value={interestRate}
                     onChange={(e) => {
                       setInterestRate(e.target.value);
                       // Clear validation errors when user starts typing
                       if (validationErrors.length > 0) {
                         setValidationErrors([]);
                       }
                     }}
                     step="0.01"
                     min="0"
                     max="100"
                   />
                   {(formSubmitted && (!interestRate || interestRate === '')) && (
                     <div className="invoice-settings-invalid-feedback d-block">
                       Interest rate is required
                     </div>
                   )}
               </div>
             </div>
             <div className="col-md-8">
               <div className="form-group">
                 <label htmlFor="invoiceType" className="form-label">Invoice Type</label>
                                   <select
                    className={`form-control form-control-lg ${
                      (validationErrors.length > 0 && (!invoiceType || invoiceType === '')) ? 'is-invalid' : ''
                    }`}
                    id="invoiceType"
                    value={invoiceType}
                    onChange={(e) => {
                      setInvoiceType(e.target.value);
                      // Clear validation errors when user starts typing
                      if (validationErrors.length > 0) {
                        setValidationErrors([]);
                      }
                    }}
                  >
                    <option value="">Select Invoice Type</option>
                    <option value="all">All</option>
                    <option value="retainers">IR/BR Invoices</option>
                    <option value="success">Success Invoices</option>
                  </select>
                  {(validationErrors.length > 0 && (!invoiceType || invoiceType === '')) && (
                    <div className="invoice-settings-invalid-feedback d-block">
                      Invoice type is required
                    </div>
                  )}
               </div>
             </div>
          </div>
        </div>

        {/* Invoice Reminder Configuration */}
        <div className="settings-section">
                     <div className="d-flex justify-content-between align-items-center mb-3">
             <h5 className="section-title mb-0">Invoice Reminder</h5>
             <button
               className="add-reminder-btn"
               onClick={addInvoiceReminder}
             >
               <i className="fas fa-plus"></i>
               Add More Reminder
             </button>
           </div>
           
           

          <div className="reminder-table">
            <div className="reminder-header">
              <div className="reminder-col">OVERDUE DAYS</div>
              <div className="reminder-col">INVOICE EMAIL TEMPLATE</div>
              <div className="reminder-col">INVOICE SMS TEMPLATE</div>
              <div className="reminder-col">ACTION</div>
            </div>

            {invoice_reminders.map((invoice_reminder, index) => (
              <div key={index} className="reminder-row">
                <div className="reminder-col">
                  <div className="d-flex">
                                                              <select
                        className={`form-control form-control-sm ${
                          isDayAlreadyUsed(invoice_reminder.days, index) || 
                          (formSubmitted && (!invoice_reminder.days || invoice_reminder.days === '')) ? 'is-invalid' : ''
                        }`}
                        value={invoice_reminder.days || ''}
                        onChange={(e) => updateInvoiceReminder(index, 'days', e.target.value === '' ? '' : parseInt(e.target.value))}
                      >
                        <option value="">Select overdue days</option>
                        {getAvailableDays(index).map(day => (
                          <option key={day} value={day}>{day} days</option>
                        ))}
                        {/* Show currently selected day even if it's used by another reminder */}
                        {invoice_reminder.days && invoice_reminder.days !== '' && !getAvailableDays(index).includes(invoice_reminder.days) && (
                          <option key={invoice_reminder.days} value={invoice_reminder.days}>
                            {invoice_reminder.days} days (Already used)
                          </option>
                        )}
                      </select>
                      {(isDayAlreadyUsed(invoice_reminder.days, index) || (formSubmitted && (!invoice_reminder.days || invoice_reminder.days === ''))) && (
                        <div className="invoice-settings-invalid-feedback d-block">
                          {isDayAlreadyUsed(invoice_reminder.days, index) 
                            ? 'This day is already selected for another reminder'
                            : 'Overdue days is required'
                          }
                        </div>
                      )}
                    {invoice_reminder.days === 31 && (
                      <div className="w-100">
                                                                                                   <select
                            className={`form-control form-control-sm ${(formSubmitted && !isReminderValid(invoice_reminder)) ? 'is-invalid' : ''}`}
                            value={invoice_reminder.repeat_days || ''}
                            onChange={(e) => updateInvoiceReminder(index, 'repeat_days', e.target.value)}
                            required
                          >
                            <option value="">Select Repeat Days *</option>
                            {repeatDaysOptions.map(option => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))}
                          </select>
                          {(formSubmitted && !isReminderValid(invoice_reminder)) && (
                            <div className="invoice-settings-invalid-feedback d-block">
                              Repeat days is required when overdue days is set to 31
                            </div>
                          )}
                      </div>
                    )}
                  </div>
                </div>
                <div className="reminder-col">
                  <select
                    className={`form-control form-control-sm ${
                      (formSubmitted && (!invoice_reminder.template_id || invoice_reminder.template_id === '')) ? 'is-invalid' : ''
                    }`}
                    value={invoice_reminder.template_id || ''}
                    onChange={(e) => updateInvoiceReminder(index, 'template_id', e.target.value)}
                  >
                    <option value="">Select Email Template</option>
                    {emailTemplateOptions.map(template => (
                      <option key={template.value} value={template.value}>
                        {template.label}
                      </option>
                    ))}
                  </select>
                  
                  {(formSubmitted && (!invoice_reminder.template_id || invoice_reminder.template_id === '')) && (
                    <div className="invoice-settings-invalid-feedback">
                      Email template is required
                    </div>
                  )}
                </div>
                <div className="reminder-col">
                  <select
                    className={`form-control form-control-sm ${
                      (formSubmitted && (!invoice_reminder.sms_template_id || invoice_reminder.sms_template_id === '')) ? 'is-invalid' : ''
                    }`}
                    value={invoice_reminder.sms_template_id || ''}
                    onChange={(e) => updateInvoiceReminder(index, 'sms_template_id', e.target.value)}
                  >
                    <option value="">Select Reminder SMS template</option>
                    <option value="9">Initial Friendly SMS Reminder</option>
                    <option value="10">Second Direct SMS Reminder</option>
                    <option value="11">Firm SMS Reminder</option>
                    <option value="12">Final Follow-Up SMS Reminder</option>
                  </select>
                  {(formSubmitted && (!invoice_reminder.sms_template_id || invoice_reminder.sms_template_id === '')) && (
                    <div className="invoice-settings-invalid-feedback">
                      SMS template is required
                    </div>
                  )}
                 </div>
                <div className="reminder-col">
                  <div className="d-flex gap-2">
                                                              <button
                        className={`preview-btn ${
                          (!invoice_reminder.template_id || invoice_reminder.template_id === '')
                            ? 'disabled-preview'
                            : ''
                        }`}
                        onClick={() => previewInvoiceReminder(invoice_reminder)}
                        disabled={loading}
                        title={
                          (!invoice_reminder.template_id || invoice_reminder.template_id === '')
                            ? 'Please select an Email Template to preview'
                            : 'Preview selected email template'
                        }
                      >
                        Preview
                      </button>
                    <button
                      className="delete-btn"
                      onClick={() => removeInvoiceReminder(index)}
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Save Button */}
        <div className="save-button-container">
          <button
            className="save-settings-btn"
            onClick={saveInvoiceSettings}
            disabled={saving}
          >
            {saving ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Saving...
              </>
            ) : (
              'Submit'
            )}
          </button>
        </div>
      </div>
      
      {/* Template Preview Modal */}
      <TemplatePreviewModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        templateData={templateData}
      />
    </PageContainer>
  );
};

export default InvoiceSettings; 