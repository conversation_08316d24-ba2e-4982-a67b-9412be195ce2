import React, { useEffect } from 'react';
import PaidModalContent from './Modals/PaidModalContent';
import CancelledModalContent from './Modals/CancelledModalContent';
import ReminderModalContent from './Modals/ReminderModalContent';
import PaymentProcessModalContent from './Modals/PaymentProcessModalContent';
import PartialPaidModalContent from './Modals/PartialPaidModalContent';
import PaymentPlanModalContent from './Modals/PaymentPlanModalContent';
import ResendInvoiceModalContent from './Modals/ResendInvoiceModalContent';
import UpdateInterestModalContent from './Modals/UpdateInterestModalContent';
import PauseReminderModalContent from './Modals/PauseReminderModalContent'; 
import ResumeReminderModalContent from './Modals/ResumeReminderModalContent';
import ShareInvoiceLinkModalContent from './Modals/ShareInvoiceLinkModalContent';
import DeleteInvoiceModalContent from './Modals/DeleteInvoiceModalContent';
import DefaultModalContent from './Modals/DefaultModalContent';
import { ACTION_IDS } from './invoice-settings';

const ModalContent = ({ modalData, actionsMap }) => {
  const { actionType, invoiceId, resetActionSelection } = modalData;

  // Normalize: "2" -> 2, keep string actions like 'send_reminder' as-is
  const normalizedType =
    typeof actionType === 'string' && /^\d+$/.test(actionType)
      ? Number(actionType)
      : actionType;

  useEffect(() => {
    if (typeof resetActionSelection === 'function') {
      resetActionSelection();
    }
  }, [normalizedType, invoiceId, resetActionSelection]);

  const actionText = actionsMap?.[normalizedType]?.text || String(normalizedType);

  switch (normalizedType) {
    case ACTION_IDS.PAID:
      return <PaidModalContent modalData={modalData} />;
    case ACTION_IDS.CANCEL:
      return <CancelledModalContent modalData={modalData} />;
    case ACTION_IDS.SEND_REMINDER:
      return <ReminderModalContent modalData={modalData} />;
    case ACTION_IDS.PAYMENT_IN_PROCESS:
      return <PaymentProcessModalContent modalData={modalData} />;
    case ACTION_IDS.PARTIALLY_PAID:
      return <PartialPaidModalContent modalData={modalData} />;
    case ACTION_IDS.PAYMENT_PLAN:
      return <PaymentPlanModalContent modalData={modalData} />;
    case ACTION_IDS.RESEND:
      return <ResendInvoiceModalContent modalData={modalData} />;
    case ACTION_IDS.UPDATE_INTEREST:
      return <UpdateInterestModalContent modalData={modalData} />;
    case ACTION_IDS.SHARE_INVOICE_LINK:
      return <ShareInvoiceLinkModalContent modalData={modalData} />;
    case ACTION_IDS.DELETE:
      return <DeleteInvoiceModalContent modalData={modalData} />;
    case ACTION_IDS.CANCEL_AUTO_INV_REMINDER:
      return <PauseReminderModalContent modalData={modalData} />;
    case ACTION_IDS.RESUME_AUTO_INV_REMINDER:
      return <ResumeReminderModalContent modalData={modalData} />;
    default:
      return <DefaultModalContent modalData={modalData} actionText={actionText} />;
  }
};

export default ModalContent;
