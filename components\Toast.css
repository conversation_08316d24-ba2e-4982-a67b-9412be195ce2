.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1075; /* Higher than modals (1060) but lower than loading overlay (1080) */
  max-width: 300px;
  transform: translateX(110%);
  transition: transform 0.3s ease-in-out, opacity 0.3s ease;
  opacity: 0;
}

.toast-container.visible {
  transform: translateX(0);
  opacity: 1;
}

.toast-content {
  background-color: #fff;
  color: #333;
  padding: 12px 16px;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-left: 4px solid #ff7f50; /* Orange color to match the Copy Link button */
  font-size: 14px;
  font-weight: 500;
}

.toast-content::before {
  content: '✓';
  margin-right: 10px;
  color: #ff7f50;
  font-weight: bold;
}

.toast-close {
  background: none;
  border: none;
  color: #999;
  font-size: 18px;
  cursor: pointer;
  margin-left: 10px;
  padding: 0 5px;
  transition: color 0.2s;
}

.toast-close:hover {
  color: #333;
}
