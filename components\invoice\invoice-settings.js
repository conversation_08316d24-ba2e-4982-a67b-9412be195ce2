// Centralized settings for invoice module

export const INVOICE_API_BASE = 'https://portal.occamsadvisory.com/portal/wp-json';
export const INVOICE_V1 = `${INVOICE_API_BASE}/invoices/v1`;

// ---------- Endpoints ----------
export const ENDPOINTS = {
  GET_INVOICE_ACTION: `${INVOICE_V1}/get-invoice-action`,
  SEND_PARTIALLY_PAID: `${INVOICE_V1}/send-partially-paid-invoice-action`,
  CUSTOM_REMINDER_TEMPLATES: `${INVOICE_V1}/get-invoice-template-reminders`,
  SEND_REMINDER_ACTION: `${INVOICE_V1}/send-reminder-action`,
  SEND_REMINDER_MAIL_ACTION: `${INVOICE_V1}/send-reminder-mail-action`,
  //SEND_ADD_INTEREST: `${INVOICE_V1}/send-add-interest-action`,
  SEND_ADD_INTEREST: `${INVOICE_API_BASE}/v1/interest-automation-invoice-by-id`,
  INVOICE_LISTING: `${INVOICE_V1}/invoice-listing`,
  MARK_DELETE_INVOICE_ACTION: `${INVOICE_V1}/mark-delete-action`,
  SEND_PAUSE_REMINDER_ACTION: `${INVOICE_V1}/send-pause-invoice-reminder-action`,
  SEND_RESUME_REMINDER_ACTION: `${INVOICE_V1}/send-resume-invoice-reminder-action`,
};

// ---------- Canonical IDs (no more magic numbers) ----------
export const STATUS_IDS = Object.freeze({
  UNPAID: 1,
  PAID: 2,
  CANCELLED: 3,
  DRAFT: 4,
  REMINDER: 5,
  PAYMENT_IN_PROCESS: 6,
  //VOID: 14,
  PARTIALLY_PAID: 14,
 // PAYMENT_PLAN: 19, // keep if/when you use
});

export const ACTION_IDS = Object.freeze({
  UNPAID: 1,
  PAID: 2,
  CANCEL: 3,
  DRAFT: 4,
  PAYMENT_IN_PROCESS: 6,
  DELETE: 13,
  //VOID: 14,
  PARTIALLY_PAID: 14,
  //PAYMENT_PLAN: 19, // keep if/when you use

  // string-based UI actions
  RESEND: 'resend',
  SHARE_INVOICE_LINK: 'share_invoice_link',
  SEND_REMINDER: 'send_reminder',
  UPDATE_INTEREST: 'update_interest',
  CANCEL_AUTO_INV_REMINDER: 'cancel_auto_inv_reminder',
  RESUME_AUTO_INV_REMINDER: 'resume_auto_inv_reminder',
  PP_INFO: 'pp_info', // info-only partial payment view
});

export const SPECIAL_USER_IDS = Object.freeze({
  CAN_SEE_DELETE: 45117, // the only user who should see 'Delete'
});

// Pagination / date defaults
export const DEFAULT_PAGE_SIZE = 50;
export const DEFAULT_INVOICE_DATE_RANGE_DAYS = 15; // existing export kept

// ---------- Payment Modes ----------
export const PAYMENT_MODES = [
  { label: 'Occams Initiated - eCheck', value: 'occams_initiated_eCheck' },
  { label: 'Occams Initiated - ACH', value: 'occams_initiated_ach' },
  { label: 'Client Initiated - Wire', value: 'occams_initiated_wire' },
  { label: 'Client Initiated - ACH', value: 'client_initiated_ach' },
  { label: 'Client Initiated - Check Mailed', value: 'client_initiated_check_mailed' },
  { label: 'Credit Card or Debit Card', value: 'credit_card_or_debit_card' },
];

// (Optional small map; keep if you use it elsewhere)
export const STATUS_MAP = {
  '0': 'Draft',
  '1': 'Sent',
  '2': 'Paid',
  '3': 'Partially Paid',
  '4': 'Cancelled',
};

// Merchant
export const MERCHANT_ID = '1';

// ---------- Status map (now keyed by STATUS_IDS) ----------
export const STATUS_MAP_FULL = {
  [STATUS_IDS.UNPAID]:           { text: 'Unpaid',             color: '#FFA500' },
  [STATUS_IDS.PAID]:             { text: 'Paid',               color: '#008000' },
  [STATUS_IDS.CANCELLED]:        { text: 'Cancelled',          color: '#FF0000' },
  [STATUS_IDS.DRAFT]:            { text: 'Draft',              color: '#808080' },
  [STATUS_IDS.REMINDER]:         { text: 'Reminder',           color: '#f06b25' },
  [STATUS_IDS.PAYMENT_IN_PROCESS]:{ text: 'Payment in process',color: '#0000FF' },
  [STATUS_IDS.VOID]:             { text: 'Void',               color: '#FF0000' },
  [STATUS_IDS.PARTIALLY_PAID]:   { text: 'Partial paid',       color: '#FF8C00' },
  // [STATUS_IDS.PAYMENT_PLAN]:   { text: 'Payment Plan',       color: '#FF0000' },
};

// ---------- Action map (same API-facing keys, sourced from ACTION_IDS) ----------
export const ACTIONS_MAP = {
  [ACTION_IDS.UNPAID]:              { text: 'Unpaid' },
  [ACTION_IDS.PAID]:                { text: 'Paid' },
  [ACTION_IDS.CANCEL]:              { text: 'Cancel' },
  [ACTION_IDS.DRAFT]:               { text: 'Draft' },
  [ACTION_IDS.PAYMENT_IN_PROCESS]:  { text: 'Payment In Process' },
  [ACTION_IDS.VOID]:                { text: 'Void' },
  [ACTION_IDS.PARTIALLY_PAID]:      { text: 'Partially Paid' },
  // [ACTION_IDS.PAYMENT_PLAN]:      { text: 'Payment Plan' },

  [ACTION_IDS.RESEND]:                { text: 'Resend Invoice' },
  [ACTION_IDS.SHARE_INVOICE_LINK]:    { text: 'Share Invoice Link' },
  [ACTION_IDS.SEND_REMINDER]:         { text: 'Send Reminder' },
  [ACTION_IDS.UPDATE_INTEREST]:       { text: 'Update Interest' },
  [ACTION_IDS.CANCEL_AUTO_INV_REMINDER]: { text: 'Pause Invoice Reminder' },
  [ACTION_IDS.RESUME_AUTO_INV_REMINDER]: { text: 'Resume Invoice Reminder' },
  [ACTION_IDS.PP_INFO]:               { text: 'Partial Payment Details' },
};

// ---------- Centralized modal sizes ----------
export const MODAL_SIZES = Object.freeze({
  // numeric actions
  [ACTION_IDS.UNPAID]:             'lg',
  [ACTION_IDS.PAID]:               'lg',
  [ACTION_IDS.CANCEL]:             'lg',
  [ACTION_IDS.DRAFT]:              'lg',
  [ACTION_IDS.PAYMENT_IN_PROCESS]: 'lg',
  [ACTION_IDS.DELETE]:             'md', // confirm dialog fits well in md
  [ACTION_IDS.VOID]:               'lg',
  [ACTION_IDS.PARTIALLY_PAID]:     'xl',
  [ACTION_IDS.PAYMENT_PLAN]:       'xl',

  // string actions
  [ACTION_IDS.RESEND]:                 'lg',
  [ACTION_IDS.SHARE_INVOICE_LINK]:     'lg',
  [ACTION_IDS.SEND_REMINDER]:          'lg',
  [ACTION_IDS.UPDATE_INTEREST]:        'lg',
  [ACTION_IDS.CANCEL_AUTO_INV_REMINDER]:'lg',
  [ACTION_IDS.RESUME_AUTO_INV_REMINDER]:'lg',
  [ACTION_IDS.PP_INFO]:                'xl', // info-only partial payment details

  // fallback
  default: 'md', // sizes: 'sm' | 'md' | 'lg' | 'xl' | 'full'
});

export const ALLOWED_ROLES_FOR_SHARE_LINK = Object.freeze([
  "echeck_client","master_sales","iris_sales_agent_rep",
  "iris_sales_agent","fprs_sales_agent","iris_affiliate_users",
]);

// get current login user data
export const getCurrentUserInvoice = () => {
  try {
    const userObj = JSON.parse(localStorage.getItem("user"));
    return userObj?.user || userObj?.data?.user || null;
  } catch (err) {
    console.error("Failed to parse user from localStorage:", err);
    return null;
  }
};