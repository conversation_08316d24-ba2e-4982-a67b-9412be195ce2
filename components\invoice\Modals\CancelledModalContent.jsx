import React, { useState, useEffect, useRef } from 'react';
import Swal from 'sweetalert2';
import axios from 'axios';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import EmailToggleSection from '../Helpers/EmailToggleSection';

import { handleCancelInvoiceSave } from '../invoiceActionHandlers';
import { cancelInvoiceSchema } from '../Helpers/validationLibrary/InvoiceValidator';
import { useInvoiceValidation } from '../Helpers/validationLibrary/useInvoiceValidation';
import { useEmailValidation } from '../Helpers/validationLibrary/useEmailValidation';
import { getCurrentUserInvoice, ENDPOINTS, PAYMENT_MODES, ACTION_IDS } from '../invoice-settings';

import ReadOnlyDateInput from '../Helpers/ReadOnlyDateInput';



const CancelledModalContent = ({ modalData }) => {
  const [inputRows, setInputRows] = useState([{
    note: '',
  }]);

  const [sendEmail, setSendEmail] = useState(true);
  const [emailTo, setEmailTo] = useState('');
  const [cc, setCc] = useState('');
  const [bcc, setBcc] = useState('');
  const [subject, setSubject] = useState('');
  const [emailBody, setEmailBody] = useState('<p></p>');

  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [formHasErrors, setFormHasErrors] = useState(false);
  const [emailUpdateNote, setEmailUpdateNote] = useState('');
  const [noteValidationError, setNoteValidationError] = useState(false);
  const sendEmailRef = useRef();

  const { errors, setErrors, validateAllRows, handleInputChange } = useInvoiceValidation(
    cancelInvoiceSchema, inputRows, setInputRows
  );

  // Email validation hook
  const {
    emailErrors,
    validateAllEmailFields,
    clearEmailErrors,
    validateEmailField,
  } = useEmailValidation();

  const handleEmailFieldChange = (field, value) => {
    validateEmailField(field, value);
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.post(ENDPOINTS.GET_INVOICE_ACTION, {
          action_type: String(ACTION_IDS.CANCEL),
          invoiceid: modalData?.invoiceId,
          invoiceAmount: modalData?.invoiceAmount,
        });
        const data = response.data || {};
        setEmailTo(data.user_email || '');
        setCc(data.cc_email || '');
        setBcc(data.bcc_email || '');
        setSubject(data.subject || '');
        setEmailBody(data.main_content || '<p></p>');
      } catch (err) {
        // console.error('[CancelModal] Failed to fetch:', err);
      } finally {
        setLoading(false);
      }
    };

    if (modalData?.invoiceId && modalData?.invoiceAmount) {
      fetchData();
    } else {
      setLoading(false);
    }
  }, [modalData?.invoiceId, modalData?.invoiceAmount]);

  const formatDateToDMY = (date) => {
    if (!date || isNaN(new Date(date).getTime())) return '';
    const d = new Date(date);
    return `${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getDate().toString().padStart(2, '0')}/${d.getFullYear()}`;
  };
  const handleSubmit = async () => {
    const isValid = await validateAllRows();
    const user = getCurrentUserInvoice();

    let isValidEmail = true;
    let isValidNote = true;

    if (sendEmail) {
      isValidEmail = await validateAllEmailFields({
        to: emailTo,
        cc: cc,
        bcc: bcc,
        subject: subject,
      });
    } else {
      clearEmailErrors?.(); // Very important before switching to note validation
      isValidNote = await sendEmailRef.current?.triggerValidation();
    }

    //Set form error state correctly for both modes
    const hasErrors = !isValid || (sendEmail ? !isValidEmail : !isValidNote);
    setFormHasErrors(hasErrors);
    if (hasErrors) return;

    setSubmitting(true);

    const row = inputRows[0];

    const params = {
      action_type: 'cancel',
      invoiceid: modalData?.invoiceId,
      // manual_payment_mode: row.paymentMode,
      // payment_mode_note: row.note || row.paymentMode,
      // payment_date: formatDateToDMY(row.paymentDate),
      // cleared_date: formatDateToDMY(row.clearedDate),
      send_email_update_to_client: sendEmail,
      email_update_note: emailUpdateNote || 'Invoice has been marked as cancelled.',
      user_email: emailTo,
      cc: cc,
      bcc: bcc,
      subject: subject,
      message: emailBody,
      user_id: user?.id || "",
    };
    // console.log('Check for debugging canceled data send: ',params);
    // return;
    try {
      const res = await handleCancelInvoiceSave(params);
      if (res?.success) {
        await Swal.fire({
          icon: 'success',
          title: 'Cancelled',
          text: res.message || 'Invoice cancelled successfully.',
          timer: 2000,
          showConfirmButton: false,
        });
          // Auto close and refresh report
        if (modalData?.onClose) modalData.onClose();
        if (modalData?.fetchInvoices) modalData.fetchInvoices();
      } else {
        throw new Error(res?.message || 'Cancel failed.');
      }
    } catch (error) {
      Swal.fire('Error', error.message || 'Something went wrong.', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="modal-body text-center py-5">
        <div className="spinner-border text-primary" role="status"></div>
        <p className="mt-3">Loading invoice details...</p>
      </div>
    );
  }

  return (
    <div className="invoiceModal">
      {/* Email Review Pane */}
      <EmailToggleSection
        ref={sendEmailRef}
        sendEmail={sendEmail}
        setSendEmail={setSendEmail}
        emailTo={emailTo}
        setEmailTo={setEmailTo}
        cc={cc}
        setCc={setCc}
        bcc={bcc}
        setBcc={setBcc}
        subject={subject}
        setSubject={setSubject}
        emailBody={emailBody}
        setEmailBody={setEmailBody}
        emailUpdateNote={emailUpdateNote}
        setEmailUpdateNote={setEmailUpdateNote}
        onNoteValidationChange={setNoteValidationError}
        emailErrors={emailErrors}
        onEmailFieldChange={handleEmailFieldChange}
        validateAllEmailFields={validateAllEmailFields}
        validateEmailField={validateEmailField}
        clearEmailErrors={clearEmailErrors}
      />
      {/* Actions */}
      <div className="d-flex justify-content-center gap-3 mt-4">
        <button
          className="btn save-btn"
          onClick={handleSubmit}
          disabled={submitting}
        >
          {submitting ? (
            <div className="spinner-border spinner-border-sm text-light" role="status">
              <span className="visually-hidden">Processing...</span>
            </div>
          ) : (
            'Cancel Invoice'
          )}
        </button>
        <button className="btn cancel-btn" onClick={modalData.onClose}>
          Close
        </button>
      </div>

      {/* Validation Error Alert */}

      {formHasErrors && (
        <div className="alert alert-danger mt-3 sts" role="alert">
          <strong>
            <i className="fa fa-exclamation-circle me-2"></i> Please fix all validation errors.
          </strong>
        </div>
      )}
    </div>
  );
};

export default CancelledModalContent;