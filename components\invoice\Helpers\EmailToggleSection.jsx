// EmailToggleSection.jsx
import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useEffect,
} from "react";
import EmailReviewPane from "./EmailReviewPane";
import SendEmailToggle from "./SendEmailToggle";

const EmailToggleSection = forwardRef((props, ref) => {
  const sendEmailRef = useRef();

  const {
    sendEmail,
    setSendEmail,
    emailTo,
    setEmailTo,
    cc,
    setCc,
    bcc,
    setBcc,
    subject,
    setSubject,
    emailBody,
    setEmailBody,
    emailUpdateNote,
    setEmailUpdateNote,
    onNoteValidationChange,
    emailErrors,
    onEmailFieldChange,
    validateAllEmailFields,
    clearEmailErrors,
    validateEmailField,
  } = props;

  const handleEmailFieldChange = (field, value) => {
    if (validateEmailField) {
      validateEmailField(field, value);
    }
    if (onEmailFieldChange) {
      onEmailFieldChange(field, value);
    }
  };

  useImperativeHandle(ref, () => ({
    async triggerValidation() {
      // console.log("[EmailToggleSection] triggerValidation called. sendEmail =", sendEmail);
      if (sendEmail) {
        if (validateAllEmailFields) {
          const result = await validateAllEmailFields({to: emailTo, cc, bcc, subject});
          // console.log("[EmailToggleSection] validateAllEmailFields result:", result);
          return result;
        } else {
          // console.warn("[EmailToggleSection] validateAllEmailFields is not defined.");
          return false;
        }
      } else {
        if (sendEmailRef.current?.triggerValidation) {
          const result = await sendEmailRef.current.triggerValidation();
          // console.log("[EmailToggleSection] Note field validation result:", result);
          return result;
        } else {
          // console.warn(
          //   "[EmailToggleSection] Note validation ref not available."
          // );
          return false;
        }
      }
    },
    clearErrors() {
      clearEmailErrors?.();
    },
  }));

  useEffect(() => {
    if (sendEmail) {
      // Full validation when toggled ON
      validateAllEmailFields?.({
        to: emailTo,
        cc,
        bcc,
        subject,
      });
    } else {
      // Cleanup when toggled OFF
      clearEmailErrors?.();
    }
  }, [sendEmail]);

  return (
    <div className="email-toggle-section mt-4">
      <div className="form-check d-flex align-items-center mb-3">
        <input
          type="checkbox"
          className="form-check-input me-2"
          checked={sendEmail}
          onChange={(e) => {
            const isChecked = e.target.checked;
            setSendEmail(isChecked);
            if (!isChecked) clearEmailErrors();
          }}
        />
        <label className="form-check-label">Send Email Update to Client</label>
      </div>

      {sendEmail ? (
        <EmailReviewPane
          emailTo={emailTo}
          setEmailTo={setEmailTo}
          cc={cc}
          setCc={setCc}
          bcc={bcc}
          setBcc={setBcc}
          subject={subject}
          setSubject={setSubject}
          emailBody={emailBody}
          setEmailBody={setEmailBody}
          emailErrors={emailErrors}
          onEmailFieldChange={handleEmailFieldChange}
        />
      ) : (
        <SendEmailToggle
          ref={sendEmailRef}
          sendEmail={sendEmail}
          emailUpdateNote={emailUpdateNote}
          setEmailUpdateNote={setEmailUpdateNote}
          onValidationChange={onNoteValidationChange}
        />
      )}
    </div>
  );
});

export default EmailToggleSection;
