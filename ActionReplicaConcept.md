# Invoice Actions को Reuse करने का Complete Guide

## मौजूदा System की समझ (Current System Understanding)

### ProjectDetail.jsx में Invoice Tab की Current Structure:

1. **State Management:**
   - `invoices` - सभी invoices का data
   - `invoiceActions` - selected actions का state
   - `handleInvoiceActionChange` - action change handler

2. **Current Action Logic:**
   - Status के आधार पर actions show होते हैं
   - Status 2, 3, 6 के लिए कोई actions नहीं
   - Status 1, 5 के लिए: Paid, Void, Payment in process, Partially paid, Share link
   - Status 14 के लिए: केवल Partially paid

## Step 1: Invoice Actions को Advanced System में Convert करना

### File: `components/common/ProjectInvoiceActions.jsx` बनाना

```jsx
import React, { useState } from 'react';
import PropTypes from 'prop-types';

// Invoice actions mapping
const INVOICE_ACTIONS = {
  2: { text: 'Paid', requiresModal: true },
  3: { text: 'Void', requiresModal: true },
  6: { text: 'Payment in process', requiresModal: true },
  14: { text: 'Partially paid', requiresModal: true },
  'share_invoice_link': { text: 'Share Invoice link', requiresModal: false }
};

// Status-based action mapping
const STATUS_ACTION_MAP = {
  1: [2, 3, 6, 14, 'share_invoice_link'], // Unpaid
  5: [2, 3, 6, 14, 'share_invoice_link'], // Overdue
  14: [14] // Partially paid
};

const ProjectInvoiceActions = ({ 
  invoice, 
  selectedValue, 
  onActionChange, 
  disabled = false,
  className = ""
}) => {
  // Available actions for current invoice status
  const getAvailableActions = (status) => {
    return STATUS_ACTION_MAP[status] || [];
  };

  // Check if actions should be shown
  const shouldShowActions = (status) => {
    return ![2, 3, 6].includes(status);
  };

  const availableActions = getAvailableActions(invoice.status);
  const showActions = shouldShowActions(invoice.status);

  // Handle action selection
  const handleChange = (e) => {
    const value = e.target.value;
    if (value && onActionChange) {
      onActionChange(e, invoice.id, invoice);
    }
  };

  // If no actions should be shown
  if (!showActions) {
    return null;
  }

  return (
    <select 
      className={`react-select__control ${className}`}
      name="invoiceActions" 
      value={selectedValue || ''} 
      onChange={handleChange}
      disabled={disabled}
    >
      <option value="">Action</option>
      {availableActions.map((actionKey) => {
        const action = INVOICE_ACTIONS[actionKey];
        if (!action) return null;
        
        return (
          <option
            key={actionKey}
            value={actionKey}
            data-id={invoice.id}
            data-invoice-type={invoice.invoice_type}
            data-invoice-date={invoice.invoice_date}
            data-invoice-amount={invoice.total_amount}
            data-invoice-url={invoice.invoice_url}
          >
            {action.text}
          </option>
        );
      })}
    </select>
  );
};

ProjectInvoiceActions.propTypes = {
  invoice: PropTypes.object.isRequired,
  selectedValue: PropTypes.string,
  onActionChange: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  className: PropTypes.string
};

export default ProjectInvoiceActions;
```

## Step 2: InvoicesTab.jsx को Update करना

### Line 1 पर import add करें:
```jsx
import ProjectInvoiceActions from '../common/ProjectInvoiceActions';
```

### Line 67-136 के बीच existing select को replace करें:
```jsx
<div className="opp_edit_dlt_btn projects-iris">
  <ProjectInvoiceActions
    invoice={invoice}
    selectedValue={invoiceActions[invoice.id]}
    onActionChange={handleInvoiceActionChange}
    disabled={false}
    className="project-invoice-actions"
  />
</div>
```

## Step 3: ProjectDetail.jsx में Enhanced Handler बनाना

### Line 688 के बाद enhanced handler add करें:
```jsx
// Enhanced invoice action handler with modal support
const handleInvoiceActionChange = (e, invoiceId, invoiceData) => {
  const { value } = e.target;
  
  // Update state
  setInvoiceActions(prev => ({
    ...prev,
    [invoiceId]: value
  }));

  // Handle different action types
  handleInvoiceAction(value, invoiceId, invoiceData);
};

// Action processor function
const handleInvoiceAction = (actionType, invoiceId, invoiceData) => {
  console.log('Processing action:', {
    actionType,
    invoiceId,
    invoiceData
  });

  switch(actionType) {
    case '2': // Paid
      handlePaidAction(invoiceData);
      break;
    case '3': // Void
      handleVoidAction(invoiceData);
      break;
    case '6': // Payment in process
      handlePaymentProcessAction(invoiceData);
      break;
    case '14': // Partially paid
      handlePartiallyPaidAction(invoiceData);
      break;
    case 'share_invoice_link': // Share link
      handleShareLinkAction(invoiceData);
      break;
    default:
      console.log('Unknown action type:', actionType);
  }
};

// Individual action handlers
const handlePaidAction = (invoiceData) => {
  console.log('Handling paid action for invoice:', invoiceData.id);
  // यहाँ paid action का logic होगा
  // Modal open करना या API call करना
};

const handleVoidAction = (invoiceData) => {
  console.log('Handling void action for invoice:', invoiceData.id);
  // यहाँ void action का logic होगा
};

const handlePaymentProcessAction = (invoiceData) => {
  console.log('Handling payment process action for invoice:', invoiceData.id);
  // यहाँ payment process action का logic होगा
};

const handlePartiallyPaidAction = (invoiceData) => {
  console.log('Handling partially paid action for invoice:', invoiceData.id);
  // यहाँ partially paid action का logic होगा
};

const handleShareLinkAction = (invoiceData) => {
  console.log('Handling share link action for invoice:', invoiceData.id);
  // यहाँ share link action का logic होगा
  // Direct action without modal
  if (invoiceData.invoice_url) {
    navigator.clipboard.writeText(invoiceData.invoice_url);
    alert('Invoice link copied to clipboard!');
  }
};
```

## Step 4: Modal Integration के लिए Setup

### Line 25 के बाद Modal import add करें:
```jsx
import Modal from './common/Modal';
```

### Modal state add करें (Line 679 के बाद):
```jsx
// Modal states
const [showInvoiceModal, setShowInvoiceModal] = useState(false);
const [modalData, setModalData] = useState(null);
const [modalType, setModalType] = useState('');
```

### Enhanced action handlers with modal support:
```jsx
// Updated action handlers with modal integration
const handlePaidAction = (invoiceData) => {
  setModalType('paid');
  setModalData({
    title: `Mark as Paid - Invoice #${invoiceData.customer_invoice_no}`,
    invoice: invoiceData,
    actionType: 'paid'
  });
  setShowInvoiceModal(true);
};

const handleVoidAction = (invoiceData) => {
  setModalType('void');
  setModalData({
    title: `Void Invoice - Invoice #${invoiceData.customer_invoice_no}`,
    invoice: invoiceData,
    actionType: 'void'
  });
  setShowInvoiceModal(true);
};

const handlePaymentProcessAction = (invoiceData) => {
  setModalType('payment_process');
  setModalData({
    title: `Payment in Process - Invoice #${invoiceData.customer_invoice_no}`,
    invoice: invoiceData,
    actionType: 'payment_process'
  });
  setShowInvoiceModal(true);
};

const handlePartiallyPaidAction = (invoiceData) => {
  setModalType('partially_paid');
  setModalData({
    title: `Partially Paid - Invoice #${invoiceData.customer_invoice_no}`,
    invoice: invoiceData,
    actionType: 'partially_paid'
  });
  setShowInvoiceModal(true);
};

// Modal close handler
const handleModalClose = () => {
  setShowInvoiceModal(false);
  setModalData(null);
  setModalType('');

  // Reset action selection
  if (modalData?.invoice?.id) {
    setInvoiceActions(prev => ({
      ...prev,
      [modalData.invoice.id]: ''
    }));
  }
};

// Modal success handler
const handleModalSuccess = (result) => {
  console.log('Action completed successfully:', result);

  // Refresh invoice data
  fetchInvoiceData();

  // Close modal
  handleModalClose();

  // Show success message
  alert('Action completed successfully!');
};
```

## Step 5: Modal Component बनाना

### File: `components/common/ProjectInvoiceModal.jsx`

```jsx
import React, { useState } from 'react';
import PropTypes from 'prop-types';

const ProjectInvoiceModal = ({
  isOpen,
  onClose,
  modalData,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({});

  if (!isOpen || !modalData) return null;

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // यहाँ API call होगी
      const result = await processInvoiceAction(
        modalData.actionType,
        modalData.invoice.id,
        formData
      );

      if (onSuccess) {
        onSuccess(result);
      }
    } catch (error) {
      console.error('Error processing action:', error);
      alert('Error processing action: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Render different content based on action type
  const renderModalContent = () => {
    switch(modalData.actionType) {
      case 'paid':
        return (
          <div>
            <h5>Mark Invoice as Paid</h5>
            <form onSubmit={handleSubmit}>
              <div className="mb-3">
                <label className="form-label">Payment Date</label>
                <input
                  type="date"
                  className="form-control"
                  value={formData.paymentDate || ''}
                  onChange={(e) => setFormData({...formData, paymentDate: e.target.value})}
                  required
                />
              </div>
              <div className="mb-3">
                <label className="form-label">Payment Mode</label>
                <select
                  className="form-control"
                  value={formData.paymentMode || ''}
                  onChange={(e) => setFormData({...formData, paymentMode: e.target.value})}
                  required
                >
                  <option value="">Select Payment Mode</option>
                  <option value="cash">Cash</option>
                  <option value="check">Check</option>
                  <option value="credit_card">Credit Card</option>
                  <option value="bank_transfer">Bank Transfer</option>
                </select>
              </div>
              <div className="mb-3">
                <label className="form-label">Notes</label>
                <textarea
                  className="form-control"
                  value={formData.notes || ''}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                  rows="3"
                />
              </div>
              <div className="d-flex justify-content-end">
                <button type="button" className="btn btn-secondary me-2" onClick={onClose}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary" disabled={loading}>
                  {loading ? 'Processing...' : 'Mark as Paid'}
                </button>
              </div>
            </form>
          </div>
        );

      case 'void':
        return (
          <div>
            <h5>Void Invoice</h5>
            <p>Are you sure you want to void this invoice?</p>
            <p><strong>Invoice:</strong> {modalData.invoice.customer_invoice_no}</p>
            <p><strong>Amount:</strong> ${modalData.invoice.total_amount}</p>
            <form onSubmit={handleSubmit}>
              <div className="mb-3">
                <label className="form-label">Reason for Voiding</label>
                <textarea
                  className="form-control"
                  value={formData.reason || ''}
                  onChange={(e) => setFormData({...formData, reason: e.target.value})}
                  rows="3"
                  required
                />
              </div>
              <div className="d-flex justify-content-end">
                <button type="button" className="btn btn-secondary me-2" onClick={onClose}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-danger" disabled={loading}>
                  {loading ? 'Processing...' : 'Void Invoice'}
                </button>
              </div>
            </form>
          </div>
        );

      case 'payment_process':
        return (
          <div>
            <h5>Mark Payment in Process</h5>
            <form onSubmit={handleSubmit}>
              <div className="mb-3">
                <label className="form-label">Expected Payment Date</label>
                <input
                  type="date"
                  className="form-control"
                  value={formData.expectedDate || ''}
                  onChange={(e) => setFormData({...formData, expectedDate: e.target.value})}
                />
              </div>
              <div className="mb-3">
                <label className="form-label">Notes</label>
                <textarea
                  className="form-control"
                  value={formData.notes || ''}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                  rows="3"
                />
              </div>
              <div className="d-flex justify-content-end">
                <button type="button" className="btn btn-secondary me-2" onClick={onClose}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary" disabled={loading}>
                  {loading ? 'Processing...' : 'Update Status'}
                </button>
              </div>
            </form>
          </div>
        );

      case 'partially_paid':
        return (
          <div>
            <h5>Record Partial Payment</h5>
            <form onSubmit={handleSubmit}>
              <div className="mb-3">
                <label className="form-label">Payment Amount</label>
                <input
                  type="number"
                  step="0.01"
                  className="form-control"
                  value={formData.amount || ''}
                  onChange={(e) => setFormData({...formData, amount: e.target.value})}
                  max={modalData.invoice.total_amount}
                  required
                />
                <small className="text-muted">
                  Total Invoice Amount: ${modalData.invoice.total_amount}
                </small>
              </div>
              <div className="mb-3">
                <label className="form-label">Payment Date</label>
                <input
                  type="date"
                  className="form-control"
                  value={formData.paymentDate || ''}
                  onChange={(e) => setFormData({...formData, paymentDate: e.target.value})}
                  required
                />
              </div>
              <div className="mb-3">
                <label className="form-label">Payment Mode</label>
                <select
                  className="form-control"
                  value={formData.paymentMode || ''}
                  onChange={(e) => setFormData({...formData, paymentMode: e.target.value})}
                  required
                >
                  <option value="">Select Payment Mode</option>
                  <option value="cash">Cash</option>
                  <option value="check">Check</option>
                  <option value="credit_card">Credit Card</option>
                  <option value="bank_transfer">Bank Transfer</option>
                </select>
              </div>
              <div className="mb-3">
                <label className="form-label">Notes</label>
                <textarea
                  className="form-control"
                  value={formData.notes || ''}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                  rows="3"
                />
              </div>
              <div className="d-flex justify-content-end">
                <button type="button" className="btn btn-secondary me-2" onClick={onClose}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary" disabled={loading}>
                  {loading ? 'Processing...' : 'Record Payment'}
                </button>
              </div>
            </form>
          </div>
        );

      default:
        return <div>Unknown action type</div>;
    }
  };

  return (
    <div className="modal fade show" style={{display: 'block'}} tabIndex="-1">
      <div className="modal-dialog modal-lg">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">{modalData.title}</h5>
            <button type="button" className="btn-close" onClick={onClose}></button>
          </div>
          <div className="modal-body">
            {renderModalContent()}
          </div>
        </div>
      </div>
      <div className="modal-backdrop fade show"></div>
    </div>
  );
};

// API function for processing actions
const processInvoiceAction = async (actionType, invoiceId, formData) => {
  // यहाँ actual API calls होंगी
  console.log('Processing action:', { actionType, invoiceId, formData });

  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ success: true, message: 'Action completed successfully' });
    }, 1000);
  });
};

ProjectInvoiceModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  modalData: PropTypes.object,
  onSuccess: PropTypes.func
};

export default ProjectInvoiceModal;
```

## Step 6: ProjectDetail.jsx में Modal Integration

### Line 25 के बाद Modal import add करें:
```jsx
import ProjectInvoiceModal from './common/ProjectInvoiceModal';
```

### Line 5192 के बाद Modal component add करें:
```jsx
{/* Invoice Action Modal */}
{showInvoiceModal && (
  <ProjectInvoiceModal
    isOpen={showInvoiceModal}
    onClose={handleModalClose}
    modalData={modalData}
    onSuccess={handleModalSuccess}
  />
)}
```

## Step 7: CSS Styling Add करना

### File: `components/common/ProjectInvoiceActions.css` बनाना

```css
/* Project Invoice Actions Styling */
.project-invoice-actions {
  min-width: 150px;
  font-size: 0.9rem;
  height: 38px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: #fff;
  color: #495057;
  transition: border-color 0.15s ease-in-out;
}

.project-invoice-actions:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.project-invoice-actions:hover {
  border-color: #adb5bd;
}

.project-invoice-actions:disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Modal styling enhancements */
.modal-content {
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-radius: 8px 8px 0 0;
}

.modal-body {
  padding: 2rem;
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-control:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Button styling */
.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-danger:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

/* Responsive design */
@media (max-width: 768px) {
  .project-invoice-actions {
    min-width: 120px;
    font-size: 0.8rem;
  }

  .modal-dialog {
    margin: 1rem;
  }

  .modal-body {
    padding: 1rem;
  }
}
```

## Step 8: Testing और Debugging

### Testing Checklist:

1. **Basic Functionality Test:**
   - Different invoice statuses के साथ test करें
   - Action dropdown properly show हो रहा है या नहीं
   - Modal open/close properly काम कर रहा है या नहीं

2. **Action Processing Test:**
   - हर action type के लिए modal content check करें
   - Form validation properly काम कर रहा है या नहीं
   - API calls properly trigger हो रही हैं या नहीं

3. **Error Handling Test:**
   - Network errors handle हो रहे हैं या नहीं
   - Form validation errors show हो रहे हैं या नहीं
   - Loading states properly work कर रहे हैं या नहीं

### Debug करने के लिए Console Logs:

```jsx
// ProjectInvoiceActions.jsx में debug logs
console.log('Invoice status:', invoice.status);
console.log('Available actions:', availableActions);
console.log('Should show actions:', showActions);

// ProjectDetail.jsx में debug logs
console.log('Action selected:', { actionType, invoiceId, invoiceData });
console.log('Modal data:', modalData);
console.log('Modal type:', modalType);
```

## Step 9: Production Ready बनाना

### Error Handling Enhancement:

```jsx
// Enhanced error handling in ProjectDetail.jsx
const handleInvoiceAction = async (actionType, invoiceId, invoiceData) => {
  try {
    console.log('Processing action:', { actionType, invoiceId, invoiceData });

    switch(actionType) {
      case '2':
        await handlePaidAction(invoiceData);
        break;
      case '3':
        await handleVoidAction(invoiceData);
        break;
      case '6':
        await handlePaymentProcessAction(invoiceData);
        break;
      case '14':
        await handlePartiallyPaidAction(invoiceData);
        break;
      case 'share_invoice_link':
        await handleShareLinkAction(invoiceData);
        break;
      default:
        throw new Error(`Unknown action type: ${actionType}`);
    }
  } catch (error) {
    console.error('Error processing invoice action:', error);
    alert('Error processing action: ' + error.message);

    // Reset action selection on error
    setInvoiceActions(prev => ({
      ...prev,
      [invoiceId]: ''
    }));
  }
};
```

### Performance Optimization:

```jsx
// Memoize action component
import React, { memo } from 'react';

const ProjectInvoiceActions = memo(({ invoice, selectedValue, onActionChange, disabled, className }) => {
  // Component logic...
});

// Memoize available actions calculation
const availableActions = useMemo(() => {
  return getAvailableActions(invoice.status);
}, [invoice.status]);
```

## Step 10: Final Integration Steps

### 1. Import CSS in ProjectDetail.jsx:
```jsx
import './common/ProjectInvoiceActions.css';
```

### 2. Update InvoicesTab.jsx import:
```jsx
import ProjectInvoiceActions from '../common/ProjectInvoiceActions';
import '../common/ProjectInvoiceActions.css';
```

### 3. Test Complete Flow:
1. ProjectDetail.jsx load करें
2. Invoices tab पर click करें
3. Different status वाले invoices के साथ actions test करें
4. Modal functionality test करें
5. Form submissions test करें

## Summary (सारांश)

इस guide में हमने:

1. **Current System को समझा** - ProjectDetail.jsx में invoice actions कैसे काम करते हैं
2. **Reusable Component बनाया** - ProjectInvoiceActions component
3. **Modal System बनाया** - ProjectInvoiceModal component
4. **Enhanced Handlers बनाए** - Better action processing
5. **Styling Add की** - Professional CSS styling
6. **Testing Strategy बनाई** - Complete testing approach
7. **Production Ready बनाया** - Error handling और optimization

अब आप इस system को कहीं भी reuse कर सकते हैं और same functionality मिलेगी जो InvoiceReport.jsx में है।

**Next Steps:**
1. सभी files create करें
2. Code को step by step implement करें
3. Testing करें
4. Production में deploy करें

**Happy Coding! 🚀**
