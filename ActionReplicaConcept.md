# Existing Invoice Components को Reuse करने का Complete Guide

## मौजूदा Invoice System की समझ (Understanding Existing Invoice System)

### Invoice Folder में Available Components:

1. **InvoiceReport.jsx** - Main report with ActionDropdown component
2. **invoice-settings.js** - सभी constants, ACTION_IDS, ACTIONS_MAP
3. **invoiceActionHandlers.js** - API handlers for all actions
4. **ModalContents.jsx** - Modal content router
5. **Modals/** - सभी individual modal components
6. **invoiceSwalActions.js** - SweetAlert actions

### Current ProjectDetail.jsx में Simple Actions:
- Status-based hardcoded options
- No modal integration
- Limited functionality

## Step 1: Existing Invoice Components को Import करना

### ProjectDetail.jsx में Required Imports Add करें (Line 25 के बाद):

```jsx
// Invoice system imports
import {
  ACTION_IDS,
  ACTIONS_MAP,
  getCurrentUserInvoice,
  SPECIAL_USER_IDS,
  ALLOWED_ROLES_FOR_SHARE_LINK
} from './invoice/invoice-settings';
import Modal from './common/Modal';
import ModalContent from './invoice/ModalContents';
import './invoice/invoice.css';
```

## Step 2: ActionDropdown Component को Extract करना

### File: `components/common/ReusableActionDropdown.jsx` बनाना

```jsx
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  ACTION_IDS,
  ACTIONS_MAP,
  SPECIAL_USER_IDS,
  ALLOWED_ROLES_FOR_SHARE_LINK
} from '../invoice/invoice-settings';

const ReusableActionDropdown = ({
  invoice,
  currentUserId,
  userRoles = [],
  actionSelections = {},
  onActionChange,
  disabled = false,
  className = ""
}) => {
  // Parse invoice actions (same logic as InvoiceReport.jsx)
  const parseInvoiceActions = (inv) => {
    if (!inv || !inv.action) return [];
    return inv.action.split(",").map((a) => a.trim()).filter(Boolean);
  };

  // Get available actions with filtering
  const getFilteredActions = (inv) => {
    const actionList = parseInvoiceActions(inv);

    return actionList.filter(action => {
      // Hide delete action for non-authorized users
      if (action === String(ACTION_IDS.DELETE) &&
          currentUserId !== SPECIAL_USER_IDS.CAN_SEE_DELETE) {
        return false;
      }

      // Hide share invoice link for unauthorized roles
      if (action === String(ACTION_IDS.SHARE_INVOICE_LINK) &&
          !userRoles.some(role => ALLOWED_ROLES_FOR_SHARE_LINK.includes(role))) {
        return false;
      }

      return true;
    });
  };

  const availableActions = getFilteredActions(invoice);
  const hasActions = availableActions.length > 0;

  // Handle action change
  const handleChange = (e) => {
    const value = e.target.value;
    if (value !== "Select" && onActionChange) {
      onActionChange(invoice.invoice_id || invoice.id, value, invoice);
    }
  };

  // If no actions available
  if (!hasActions) {
    return (
      <select className="form-select action-disabled-invoice" disabled>
        <option>No Action</option>
      </select>
    );
  }

  return (
    <select
      className={`form-select ${className}`}
      value={actionSelections[invoice.invoice_id || invoice.id] || "Select"}
      onChange={handleChange}
      disabled={disabled}
    >
      <option value="Select">Select Action</option>
      {availableActions.map((action, idx) => {
        const actionText = ACTIONS_MAP[action]?.text || `Action ${action}`;
        return (
          <option key={`${action}-${idx}`} value={action}>
            {actionText}
          </option>
        );
      })}
    </select>
  );
};

ReusableActionDropdown.propTypes = {
  invoice: PropTypes.object.isRequired,
  currentUserId: PropTypes.number,
  userRoles: PropTypes.array,
  actionSelections: PropTypes.object,
  onActionChange: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  className: PropTypes.string
};

export default ReusableActionDropdown;
```

## Step 3: InvoicesTab.jsx को Update करना

### Line 1 पर import add करें:
```jsx
import ReusableActionDropdown from '../common/ReusableActionDropdown';
import { getCurrentUserInvoice } from '../invoice/invoice-settings';
```

### Line 67-136 के बीच existing select को replace करें:
```jsx
<div className="opp_edit_dlt_btn projects-iris">
  <ReusableActionDropdown
    invoice={invoice}
    currentUserId={getCurrentUserInvoice()?.id}
    userRoles={getCurrentUserInvoice()?.roles || []}
    actionSelections={invoiceActions}
    onActionChange={handleInvoiceActionChange}
    disabled={false}
    className="project-invoice-actions"
  />
</div>
```

## Step 4: ProjectDetail.jsx में Existing Invoice System Integration

### Line 679 के बाद Modal states add करें:
```jsx
// Modal states for invoice actions (same as InvoiceReport.jsx)
const [showModal, setShowModal] = useState(false);
const [modalData, setModalData] = useState(null);
const [actionSelections, setActionSelections] = useState({});
```

### Line 688 को replace करें - Enhanced handler with existing invoice logic:
```jsx
// Enhanced invoice action handler (InvoiceReport.jsx से inspired)
const handleInvoiceActionChange = (invoiceId, newValue, inv) => {
  if (newValue === "Select") return;

  // Update action selections
  setActionSelections(prev => ({
    ...prev,
    [invoiceId]: newValue
  }));

  // Create modal payload (same structure as InvoiceReport.jsx)
  const modalPayload = {
    title: `${ACTIONS_MAP[newValue]?.text || "Action"} - Invoice #${inv?.customer_invoice_no}`,
    actionType: newValue || "",
    invoiceId: invoiceId || "",
    customerInvoiceNo: inv?.customer_invoice_no || "N/A",
    invoiceDate: inv?.invoice_date || "N/A",
    invoiceAmount: isNaN(parseFloat(inv?.total_amount))
      ? "0.00"
      : parseFloat(inv.total_amount).toFixed(2),
    invoiceUrl: inv?.invoice_url || "",
    businessName: inv?.business_name || "N/A",
    customerName: inv?.customer_name || "N/A",
    userEmail: inv?.user_email || "",
    statusId: inv?.status_id || inv?.status || "",
    productTitle: inv?.product_title || inv?.product_names || "N/A",
    dueDate: inv?.due_date || "N/A",
    daysDue: typeof inv?.days_due === "number" || !isNaN(inv?.days_due)
      ? parseInt(inv.days_due, 10)
      : "N/A",
    resetActionSelection: () => {
      setActionSelections(prev => ({
        ...prev,
        [invoiceId]: "Select"
      }));
    }
  };

  // Set modal data and show modal
  setModalData(modalPayload);
  setShowModal(true);
};

// Modal close handler
const handleModalClose = () => {
  setShowModal(false);
  setModalData(null);

  // Reset action selection
  if (modalData?.invoiceId) {
    setActionSelections(prev => ({
      ...prev,
      [modalData.invoiceId]: "Select"
    }));
  }
};

// Modal success handler
const handleModalSuccess = (result) => {
  console.log('Invoice action completed:', result);

  // Refresh invoice data
  fetchInvoiceData();

  // Close modal
  handleModalClose();

  // Show success message (optional)
  // Swal.fire('Success!', 'Action completed successfully', 'success');
};
```

## Step 5: Invoice Data Format को Adjust करना

### fetchInvoiceData function को update करें (Line 1596):
```jsx
// Modified fetchInvoiceData function
const fetchInvoiceData = async () => {
  setLoading(true);
  setError('');

  try {
    const response = await axios.post(
      "https://portal.occamsadvisory.com/portal/wp-json/productsplugin/v1/get-project-invoices",
      { project_id: projectId }
    );

    if (response.data.status == 200) {
      setIsInvoiceData(true);

      // Transform invoice data to match InvoiceReport format
      const transformedInvoices = (response.data.data || []).map(invoice => ({
        ...invoice,
        // Add action field based on status (same logic as InvoiceReport)
        action: getInvoiceActions(invoice.status),
        // Ensure required fields exist
        invoice_id: invoice.id,
        customer_invoice_no: invoice.customer_invoice_no || `ERC-${invoice.id}`,
        total_amount: invoice.total_amount || '0.00',
        business_name: invoice.business_name || 'N/A',
        customer_name: invoice.customer_name || 'N/A',
        user_email: invoice.user_email || '',
        product_title: invoice.product_names || 'N/A'
      }));

      setInvoices(transformedInvoices);
      console.log('Transformed invoices:', transformedInvoices);
    } else {
      setError(response.data.message || 'Failed to fetch invoices');
    }
  } catch (err) {
    console.error('Error fetching invoice data:', err);
  } finally {
    setLoading(false);
  }
};

// Helper function to get actions based on status
const getInvoiceActions = (status) => {
  switch(status) {
    case 1: // Unpaid
    case 5: // Overdue
      return "2,3,6,14,share_invoice_link"; // Paid, Void, Payment in process, Partially paid, Share link
    case 14: // Partially paid
      return "14"; // Only Partially paid
    case 2: // Paid
    case 3: // Void
    case 6: // Payment in process
    default:
      return ""; // No actions
  }
};
```

## Step 6: Modal Integration with Existing Invoice Components

### Line 5192 के बाद Modal component add करें:
```jsx
{/* Invoice Action Modal - Using existing ModalContent */}
{showModal && modalData && (
  <Modal
    isOpen={showModal}
    onClose={handleModalClose}
    title={modalData.title}
    size="lg"
  >
    <ModalContent
      modalData={modalData}
      actionsMap={ACTIONS_MAP}
    />
  </Modal>
)}
```

## Step 7: Complete Implementation Steps

### 1. Create ReusableActionDropdown component:
```bash
# File path: components/common/ReusableActionDropdown.jsx
# (Code already provided in Step 2)
```

### 2. Update ProjectDetail.jsx imports (Line 25 के बाद):
```jsx
// Invoice system imports
import {
  ACTION_IDS,
  ACTIONS_MAP,
  getCurrentUserInvoice,
  SPECIAL_USER_IDS,
  ALLOWED_ROLES_FOR_SHARE_LINK
} from './invoice/invoice-settings';
import Modal from './common/Modal';
import ModalContent from './invoice/ModalContents';
import './invoice/invoice.css';
import ReusableActionDropdown from './common/ReusableActionDropdown';
```

### 3. Update InvoicesTab.jsx (Line 1 पर):
```jsx
import React from 'react';
import ReusableActionDropdown from '../common/ReusableActionDropdown';
import { getCurrentUserInvoice } from '../invoice/invoice-settings';
```

### 4. Replace action dropdown in InvoicesTab.jsx (Line 67-136):
```jsx
<div className="opp_edit_dlt_btn projects-iris">
  <ReusableActionDropdown
    invoice={invoice}
    currentUserId={getCurrentUserInvoice()?.id}
    userRoles={getCurrentUserInvoice()?.roles || []}
    actionSelections={invoiceActions}
    onActionChange={handleInvoiceActionChange}
    disabled={false}
    className="project-invoice-actions"
  />
</div>
```

## Step 8: Testing और Verification

### Testing Checklist:

1. **Component Integration Test:**
```jsx
// Console में check करें:
console.log('Current user:', getCurrentUserInvoice());
console.log('Invoice actions:', invoice.action);
console.log('Available actions:', getFilteredActions(invoice));
```

2. **Modal Integration Test:**
```jsx
// Modal data structure check:
console.log('Modal data:', modalData);
console.log('Action type:', modalData.actionType);
console.log('Invoice data:', modalData.invoiceId);
```

3. **Action Processing Test:**
- Different invoice statuses के साथ test करें
- Modal open/close functionality test करें
- Form submissions test करें (if applicable)

### Debug Steps:

1. **Check Invoice Data Format:**
```jsx
// fetchInvoiceData में add करें:
console.log('Original invoice data:', response.data.data);
console.log('Transformed invoice data:', transformedInvoices);
```

2. **Check Action Dropdown:**
```jsx
// ReusableActionDropdown में add करें:
console.log('Invoice status:', invoice.status);
console.log('Parsed actions:', parseInvoiceActions(invoice));
console.log('Filtered actions:', getFilteredActions(invoice));
```

3. **Check Modal Integration:**
```jsx
// handleInvoiceActionChange में add करें:
console.log('Action selected:', { invoiceId, newValue, inv });
console.log('Modal payload:', modalPayload);
```

## Step 9: Production Ready Implementation

### Final File Structure:
```
components/
├── common/
│   └── ReusableActionDropdown.jsx (New file)
├── invoice/ (Existing folder - reuse करेंगे)
│   ├── invoice-settings.js
│   ├── ModalContents.jsx
│   ├── Modals/ (All modal components)
│   ├── invoiceActionHandlers.js
│   └── invoice.css
├── tabs/
│   └── InvoicesTab.jsx (Updated)
└── ProjectDetail.jsx (Updated)
```

### Key Benefits of Reusing Existing Invoice Components:

1. **No Duplicate Code** - Existing modal components reuse
2. **Consistent UI/UX** - Same look and feel as InvoiceReport
3. **Proven Functionality** - Already tested and working
4. **Easy Maintenance** - Single source of truth
5. **All Features Available** - Complete action functionality

### Production Checklist:

1. **✅ Component Creation:**
   - ReusableActionDropdown.jsx created
   - Existing invoice components imported

2. **✅ Integration:**
   - ProjectDetail.jsx updated with imports
   - InvoicesTab.jsx updated with new component
   - Modal system integrated

3. **✅ Data Transformation:**
   - Invoice data format adjusted
   - Action field added based on status

4. **✅ Testing:**
   - Debug logs added
   - Testing steps provided

### Error Handling:

```jsx
// Add to ReusableActionDropdown.jsx
const ReusableActionDropdown = ({ invoice, ...props }) => {
  try {
    // Component logic
    const availableActions = getFilteredActions(invoice);
    // ... rest of component
  } catch (error) {
    console.error('Error in ReusableActionDropdown:', error);
    return (
      <select className="form-select action-disabled-invoice" disabled>
        <option>Error Loading Actions</option>
      </select>
    );
  }
};
```

## Step 8: Testing और Debugging

### Testing Checklist:

1. **Basic Functionality Test:**
   - Different invoice statuses के साथ test करें
   - Action dropdown properly show हो रहा है या नहीं
   - Modal open/close properly काम कर रहा है या नहीं

2. **Action Processing Test:**
   - हर action type के लिए modal content check करें
   - Form validation properly काम कर रहा है या नहीं
   - API calls properly trigger हो रही हैं या नहीं

3. **Error Handling Test:**
   - Network errors handle हो रहे हैं या नहीं
   - Form validation errors show हो रहे हैं या नहीं
   - Loading states properly work कर रहे हैं या नहीं

### Debug करने के लिए Console Logs:

```jsx
// ProjectInvoiceActions.jsx में debug logs
console.log('Invoice status:', invoice.status);
console.log('Available actions:', availableActions);
console.log('Should show actions:', showActions);

// ProjectDetail.jsx में debug logs
console.log('Action selected:', { actionType, invoiceId, invoiceData });
console.log('Modal data:', modalData);
console.log('Modal type:', modalType);
```

## Step 9: Production Ready बनाना

### Error Handling Enhancement:

```jsx
// Enhanced error handling in ProjectDetail.jsx
const handleInvoiceAction = async (actionType, invoiceId, invoiceData) => {
  try {
    console.log('Processing action:', { actionType, invoiceId, invoiceData });

    switch(actionType) {
      case '2':
        await handlePaidAction(invoiceData);
        break;
      case '3':
        await handleVoidAction(invoiceData);
        break;
      case '6':
        await handlePaymentProcessAction(invoiceData);
        break;
      case '14':
        await handlePartiallyPaidAction(invoiceData);
        break;
      case 'share_invoice_link':
        await handleShareLinkAction(invoiceData);
        break;
      default:
        throw new Error(`Unknown action type: ${actionType}`);
    }
  } catch (error) {
    console.error('Error processing invoice action:', error);
    alert('Error processing action: ' + error.message);

    // Reset action selection on error
    setInvoiceActions(prev => ({
      ...prev,
      [invoiceId]: ''
    }));
  }
};
```

### Performance Optimization:

```jsx
// Memoize action component
import React, { memo } from 'react';

const ProjectInvoiceActions = memo(({ invoice, selectedValue, onActionChange, disabled, className }) => {
  // Component logic...
});

// Memoize available actions calculation
const availableActions = useMemo(() => {
  return getAvailableActions(invoice.status);
}, [invoice.status]);
```

## Step 10: Final Integration Steps

### 1. Import CSS in ProjectDetail.jsx:
```jsx
import './common/ProjectInvoiceActions.css';
```

### 2. Update InvoicesTab.jsx import:
```jsx
import ProjectInvoiceActions from '../common/ProjectInvoiceActions';
import '../common/ProjectInvoiceActions.css';
```

### 3. Test Complete Flow:
1. ProjectDetail.jsx load करें
2. Invoices tab पर click करें
3. Different status वाले invoices के साथ actions test करें
4. Modal functionality test करें
5. Form submissions test करें

## Summary (सारांश)

इस guide में हमने **Existing Invoice Components को Reuse** करके ProjectDetail.jsx में advanced invoice actions implement किए:

### 🎯 **Key Achievements:**

1. **Existing Components Reused:**
   - `invoice-settings.js` - सभी constants और mappings
   - `ModalContents.jsx` - Complete modal system
   - `Modals/` folder - सभी individual modals
   - `invoice.css` - Professional styling

2. **New Component Created:**
   - `ReusableActionDropdown.jsx` - Reusable action dropdown

3. **Enhanced Integration:**
   - ProjectDetail.jsx में complete invoice system
   - InvoicesTab.jsx में advanced actions
   - Modal system with existing components

4. **Data Transformation:**
   - Invoice data को InvoiceReport format में convert
   - Status-based action mapping
   - Proper field mapping

### 🚀 **Benefits:**

- **No Code Duplication** - Existing components reuse
- **Consistent Experience** - Same UI/UX as InvoiceReport
- **Full Functionality** - सभी actions available (Paid, Void, Partial, etc.)
- **Easy Maintenance** - Single source of truth
- **Production Ready** - Tested और proven components

### 📋 **Implementation Steps:**

1. ✅ ReusableActionDropdown.jsx create करें
2. ✅ ProjectDetail.jsx में imports add करें
3. ✅ InvoicesTab.jsx update करें
4. ✅ fetchInvoiceData function modify करें
5. ✅ Modal integration complete करें
6. ✅ Testing और debugging करें

### 🔧 **Next Steps:**

1. **Code Implementation:** सभी provided code को files में add करें
2. **Testing:** Different invoice statuses के साथ test करें
3. **Debugging:** Console logs check करें
4. **Production:** Error handling verify करें

अब आपके पास **InvoiceReport.jsx की complete functionality** ProjectDetail.jsx में available है, बिना कोई duplicate code के!

**Happy Coding! 🚀**
