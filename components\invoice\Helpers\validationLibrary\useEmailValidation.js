import { useState, useCallback } from "react";
import { emailSectionSchema } from "./FieldValidators";

export const useEmailValidation = () => {
  const [emailErrors, setEmailErrors] = useState({});

  // Individual field validation (runtime)
  const validateEmailField = useCallback(async (field, value) => {
    try {
      await emailSectionSchema.validateAt(field, { [field]: value });
      setEmailErrors((prev) => ({ ...prev, [field]: "" }));
      // console.log(`Email field '${field}' validation passed:`, value);
      return true;
    } catch (err) {
      setEmailErrors((prev) => ({ ...prev, [field]: err.message }));
      // console.log(`Email field '${field}' validation failed:`, value, err.message);
      return false;
    }
  }, []);

  // Full validation (on submit or triggerValidation)
  const validateAllEmailFields = useCallback(async (emailData) => {
    const { to, cc, bcc, subject } = emailData;
    let valid = true;
    const newErrors = {};

    // console.log(" Validating email data:", emailData);

    try {
      await emailSectionSchema.validate(
        { to, cc, bcc, subject },
        { abortEarly: false }
      );
      // console.log("All email fields validation passed");
    } catch (err) {
      valid = false;
      err.inner.forEach((e) => {
        newErrors[e.path] = e.message;
      });
      // console.log("Email validation failed:", newErrors);
    }

    setEmailErrors((prev) => ({ ...prev, ...newErrors }));
    // console.log("Email Validation Errors:", newErrors); // Logs validation issues

    return valid;
  }, []);

  // Reset all email errors
  const clearEmailErrors = useCallback(() => {
    // console.log(" Clearing email errors");
    setEmailErrors({});
  }, []);

  return {
    emailErrors,
    validateEmailField,
    validateAllEmailFields,
    clearEmailErrors,
  };
};
