// Helpers/validationLibrary/useEmailValidation.js
import { useState } from "react";
import * as yup from "yup";

const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

export const emailSectionSchema = yup.object().shape({
  to: yup
    .string()
    .required("Recipient email (To) is required")
    .test("valid-emails", "Enter a valid email address", (value) => {
      if (!value) return false;
      const emails = value.split(",").map(e => e.trim()).filter(e => e);
      return emails.length > 0 && emails.every(email => emailRegex.test(email));
    }),
  cc: yup
    .string()
    .test("valid-emails", "Enter a valid email address", (value) => {
      if (!value) return true;
      const emails = value.split(",").map(e => e.trim()).filter(e => e);
      return emails.every(email => emailRegex.test(email));
    }),
  bcc: yup
    .string()
    .test("valid-emails", "Enter a valid email address", (value) => {
      if (!value) return true;
      const emails = value.split(",").map(e => e.trim()).filter(e => e);
      return emails.every(email => emailRegex.test(email));
    }),
  subject: yup
    .string()
    .required("Email subject is required")
    .max(150, "Subject should be under 150 characters"),
});

export const useEmailValidation = () => {
  const [emailErrors, setEmailErrors] = useState({ to: "", cc: "", bcc: "", subject: "" });

  const validateEmailField = async (name, value) => {
    try {
      await emailSectionSchema.validateAt(name, { [name]: value });
      setEmailErrors(prev => ({ ...prev, [name]: "" }));
    } catch (err) {
      setEmailErrors(prev => ({ ...prev, [name]: err.message }));
    }
  };

  const validateAllEmailFields = async (values) => {
    try {
      await emailSectionSchema.validate(values, { abortEarly: false });
      setEmailErrors({ to: "", cc: "", bcc: "", subject: "" });
      return true;
    } catch (err) {
      const newErrors = {};
      err.inner.forEach((e) => {
        newErrors[e.path] = e.message;
      });
      setEmailErrors(prev => ({ ...prev, ...newErrors }));
      return false;
    }
  };

  return {
    emailErrors,
    validateEmailField,
    validateAllEmailFields,
  };
};
