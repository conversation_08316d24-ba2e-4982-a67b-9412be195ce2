import React from 'react';
import './ReportStyle.css';
import { getAssetPath } from '../../utils/assetUtils';
import PastDueTabs from '../past-due-invoice/PastDueTabs';
import InvoiceTabs from '../invoice/InvoiceTab';

/**
 * PageContainer - A reusable container component for page layouts
 *
 * @param {Object} props - Component props
 * @param {string} props.title - The title of the page
 * @param {React.ReactNode} props.children - The content to render inside the container
 * @param {string} props.titleIcon - Optional path to the icon image to display next to the title
 * @param {boolean} props.fullWidth - Whether the container should take full width
 * @param {string} props.className - Additional CSS classes to apply to the container
 * @returns {JSX.Element} - The PageContainer component
 */
const PageContainer = ({
  title,
  children,
  titleIcon = 'assets/images/Knowledge_Ceter_White.svg',
  fullWidth = false,
  className = '',
  activeTab,
  onTabChange,
  showInvoiceTabs = false,
  showPastDueTabs = false, // <-- new prop
  tabsDisabled = false,
  // new props for tab hover tooltip
  tabHoverText = '',
  setTabHoverText,
  tabTooltipMap = {}
}) => {
  return (
    
    <div className="main_content_iner">
      <div className="container-fluid p-0">
        <div className="row justify-content-center">
          <div className={`${fullWidth ? 'col-12' : 'col-lg-12'}`}>
            <div className={`white_card card_height_100 mb_30 ${className}`}>
              <div className="white_card_header">
                <div className="box_header m-0 new_report_header">
                  <div className="title_img">
                    {titleIcon && (
                      <img
                        src={getAssetPath(titleIcon)}
                        className="page-title-img"
                        alt=""
                      />
                    )}
                    <h4 className="text-white">{title}</h4>
                  </div>
                  {showPastDueTabs && (
                    <PastDueTabs activeTab={activeTab} onTabChange={onTabChange} tabsDisabled={tabsDisabled} />
                  )}
                  {showInvoiceTabs && (
                    <>
                    <InvoiceTabs
                      activeTab={activeTab}
                      onTabChange={onTabChange}
                      tabHoverText={tabHoverText}
                      setTabHoverText={setTabHoverText}
                      tabTooltipMap={tabTooltipMap}
                    />
                    </>
                  )}
                </div>
              </div>
              <div className="invoice-card-title">
                    {(tabHoverText || (tabTooltipMap && tabTooltipMap[activeTab])) && (
                      <div className="invoice-tabs-tooltip-wrapper" role="tooltip" aria-live="polite">
                        <div className="tabs-tooltip-inner">
                          <span className="tabs-tooltip-icon" aria-hidden="true">
                            <i className="fa fa-info-circle" />
                          </span>
                          <div className="tabs-tooltip-text">
                            {tabHoverText || tabTooltipMap[activeTab]}
                          </div>
                        </div>
                      </div>
                    )}
              </div>
              <div className="white_card_body">
                {children}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
  );
};

export default PageContainer;
