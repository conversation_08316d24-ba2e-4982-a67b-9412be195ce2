import React, { useState } from 'react';

const InvoiceEditSummaryRight = ({
  subtotal,
  discountValue,
  discountType,
  onDiscountValueChange,
  onDiscountTypeChange,
  total,
  totalPartialPaymentReceived,
  totalOutstandingValue,
  outstandingServiceValue,
  outstandingChargeValue
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  // Helper function to format numbers in US format
  const formatUSCurrency = (value) => {
    if (!value || value === '0.00' || value === '0') return '0.00';
    const numValue = parseFloat(value);
    if (isNaN(numValue)) return '0.00';
    return numValue.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };
  const discountTypes = [
    { label: '%', value: 1 },
    { label: '$', value: 2 }
  ];
  return (
    <div className="bg-white p-3 rounded border summary-right" style={{ minWidth: 320 }}>
      <input type="hidden" name="total_amount" value={total} />
      <input type="hidden" name="subtotal" value={subtotal} />
      <input type="hidden" name="line_total_price" value={subtotal} />
      <input type="hidden" name="total_partial_payment_recieved" 
      id="total_partial_payment_recieved" 
      value={totalPartialPaymentReceived || ''} />
      <input type="hidden" name="internal_customer_notes" value="" />
      <input type="hidden" name="note_to_recipient" value="" />
      
      <div className="mb-3 d-flex align-items-center justify-content-between">
        <span>Subtotal</span>
        <div className="input-group" style={{ width: 150 }}>
          <span className="input-group-text">$</span>
          <input
            type="text"
            className="form-control"
            value={subtotal}
            readOnly
            style={{ background: '#f7f7f7' }}
          />
        </div>
      </div>
      <div className="mb-3 d-flex align-items-center justify-content-between">
        <span>Discount</span>
        <div className="input-group" style={{ width: 150 }}>
          <input
            type="number"
            className="form-control other_discountinput"
            value={discountValue}
            onChange={e => {
              let value = e.target.value;
              // If discountType is percentage (either '1' or '%'), apply restriction
              if (discountType === '%' || discountType === 1 || discountType === '1') {
                const parsed = parseFloat(value);
                if (value === '' || isNaN(parsed)) {
                  value = '';
                } else if (parsed > 100) {
                  value = 100;
                } else if (parsed < 0) {
                  value = '';
                }
              }
              onDiscountValueChange({ target: { value } });
            }}
            min="0"
            max="100"
            step="0.01"
            style={{ background: '#fff' }}
            onKeyDown={e => {
              if (e.key === 'e' || e.key === '+' || e.key === '-') {
                e.preventDefault();
              }
            }}
          />
          <select
            className="form-select right-summary-discount"
            value={discountType}
            onChange={e => {
              const selectedValue = e.target.value;
              onDiscountTypeChange({ target: { value: parseInt(selectedValue) } });
            }}
            style={{ maxWidth: 60 }}
          >
            <option value="1">%</option>
            <option value="2">$</option>
          </select>
        </div>
      </div>
      <div className="mb-2 d-flex align-items-center justify-content-between">
        <strong>Invoice Total</strong>
        <strong style={{ fontSize: 20 }}>
          ${formatUSCurrency(total)}
        </strong>
      </div>
      
      {/* Payment Total Section - Only show if there's a payment amount */}
      {totalPartialPaymentReceived && parseFloat(totalPartialPaymentReceived) > 0 && (
        <div className="mb-2 d-flex align-items-center justify-content-between" 
             style={{ 
               border: '2px solid #dc3545', 
               borderRadius: '4px', 
               padding: '8px',
               backgroundColor: '#fff5f5'
             }}>
          <strong style={{ color: '#dc3545' }}>Payment Total</strong>
          <strong style={{ fontSize: 18, color: '#dc3545' }}>
            ${formatUSCurrency(totalPartialPaymentReceived)}
          </strong>
        </div>
      )}
      
      {/* Balance Amount Section - Only show if there's a payment amount */}
      {totalPartialPaymentReceived && parseFloat(totalPartialPaymentReceived) > 0 && (
        <div className="mb-2 d-flex align-items-center justify-content-between" style={{ position: 'relative' }}>
          <div className="d-flex align-items-center">
            <span style={{ marginRight: '5px' }}>Balance amount</span>
            <i 
              className="fas fa-info-circle" 
              style={{ color: '#007bff', fontSize: '14px', cursor: 'pointer' }}
              onMouseEnter={() => setShowTooltip(true)}
              onMouseLeave={() => setShowTooltip(false)}
            ></i>
          </div>
          <strong style={{ fontSize: 18 }}>
            ${formatUSCurrency(totalOutstandingValue)}
          </strong>
          
          {/* Tooltip */}
          {showTooltip && (
            <div style={{
              position: 'absolute',
              bottom: '100%',
              left: '50%',
              transform: 'translateX(-50%)',
              backgroundColor: '#007bff',
              color: 'white',
              padding: '8px 12px',
              borderRadius: '6px',
              fontSize: '12px',
              whiteSpace: 'nowrap',
              zIndex: 1000,
              marginBottom: '5px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
            }}>
              Outstanding service ${formatUSCurrency(outstandingServiceValue)} & Outstanding charge ${formatUSCurrency(outstandingChargeValue)}
              <div style={{
                position: 'absolute',
                top: '100%',
                left: '50%',
                transform: 'translateX(-50%)',
                width: 0,
                height: 0,
                borderLeft: '5px solid transparent',
                borderRight: '5px solid transparent',
                borderTop: '5px solid #007bff'
              }}></div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default InvoiceEditSummaryRight; 