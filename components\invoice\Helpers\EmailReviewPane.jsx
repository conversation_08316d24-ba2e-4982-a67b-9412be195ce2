import React, { useEffect } from 'react';
import RichTextEditor from "../RichTextEditorTiny";

const EmailReviewPane = ({
  emailTo, setEmailTo,
  cc, setCc,
  bcc, setBcc,
  subject, setSubject,
  emailBody, setEmailBody,
  emailErrors,
  onEmailFieldChange 
}) => {

  // Trigger validation on mount if values exist
  useEffect(() => {
    if (emailTo) onEmailFieldChange?.('to', emailTo);
    if (cc) onEmailFieldChange?.('cc', cc);
    if (bcc) onEmailFieldChange?.('bcc', bcc);
    if (subject) onEmailFieldChange?.('subject', subject);
  }, []); // Important

  return (
    <>
      <div className="row">
        <div className="col-md-4">
          <label>To:*</label>
          <input
            type="text"
            className={`form-control ${emailErrors.to ? "is-invalid" : ""}`}
            value={emailTo}
            onChange={e => {
              setEmailTo(e.target.value);
              onEmailFieldChange?.('to', e.target.value);
            }}
          />
          <div className="invalid-feedback d-block">{emailErrors.to || "\u00A0"}</div>
        </div>
        <div className="col-md-4">
          <label>CC:</label>
          <input
            type="text"
            className={`form-control ${emailErrors.cc ? "is-invalid" : ""}`}
            value={cc}
            onChange={e => {
              setCc(e.target.value);
              onEmailFieldChange?.('cc', e.target.value);
            }}
          />
          <div className="invalid-feedback d-block">{emailErrors.cc || "\u00A0"}</div>
        </div>
        <div className="col-md-4">
          <label>Bcc:</label>
          <input
            type="text"
            className={`form-control ${emailErrors.bcc ? "is-invalid" : ""}`}
            value={bcc}
            onChange={e => {
              setBcc(e.target.value);
              onEmailFieldChange?.('bcc', e.target.value);
            }}
          />
          <div className="invalid-feedback d-block">{emailErrors.bcc || "\u00A0"}</div>
        </div>
      </div>

      <div className="form-group mt-3">
        <label>Subject:*</label>
        <input
          type="text"
          className={`form-control ${emailErrors.subject ? "is-invalid" : ""}`}
          value={subject}
          onChange={e => {
            setSubject(e.target.value);
            onEmailFieldChange?.('subject', e.target.value);
          }}
        />
        <div className="invalid-feedback d-block">{emailErrors.subject || "\u00A0"}</div>
      </div>

      <div className="form-group mt-3">
        <label>Email Body</label>
        <RichTextEditor value={emailBody} onChange={setEmailBody} />
        <div className="text-muted mt-1">Use up to 3000 characters</div>
      </div>
    </>
  );
};

export default EmailReviewPane;
