.audit-logs-multi-section {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.main-heading {
  margin-bottom: 30px;
  color: #333;
  font-weight: 600;
  font-size: 24px;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

.audit-section {
  margin-bottom: 40px;
}

.section-heading {
  margin-bottom: 20px;
  color: #444;
  font-weight: 600;
  font-size: 18px;
  border-left: 4px solid #6c63ff;
  padding-left: 10px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6c63ff;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  padding: 15px;
  background-color: #fff3f3;
  color: #d32f2f;
  border-radius: 8px;
  border-left: 4px solid #d32f2f;
  margin-bottom: 20px;
}

.no-data-message {
  padding: 20px;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 8px;
  text-align: center;
  font-style: italic;
}

.table-responsive {
  overflow-x: auto;
  margin-bottom: 20px;
}

.audit-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.audit-table th,
.audit-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.audit-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #444;
  position: sticky;
  top: 0;
  z-index: 10;
}

.audit-table tr:hover {
  background-color: #f5f5f5;
}

.audit-table tr:last-child td {
  border-bottom: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .audit-logs-multi-section {
    padding: 15px;
  }

  .audit-table th,
  .audit-table td {
    padding: 8px 10px;
    font-size: 13px;
  }

  .section-heading {
    font-size: 16px;
  }
}
