// Past Due Tabs
import React, { useState} from "react";
import '../common/ReportStyle.css';

const pastDueTabs = [
  { label: "15 Days", value: "15 Days" },
  { label: "21 Days", value: "21 Days" },
  { label: "25 Days", value: "25 Days" },
  { label: "30 Days", value: "30 Days" },
  { label: "30+", value: "30+" },
];

const PastDueTabs = ({ activeTab, onTabChange, tabsDisabled = false }) => {
  
  return (
    <div className="past-due-tabs-bar">
      <div className="past-due-tabs-list">
        {pastDueTabs.map(tab => (
          <button
            key={tab.value}
            className={`tab-btn${activeTab === tab.value ? " active" : ""}${tabsDisabled ? " disabled" : ""}`}
            onClick={() => {
              if (!tabsDisabled) {
                onTabChange(tab.value);
              }
            }}
            disabled={tabsDisabled}
            style={{ 
              position: 'relative',
              opacity: tabsDisabled ? 0.6 : 1,
              cursor: tabsDisabled ? 'not-allowed' : 'pointer'
            }}
          >
            {tab.label}
            {tabsDisabled && activeTab === tab.value && (
              <span style={{ 
                position: 'absolute', 
                top: '50%', 
                right: '8px', 
                transform: 'translateY(-50%)',
                fontSize: '10px'
              }}>
                ⏳
              </span>
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default PastDueTabs;